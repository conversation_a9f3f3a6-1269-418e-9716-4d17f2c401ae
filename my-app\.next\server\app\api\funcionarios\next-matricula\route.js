(()=>{var a={};a.id=7025,a.ids=[7025],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:a=>{"use strict";a.exports=require("querystring")},12412:a=>{"use strict";a.exports=require("assert")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45231:(a,b,c)=>{"use strict";c.d(b,{N:()=>h});var d=c(13581),e=c(96330);let f=globalThis.prisma??new e.PrismaClient;var g=c(85663);let h={providers:[(0,d.A)({name:"credentials",credentials:{usuario:{label:"Usu\xe1rio",type:"text"},senha:{label:"Senha",type:"password"}},async authorize(a){if(!a?.usuario||!a?.senha)return null;try{let b=await f.usuario.findUnique({where:{usuario:a.usuario},include:{funcionario:!0}});if(!b||!b.ativo||!await (0,g.UD)(a.senha,b.senhaHash))return null;return{id:b.id.toString(),name:b.nome,email:b.email||"",role:b.nivelAcesso,usuario:b.usuario}}catch(a){return console.error("Erro na autentica\xe7\xe3o:",a),null}}})],session:{strategy:"jwt",maxAge:28800},jwt:{secret:process.env.NEXTAUTH_SECRET,maxAge:28800},callbacks:{jwt:async({token:a,user:b})=>(b&&(a.role=b.role,a.usuario=b.usuario),a),session:async({session:a,token:b})=>(b&&(a.user.id=b.sub,a.user.role=b.role,a.user.usuario=b.usuario),a)},pages:{signIn:"/login",error:"/login?error=auth_error"},debug:!1}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},85698:(a,b,c)=>{"use strict";c.d(b,{Lq:()=>h});var d=c(32190);let e={cors:{origin:[process.env.NEXTAUTH_URL||"https://rlponto.com"],methods:["GET","POST","PUT","DELETE","OPTIONS"],allowedHeaders:["Content-Type","Authorization","X-Requested-With","Accept","Origin","X-CSRF-Token"],credentials:!0},csp:{enabled:!0,directives:{"default-src":["'self'"],"script-src":["'self'","'unsafe-inline'","'unsafe-eval'","https://cdn.jsdelivr.net","https://storage.googleapis.com"],"style-src":["'self'","'unsafe-inline'","https://fonts.googleapis.com"],"font-src":["'self'","https://fonts.gstatic.com"],"img-src":["'self'","data:","blob:","https:"],"media-src":["'self'","blob:"],"connect-src":["'self'","https://viacep.com.br","https://cdn.jsdelivr.net","https://storage.googleapis.com"],"worker-src":["'self'","blob:"],"frame-ancestors":["'none'"],"base-uri":["'self'"],"form-action":["'self'"],"upgrade-insecure-requests":[]}},hsts:{enabled:!0,maxAge:31536e3,includeSubDomains:!0,preload:!0}};class f{constructor(a){this.config={...e,...a,cors:{...e.cors,...a?.cors},csp:{...e.csp,...a?.csp},hsts:{...e.hsts,...a?.hsts}}}apply(a,b){return this.config.cors&&this.applyCorsHeaders(a,b),this.applySecurityHeaders(a),this.config.csp?.enabled&&this.applyCspHeaders(a),this.config.hsts?.enabled&&this.applyHstsHeaders(a),a}applyCorsHeaders(a,b){let{cors:c}=this.config;if(c){if(b&&c.origin){let d=Array.isArray(c.origin)?c.origin:[c.origin];(!0===c.origin||d.includes(b))&&a.headers.set("Access-Control-Allow-Origin",b)}else!0===c.origin&&a.headers.set("Access-Control-Allow-Origin","*");c.methods&&a.headers.set("Access-Control-Allow-Methods",c.methods.join(", ")),c.allowedHeaders&&a.headers.set("Access-Control-Allow-Headers",c.allowedHeaders.join(", ")),c.credentials&&a.headers.set("Access-Control-Allow-Credentials","true"),a.headers.set("Access-Control-Max-Age","86400")}}applySecurityHeaders(a){a.headers.set("X-Content-Type-Options","nosniff"),a.headers.set("X-Frame-Options","DENY"),a.headers.set("X-XSS-Protection","1; mode=block"),a.headers.set("Referrer-Policy","strict-origin-when-cross-origin"),a.headers.set("Permissions-Policy","camera=(self), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()"),a.headers.get("content-type")?.includes("application/json")&&(a.headers.set("Cache-Control","no-store, no-cache, must-revalidate, proxy-revalidate"),a.headers.set("Pragma","no-cache"),a.headers.set("Expires","0"))}applyCspHeaders(a){let{csp:b}=this.config;if(!b?.directives)return;let c=Object.entries(b.directives).map(([a,b])=>0===b.length?a:`${a} ${b.join(" ")}`).join("; ");a.headers.set("Content-Security-Policy",c)}applyHstsHeaders(a){let{hsts:b}=this.config;if(!b)return;let c=`max-age=${b.maxAge}`;b.includeSubDomains&&(c+="; includeSubDomains"),b.preload&&(c+="; preload"),a.headers.set("Strict-Transport-Security",c)}isOriginAllowed(a){let{cors:b}=this.config;return!!b?.origin&&(!0===b.origin||(Array.isArray(b.origin)?b.origin:[b.origin]).includes(a))}createPreflightResponse(a){let b=new d.NextResponse(null,{status:204});return this.apply(b,a)}}let g=new f;function h(a,b,c){return(c?new f(c):g).apply(a,b)}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92391:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>N,patchFetch:()=>M,routeModule:()=>I,serverHooks:()=>L,workAsyncStorage:()=>J,workUnitAsyncStorage:()=>K});var d={};c.r(d),c.d(d,{GET:()=>E,OPTIONS:()=>H,POST:()=>F,PUT:()=>G,consultarMatriculasExistentes:()=>z,gerarProximaMatricula:()=>B,isMatriculaDisponivel:()=>D,reservarMatricula:()=>C});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(19854),w=c(45231),x=c(85698);let y=new Set;async function z(){try{let a=new Set;(global.funcionariosCadastrados||[]).forEach(b=>{b.dadosProfissionais?.matricula&&a.add(b.dadosProfissionais.matricula)});try{let b=await fetch("http://localhost:3000/api/funcionarios",{method:"GET",headers:{"Content-Type":"application/json"}});if(b.ok){let c=await b.json();c.funcionarios&&Array.isArray(c.funcionarios)&&c.funcionarios.forEach(b=>{b.dadosProfissionais?.matricula&&a.add(b.dadosProfissionais.matricula)})}}catch(a){console.log("Erro ao consultar API (usando dados locais):",a)}return console.log("Matr\xedculas utilizadas encontradas:",Array.from(a).sort()),console.log("Total de matr\xedculas em uso:",a.size),a}catch(a){return console.error("Erro ao consultar matr\xedculas existentes:",a),new Set}}async function A(){y=await z()}async function B(){if(await A(),0===y.size)return console.log("Nenhuma matr\xedcula encontrada. Iniciando com 0001"),"0001";let a=0;for(let b of y){let c=parseInt(b,10);!isNaN(c)&&c>a&&(a=c)}let b=(a+1).toString().padStart(4,"0");return console.log(`Matr\xedculas existentes: ${Array.from(y).sort().join(", ")}`),console.log(`Maior matr\xedcula encontrada: ${a.toString().padStart(4,"0")}`),console.log(`Pr\xf3xima matr\xedcula gerada: ${b}`),b}async function C(a){return await A(),!y.has(a)&&(y.add(a),console.log(`Matr\xedcula ${a} reservada com sucesso`),!0)}async function D(a){return await A(),!y.has(a)}async function E(a){try{if(!await (0,v.getServerSession)(w.N))return u.NextResponse.json({success:!1,error:"N\xe3o autorizado"},{status:401});let b=await B();return function(a,b,c){let d=u.NextResponse.json(a,b),e=c?.headers.get("origin")||void 0;return(0,x.Lq)(d,e)}({success:!0,matricula:b,formato:"Matr\xedcula gerada automaticamente no formato 0001, 0002, etc.",regras:{sequencial:!0,unica:!0,naoReutilizada:!0,formato:"4 d\xedgitos com zeros \xe0 esquerda"}},void 0,a)}catch(a){return console.error("Erro ao gerar pr\xf3xima matr\xedcula:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor ao gerar matr\xedcula"},{status:500})}}async function F(a){try{let{action:b,matricula:c}=await a.json();if("reserve"===b){if(!c)return u.NextResponse.json({success:!1,error:"Matr\xedcula \xe9 obrigat\xf3ria para reserva"},{status:400});if(!await C(c))return u.NextResponse.json({success:!1,error:"Matr\xedcula j\xe1 est\xe1 em uso",matricula:c},{status:409});return u.NextResponse.json({success:!0,matricula:c,message:"Matr\xedcula reservada com sucesso",action:"reserved"})}if("generate-and-reserve"!==b)return u.NextResponse.json({success:!1,error:'A\xe7\xe3o inv\xe1lida. Use "reserve" ou "generate-and-reserve"'},{status:400});{let a=await B();if(!await C(a))return u.NextResponse.json({success:!1,error:"Erro interno: matr\xedcula gerada j\xe1 estava em uso"},{status:500});return u.NextResponse.json({success:!0,matricula:a,message:"Matr\xedcula gerada e reservada automaticamente",action:"generated-and-reserved"})}}catch(a){return console.error("Erro ao processar matr\xedcula:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}async function G(a){try{if(!await (0,v.getServerSession)(w.N))return u.NextResponse.json({success:!1,error:"N\xe3o autorizado"},{status:401});let{matricula:b}=await a.json();if(!b)return u.NextResponse.json({success:!1,error:"Matr\xedcula \xe9 obrigat\xf3ria para verifica\xe7\xe3o"},{status:400});let c=await D(b);return u.NextResponse.json({success:!0,matricula:b,disponivel:c,message:c?"Matr\xedcula dispon\xedvel":"Matr\xedcula j\xe1 est\xe1 em uso"})}catch(a){return console.error("Erro ao verificar matr\xedcula:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}async function H(a){let b=a.headers.get("origin"),c=new u.NextResponse(null,{status:204});return(0,x.Lq)(c,b||void 0)}global.funcionariosCadastrados=[];let I=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/funcionarios/next-matricula/route",pathname:"/api/funcionarios/next-matricula",filename:"route",bundlePath:"app/api/funcionarios/next-matricula/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\funcionarios\\next-matricula\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:J,workUnitAsyncStorage:K,serverHooks:L}=I;function M(){return(0,g.patchFetch)({workAsyncStorage:J,workUnitAsyncStorage:K})}async function N(a,b,c){var d;let e="/api/funcionarios/next-matricula/route";"/index"===e&&(e="/");let g=await I.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=g,D=(0,j.normalizeAppPath)(e),E=!!(y.dynamicRoutes[D]||y.routes[C]);if(E&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[D];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let F=null;!E||I.isDev||x||(F="/index"===(F=C)?"/":F);let G=!0===I.isDev||!E,H=E&&!G,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:G,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:H,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>I.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>I.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&B&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!E)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await I.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:H,isOnDemandRevalidate:A})},z),b}},l=await I.handleResponse({req:a,nextConfig:w,cacheKey:F,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!E)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&E||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await I.onRequestError(a,b,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:H,isOnDemandRevalidate:A})}),E)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},94735:a=>{"use strict";a.exports=require("events")},96330:a=>{"use strict";a.exports=require("@prisma/client")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,6055,3916],()=>b(b.s=92391));module.exports=c})();