(()=>{var a={};a.id=8823,a.ids=[8823],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2091:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>n,metadata:()=>m});var d=c(37413),e=c(61120),f=c(81146),g=c(65353),h=c(75243),i=c(41382),j=c(61348),k=c(4536),l=c.n(k);let m={title:"Funcion\xe1rios - RLPONTO",description:"Gest\xe3o de funcion\xe1rios do sistema"};async function n({searchParams:a}){let b=await a;return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-600 rounded-lg",children:(0,d.jsx)(i.A,{className:"h-8 w-8 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Funcion\xe1rios"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Gerencie os funcion\xe1rios da empresa"})]})]}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsx)(l(),{href:"/funcionarios/desligados",children:(0,d.jsx)(h.$n,{variant:"outline",children:"Desligados"})}),(0,d.jsx)(l(),{href:"/funcionarios/novo",children:(0,d.jsxs)(h.$n,{variant:"primary",children:[(0,d.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Novo Funcion\xe1rio"]})})]})]}),(0,d.jsx)(g.FuncionariosFilters,{}),(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(o,{}),children:(0,d.jsx)(f.FuncionariosList,{searchParams:b})})]})})})}function o(){return(0,d.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-4 p-4 border rounded-lg",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full animate-pulse"}),(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/4"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded animate-pulse w-1/3"})]}),(0,d.jsx)("div",{className:"w-20 h-8 bg-gray-200 rounded animate-pulse"})]},b))})})})}},3021:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,9636)),Promise.resolve().then(c.bind(c,55991)),Promise.resolve().then(c.bind(c,98316))},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9636:(a,b,c)=>{"use strict";c.d(b,{FuncionariosFilters:()=>j});var d=c(60687),e=c(43210),f=c(16189),g=c(42613),h=c(80462);let i=(0,c(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function j(){let a=(0,f.useRouter)(),b=(0,f.useSearchParams)(),[c,j]=(0,e.useState)(b.get("search")||""),[k,l]=(0,e.useState)(b.get("setor")||""),[m,n]=(0,e.useState)(b.get("status")||""),o=function(a,b){let[c,d]=(0,e.useState)(a);return c}(c,0),p=(0,e.useCallback)(()=>{let b=new URLSearchParams;o&&b.set("search",o),k&&b.set("setor",k),m&&b.set("status",m),a.push(`/funcionarios?${b.toString()}`)},[o,k,m,a]),q=(0,e.useCallback)(()=>{j(""),l(""),n(""),a.push("/funcionarios")},[a]),r=(0,e.useMemo)(()=>c||k||m,[c,k,m]);return(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(g.WI,{className:"absolute left-3 top-1/2 -translate-y-1/2"}),(0,d.jsx)(g.pd,{placeholder:"Buscar por nome, CPF ou matr\xedcula...",value:c,onChange:a=>j(a.target.value),className:"pl-10"})]})}),(0,d.jsx)("div",{className:"w-48",children:(0,d.jsxs)("select",{value:k,onChange:a=>l(a.target.value),"data-format":"preserve",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900",style:{color:k?"#111827":"#6B7280"},children:[(0,d.jsx)("option",{value:"",style:{color:"#6B7280"},children:"Todos os setores"}),(0,d.jsx)("option",{value:"ADMINISTRA\xc7\xc3O",children:"ADMINISTRA\xc7\xc3O"}),(0,d.jsx)("option",{value:"PRODU\xc7\xc3O",children:"PRODU\xc7\xc3O"}),(0,d.jsx)("option",{value:"VENDAS",children:"VENDAS"}),(0,d.jsx)("option",{value:"RECURSOS HUMANOS",children:"RECURSOS HUMANOS"}),(0,d.jsx)("option",{value:"TECNOLOGIA",children:"TECNOLOGIA"}),(0,d.jsx)("option",{value:"FINANCEIRO",children:"FINANCEIRO"}),(0,d.jsx)("option",{value:"MARKETING",children:"MARKETING"}),(0,d.jsx)("option",{value:"OPERA\xc7\xd5ES",children:"OPERA\xc7\xd5ES"})]})}),(0,d.jsx)("div",{className:"w-40",children:(0,d.jsxs)("select",{value:m,onChange:a=>n(a.target.value),"data-format":"preserve",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900",style:{color:m?"#111827":"#6B7280"},children:[(0,d.jsx)("option",{value:"",style:{color:"#6B7280"},children:"Todos os status"}),(0,d.jsx)("option",{value:"ATIVO",children:"ATIVO"}),(0,d.jsx)("option",{value:"INATIVO",children:"INATIVO"}),(0,d.jsx)("option",{value:"F\xc9RIAS",children:"F\xc9RIAS"}),(0,d.jsx)("option",{value:"LICEN\xc7A",children:"LICEN\xc7A"})]})}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(g.$n,{onClick:p,variant:"primary",children:[(0,d.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Filtrar"]}),r&&(0,d.jsxs)(g.$n,{onClick:q,variant:"outline",children:[(0,d.jsx)(i,{className:"mr-2 h-4 w-4"}),"Limpar"]})]})]}),r&&(0,d.jsxs)("div",{className:"mt-4 flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Filtros ativos:"}),c&&(0,d.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:["Busca: ",c]}),k&&(0,d.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:["Setor: ",k]}),m&&(0,d.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:["Status: ",m]})]})]})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41382:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},55757:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,65353)),Promise.resolve().then(c.bind(c,81146)),Promise.resolve().then(c.bind(c,58570))},55991:(a,b,c)=>{"use strict";c.d(b,{FuncionariosList:()=>n});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(42613),i=c(62688);let j=(0,i.A)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var k=c(41312);let l=(0,i.A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var m=c(13861);function n({searchParams:a}){let[b,c]=(0,e.useState)([]),[f,i]=(0,e.useState)(!0),[n,p]=(0,e.useState)(null),q=async()=>{try{i(!0);let b=new URLSearchParams;a.search&&b.set("search",a.search),a.setor&&b.set("setor",a.setor),a.status&&b.set("status",a.status),a.page&&b.set("page",a.page);let d=await fetch(`/api/funcionarios?${b.toString()}`);if(!d.ok)throw Error("Erro ao carregar funcion\xe1rios");let e=await d.json();c(e.funcionarios||[])}catch(a){p(a instanceof Error?a.message:"Erro desconhecido")}finally{i(!1)}};return f?(0,d.jsx)(o,{}):n?(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-8 text-center",children:[(0,d.jsx)("div",{className:"text-red-500 mb-4",children:(0,d.jsx)(j,{className:"h-12 w-12 mx-auto"})}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Erro ao carregar funcion\xe1rios"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:n}),(0,d.jsx)(h.$n,{onClick:q,variant:"primary",children:"Tentar novamente"})]}):0===b.length?(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-8 text-center",children:[(0,d.jsx)("div",{className:"text-gray-400 mb-4",children:(0,d.jsx)(k.A,{className:"h-12 w-12 mx-auto"})}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Nenhum funcion\xe1rio encontrado"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:a.search||a.setor||a.status?"Tente ajustar os filtros de busca.":"Comece cadastrando o primeiro funcion\xe1rio."}),(0,d.jsx)(g(),{href:"/funcionarios/novo",children:(0,d.jsx)(h.$n,{variant:"primary",children:"Cadastrar Funcion\xe1rio"})})]}):(0,d.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(l,{className:"h-5 w-5 text-green-600"}),(0,d.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[b.length," funcion\xe1rio",1!==b.length?"s":""," encontrado",1!==b.length?"s":""]})]}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:(0,d.jsx)(h.$n,{variant:"outline",size:"sm",children:"Exportar Lista"})})]}),(0,d.jsx)("div",{className:"space-y-4",children:b.map(a=>(0,d.jsxs)("div",{className:"flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:(0,d.jsx)(k.A,{className:"h-6 w-6 text-blue-600"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-medium text-gray-900",children:a.nomeCompleto}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[a.dadosProfissionais.cargo," - ",a.dadosProfissionais.setor]}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:["Matr\xedcula: ",a.dadosProfissionais.matricula]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${"ativo"===a.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:a.status}),(0,d.jsx)(g(),{href:`/funcionarios/${a.id}`,children:(0,d.jsx)(h.$n,{variant:"outline",size:"sm",children:(0,d.jsx)(m.A,{className:"h-4 w-4"})})})]})]},a.id))}),(0,d.jsx)("div",{className:"mt-6 flex items-center justify-between",children:(0,d.jsxs)("div",{className:"text-sm text-gray-700",children:["Mostrando ",b.length," de ",b.length," funcion\xe1rios"]})})]})})}function o(){return(0,d.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-4 p-4 border rounded-lg",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full animate-pulse"}),(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/4"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded animate-pulse w-1/3"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded animate-pulse w-1/5"})]}),(0,d.jsx)("div",{className:"w-20 h-8 bg-gray-200 rounded animate-pulse"}),(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"})]},b))})})})}},61348:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65353:(a,b,c)=>{"use strict";c.d(b,{FuncionariosFilters:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call FuncionariosFilters() from the server but FuncionariosFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\funcionarios-filters.tsx","FuncionariosFilters")},75086:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["funcionarios",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,2091)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\funcionarios\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,57675)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,85389)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\funcionarios\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/funcionarios/page",pathname:"/funcionarios",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/funcionarios/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},80462:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81146:(a,b,c)=>{"use strict";c.d(b,{FuncionariosList:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call FuncionariosList() from the server but FuncionariosList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\funcionarios-list.tsx","FuncionariosList")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,3327,4556,1684,2121],()=>b(b.s=75086));module.exports=c})();