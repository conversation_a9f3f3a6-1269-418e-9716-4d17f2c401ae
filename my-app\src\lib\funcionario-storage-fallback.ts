/**
 * Sistema de Fallback para Funcionários
 * 
 * Usado quando o banco de dados não está disponível
 * Mantém compatibilidade com a interface do storage
 */

import { Funcionario } from '@/types';
import { logger } from '@/lib/logger';

// Map em memória para fallback
const funcionariosMap = new Map<string, Funcionario>();

// Dados iniciais para demonstração
const funcionariosIniciais: Funcionario[] = [
  {
    id: '1',
    nomeCompleto: '<PERSON>',
    cpf: '12345678900',
    email: '<EMAIL>',
    telefone: '(11) 99999-9999',
    status: 'ativo',
    dadosPessoais: {
      nomeCompleto: '<PERSON>',
      cpf: '12345678900',
      rg: '123456789',
      email: '<EMAIL>',
      telefone: '(11) 99999-9999',
      dataNascimento: '1990-01-15',
      estadoCivil: 'solt<PERSON>',
      endereco: {
        cep: '01234-567',
        logradouro: '<PERSON><PERSON>, 123',
        numero: '123',
        bairro: 'Centro',
        cidade: 'São Paulo',
        uf: 'SP'
      }
    },
    dadosProfissionais: {
      matricula: 'EMP001',
      cargo: 'Desenvolvedor',
      setor: 'TI',
      salario: 5000,
      dataAdmissao: '2024-01-15',
      horarioTrabalho: {
        entrada: '08:00',
        saida: '17:00',
        intervaloInicio: '12:00',
        intervaloFim: '13:00'
      }
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Inicializar com dados de exemplo
funcionariosIniciais.forEach(funcionario => {
  funcionariosMap.set(funcionario.id, funcionario);
});

/**
 * Sistema de Storage Fallback para Funcionários
 */
export class FuncionarioStorageFallback {
  private static instance: FuncionarioStorageFallback;

  static getInstance(): FuncionarioStorageFallback {
    if (!FuncionarioStorageFallback.instance) {
      FuncionarioStorageFallback.instance = new FuncionarioStorageFallback();
    }
    return FuncionarioStorageFallback.instance;
  }

  /**
   * Listar todos os funcionários
   */
  async getAll(): Promise<Funcionario[]> {
    try {
      logger.info('Listando funcionários (fallback)', { count: funcionariosMap.size });
      return Array.from(funcionariosMap.values());
    } catch (error) {
      logger.error('Erro ao listar funcionários (fallback)', { error: error instanceof Error ? error.message : String(error) });
      throw new Error('Erro ao carregar funcionários');
    }
  }

  /**
   * Alias para getAll (compatibilidade com API)
   */
  async findAll(): Promise<Funcionario[]> {
    return this.getAll();
  }

  /**
   * Buscar funcionário por ID
   */
  async getById(id: string): Promise<Funcionario | null> {
    try {
      const funcionario = funcionariosMap.get(id) || null;
      logger.info('Buscando funcionário por ID (fallback)', { id, found: !!funcionario });
      return funcionario;
    } catch (error) {
      logger.error('Erro ao buscar funcionário por ID (fallback)', { error: error instanceof Error ? error.message : String(error), id });
      throw new Error('Erro ao buscar funcionário');
    }
  }

  /**
   * Criar novo funcionário
   */
  async create(data: Omit<Funcionario, 'id' | 'createdAt' | 'updatedAt'>): Promise<Funcionario> {
    try {
      // Gerar ID único
      const id = `func_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Gerar matrícula se não fornecida
      const matricula = data.matricula || this.generateMatricula();

      const funcionario: Funcionario = {
        ...data,
        id,
        matricula,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      funcionariosMap.set(id, funcionario);
      
      logger.info('Funcionário criado (fallback)', { id, nome: funcionario.nome, matricula });
      return funcionario;
    } catch (error) {
      logger.error('Erro ao criar funcionário (fallback)', { error: error instanceof Error ? error.message : String(error) });
      throw new Error('Erro ao criar funcionário');
    }
  }

  /**
   * Atualizar funcionário
   */
  async update(id: string, data: Partial<Funcionario>): Promise<boolean> {
    try {
      const funcionario = funcionariosMap.get(id);
      if (!funcionario) {
        return false;
      }

      const funcionarioAtualizado = {
        ...funcionario,
        ...data,
        id, // Manter ID original
        updatedAt: new Date().toISOString()
      };

      funcionariosMap.set(id, funcionarioAtualizado);
      
      logger.info('Funcionário atualizado (fallback)', { id, nome: funcionarioAtualizado.nome });
      return true;
    } catch (error) {
      logger.error('Erro ao atualizar funcionário (fallback)', { error: error instanceof Error ? error.message : String(error), id });
      throw new Error('Erro ao atualizar funcionário');
    }
  }

  /**
   * Deletar funcionário
   */
  async delete(id: string): Promise<boolean> {
    try {
      const funcionario = funcionariosMap.get(id);
      if (!funcionario) {
        return false;
      }

      funcionariosMap.delete(id);
      
      logger.info('Funcionário removido (fallback)', { id, nome: funcionario.nome });
      return true;
    } catch (error) {
      logger.error('Erro ao remover funcionário (fallback)', { error: error instanceof Error ? error.message : String(error), id });
      throw new Error('Erro ao remover funcionário');
    }
  }

  /**
   * Buscar funcionários por termo
   */
  async search(term: string): Promise<Funcionario[]> {
    try {
      const termLower = term.toLowerCase();
      const funcionarios = Array.from(funcionariosMap.values()).filter(funcionario =>
        funcionario.nome.toLowerCase().includes(termLower) ||
        funcionario.cpf.includes(term) ||
        funcionario.matricula.toLowerCase().includes(termLower) ||
        funcionario.email.toLowerCase().includes(termLower)
      );

      logger.info('Busca de funcionários (fallback)', { term, found: funcionarios.length });
      return funcionarios;
    } catch (error) {
      logger.error('Erro na busca de funcionários (fallback)', { error: error instanceof Error ? error.message : String(error), term });
      throw new Error('Erro na busca de funcionários');
    }
  }

  /**
   * Gerar matrícula única
   */
  private generateMatricula(): string {
    const existingMatriculas = Array.from(funcionariosMap.values()).map(f => f.matricula);
    let numero = 1;
    let matricula = `EMP${numero.toString().padStart(3, '0')}`;
    
    while (existingMatriculas.includes(matricula)) {
      numero++;
      matricula = `EMP${numero.toString().padStart(3, '0')}`;
    }
    
    return matricula;
  }

  /**
   * Verificar se CPF já existe
   */
  async cpfExists(cpf: string, excludeId?: string): Promise<boolean> {
    const funcionarios = Array.from(funcionariosMap.values());
    return funcionarios.some(f => f.cpf === cpf && f.id !== excludeId);
  }

  /**
   * Verificar se email já existe
   */
  async emailExists(email: string, excludeId?: string): Promise<boolean> {
    const funcionarios = Array.from(funcionariosMap.values());
    return funcionarios.some(f => f.email === email && f.id !== excludeId);
  }

  /**
   * Verificar se matrícula já existe
   */
  async matriculaExists(matricula: string, excludeId?: string): Promise<boolean> {
    const funcionarios = Array.from(funcionariosMap.values());
    return funcionarios.some(f => f.dadosProfissionais.matricula === matricula && f.id !== excludeId);
  }

  /**
   * Buscar funcionário por CPF
   */
  async findByCpf(cpf: string): Promise<Funcionario | null> {
    const funcionarios = Array.from(funcionariosMap.values());
    return funcionarios.find(f => f.cpf === cpf) || null;
  }

  /**
   * Gerar próxima matrícula
   */
  async getNextMatricula(): Promise<string> {
    return this.generateMatricula();
  }

  /**
   * Salvar funcionário
   */
  async save(funcionario: Funcionario): Promise<boolean> {
    try {
      funcionariosMap.set(funcionario.id, funcionario);
      logger.info('Funcionário salvo (fallback)', { id: funcionario.id, nome: funcionario.nomeCompleto });
      return true;
    } catch (error) {
      logger.error('Erro ao salvar funcionário (fallback)', { error: error instanceof Error ? error.message : String(error) });
      return false;
    }
  }
}

// Exportar instância singleton
export const funcionarioStorageFallback = FuncionarioStorageFallback.getInstance();
