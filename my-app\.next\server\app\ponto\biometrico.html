<!DOCTYPE html><!--57groY_ofPMaGR4OOS9UN--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/b293d1867f231925.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-e4553bd4c3ca5db6.js"/><script src="/_next/static/chunks/4bd1b696-602635ee57868870.js" async=""></script><script src="/_next/static/chunks/5964-7bb2b019501ffae5.js" async=""></script><script src="/_next/static/chunks/main-app-9632f9c08f91efed.js" async=""></script><script src="/_next/static/chunks/app/layout-7e63b6183d7635de.js" async=""></script><script src="/_next/static/chunks/6874-d27b54d0b28e3259.js" async=""></script><script src="/_next/static/chunks/4285-3cbfa4a2f71248be.js" async=""></script><script src="/_next/static/chunks/3769-e62c0e969fff9246.js" async=""></script><script src="/_next/static/chunks/app/error-da7a176665770fd1.js" async=""></script><script src="/_next/static/chunks/app/not-found-0d232e224bc38f5f.js" async=""></script><script src="/_next/static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js" async=""></script><script src="/_next/static/chunks/6011-2c5bc7b6ccc9fe2f.js" async=""></script><script src="/_next/static/chunks/app/(dashboard)/ponto/biometrico/page-24ad808f5fc34a25.js" async=""></script><title>Ponto Biométrico - RLPONTO</title><meta name="description" content="Registro de ponto através de biometria"/><meta name="author" content="Your Name"/><meta name="keywords" content="Next.js,React,TypeScript,Tailwind CSS"/><meta name="creator" content="Your Name"/><meta name="robots" content="index, follow"/><meta property="og:title" content="My App"/><meta property="og:description" content="A professional Next.js application"/><meta property="og:url" content="http://************"/><meta property="og:site_name" content="My App"/><meta property="og:locale" content="en_US"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="My App"/><meta name="twitter:description" content="A professional Next.js application"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gray-50"><header class="bg-white shadow-sm border-b border-gray-200"><div class="px-6 py-4"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">RL</span></div><h1 class="text-xl font-bold text-gray-900">RLPONTO</h1></div></div><div class="flex-1 max-w-lg mx-8"><div class="relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search h-4 w-4 text-gray-500 absolute left-3 top-1/2 -translate-y-1/2" aria-hidden="true"><path d="m21 21-4.34-4.34"></path><circle cx="11" cy="11" r="8"></circle></svg><input type="text" placeholder="Buscar funcionários, relatórios..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder:text-gray-600 text-gray-900 font-semibold" value=""/></div></div><div class="flex items-center space-x-4"><button class="relative p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell h-5 w-5" aria-hidden="true"><path d="M10.268 21a2 2 0 0 0 3.464 0"></path><path d="M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"></path></svg><span class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span></button><div class="relative"><button class="flex items-center space-x-2 p-2 text-gray-700 hover:text-gray-900 rounded-lg hover:bg-gray-100"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user h-4 w-4" aria-hidden="true"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg></div><span class="text-sm font-medium">Administrador</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></div></div></div></div></header><div class="flex"><nav class="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen"><div class="p-6"><div class="space-y-1"><div><a class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-50 hover:text-gray-900" href="/dashboard"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house mr-3 h-5 w-5 text-gray-400" aria-hidden="true"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>Dashboard</a></div><div><a class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-50 hover:text-gray-900" href="/funcionarios"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users mr-3 h-5 w-5 text-gray-400" aria-hidden="true"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><path d="M16 3.128a4 4 0 0 1 0 7.744"></path><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><circle cx="9" cy="7" r="4"></circle></svg>Funcionários</a></div><div><a class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors bg-blue-50 text-blue-700 border-r-2 border-blue-700" href="/ponto/biometrico"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock mr-3 h-5 w-5 text-blue-700" aria-hidden="true"><path d="M12 6v6l4 2"></path><circle cx="12" cy="12" r="10"></circle></svg>Ponto</a><div class="ml-8 mt-1 space-y-1"><a class="flex items-center px-3 py-1 text-xs font-medium rounded-md transition-colors bg-blue-100 text-blue-800" href="/ponto/biometrico"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock mr-2 h-3 w-3" aria-hidden="true"><path d="M12 6v6l4 2"></path><circle cx="12" cy="12" r="10"></circle></svg>Ponto Biométrico</a><a class="flex items-center px-3 py-1 text-xs font-medium rounded-md transition-colors text-gray-600 hover:bg-gray-50 hover:text-gray-800" href="/ponto/manual"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-hand mr-2 h-3 w-3" aria-hidden="true"><path d="M18 11V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2"></path><path d="M14 10V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v2"></path><path d="M10 10.5V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2v8"></path><path d="M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15"></path></svg>Ponto Manual</a></div></div><div><a class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-50 hover:text-gray-900" href="/periodo-apuracao"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-column mr-3 h-5 w-5 text-gray-400" aria-hidden="true"><path d="M3 3v16a2 2 0 0 0 2 2h16"></path><path d="M18 17V9"></path><path d="M13 17V5"></path><path d="M8 17v-3"></path></svg>Período de Apuração</a></div><div><a class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-50 hover:text-gray-900" href="/relatorios"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text mr-3 h-5 w-5 text-gray-400" aria-hidden="true"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg>Relatórios</a></div><div><a class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-50 hover:text-gray-900" href="/estatisticas"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up mr-3 h-5 w-5 text-gray-400" aria-hidden="true"><path d="M16 7h6v6"></path><path d="m22 7-8.5 8.5-5-5L2 17"></path></svg>Estatísticas</a></div><div><a class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-50 hover:text-gray-900" href="/administracao"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings mr-3 h-5 w-5 text-gray-400" aria-hidden="true"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>Administração</a></div></div></div></nav><main class="flex-1 overflow-auto"><div class="min-h-screen bg-gray-50"><div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="space-y-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><a href="/dashboard"><button type="button" class="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none border border-gray-400 bg-transparent text-gray-700 hover:bg-gray-50 hover:border-gray-500 h-8 px-3 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left h-4 w-4 mr-2" aria-hidden="true"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg>Voltar</button></a><div class="flex items-center space-x-3"><div class="p-2 bg-blue-600 rounded-lg"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-fingerprint h-8 w-8 text-white" aria-hidden="true"><path d="M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4"></path><path d="M14 13.12c0 2.38 0 6.38-1 8.88"></path><path d="M17.29 21.02c.12-.6.43-2.3.5-3.02"></path><path d="M2 12a10 10 0 0 1 18-6"></path><path d="M2 16h.01"></path><path d="M21.8 16c.2-2 .131-5.354 0-6"></path><path d="M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2"></path><path d="M8.65 22c.21-.66.45-1.32.57-2"></path><path d="M9 6.8a6 6 0 0 1 9 5.2v2"></path></svg></div><div><h1 class="text-3xl font-bold text-gray-900">Ponto Biométrico</h1><p class="text-gray-600">Registre seu ponto usando biometria digital ou facial</p></div></div></div></div><!--$--><div class="bg-white rounded-lg shadow p-6"><div class="animate-pulse"><div class="h-6 bg-gray-200 rounded w-1/4 mb-4"></div><div class="space-y-3"><div class="h-4 bg-gray-200 rounded w-1/2"></div><div class="h-4 bg-gray-200 rounded w-1/3"></div><div class="h-4 bg-gray-200 rounded w-1/4"></div></div></div></div><!--/$--><div class="grid grid-cols-1 lg:grid-cols-2 gap-6"><div class="bg-white rounded-lg shadow p-6"><div class="flex items-center space-x-2 mb-6"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-fingerprint h-6 w-6 text-blue-600" aria-hidden="true"><path d="M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4"></path><path d="M14 13.12c0 2.38 0 6.38-1 8.88"></path><path d="M17.29 21.02c.12-.6.43-2.3.5-3.02"></path><path d="M2 12a10 10 0 0 1 18-6"></path><path d="M2 16h.01"></path><path d="M21.8 16c.2-2 .131-5.354 0-6"></path><path d="M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2"></path><path d="M8.65 22c.21-.66.45-1.32.57-2"></path><path d="M9 6.8a6 6 0 0 1 9 5.2v2"></path></svg><h2 class="text-xl font-semibold text-gray-900">Biometria Digital</h2></div><div class="space-y-6"><div class="relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 border-gray-300 bg-white"><div class="space-y-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-fingerprint h-12 w-12 text-gray-500" aria-hidden="true"><path d="M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4"></path><path d="M14 13.12c0 2.38 0 6.38-1 8.88"></path><path d="M17.29 21.02c.12-.6.43-2.3.5-3.02"></path><path d="M2 12a10 10 0 0 1 18-6"></path><path d="M2 16h.01"></path><path d="M21.8 16c.2-2 .131-5.354 0-6"></path><path d="M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2"></path><path d="M8.65 22c.21-.66.45-1.32.57-2"></path><path d="M9 6.8a6 6 0 0 1 9 5.2v2"></path></svg><div><h3 class="text-lg font-medium text-gray-900 mb-2">Posicione seu dedo no leitor biométrico</h3></div></div></div><div class="flex space-x-3"><button type="button" class="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2 flex-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-fingerprint h-4 w-4 mr-2" aria-hidden="true"><path d="M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4"></path><path d="M14 13.12c0 2.38 0 6.38-1 8.88"></path><path d="M17.29 21.02c.12-.6.43-2.3.5-3.02"></path><path d="M2 12a10 10 0 0 1 18-6"></path><path d="M2 16h.01"></path><path d="M21.8 16c.2-2 .131-5.354 0-6"></path><path d="M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2"></path><path d="M8.65 22c.21-.66.45-1.32.57-2"></path><path d="M9 6.8a6 6 0 0 1 9 5.2v2"></path></svg>Iniciar <!-- -->Biometria Digital</button></div><div class="bg-gray-50 rounded-lg p-4"><div class="flex items-start space-x-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle-alert h-5 w-5 text-yellow-600 mt-0.5" aria-hidden="true"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg><div class="text-sm text-gray-700"><p class="font-medium mb-1">Instruções:</p><ul class="space-y-1 text-xs"><li>• Posicione o dedo firmemente no sensor</li><li>• Mantenha o dedo imóvel durante o scan</li><li>• Certifique-se de que o dedo esteja limpo</li></ul></div></div></div></div></div><div class="bg-white rounded-lg shadow p-6"><div class="flex items-center space-x-2 mb-6"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-camera h-6 w-6 text-green-600" aria-hidden="true"><path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path><circle cx="12" cy="13" r="3"></circle></svg><h2 class="text-xl font-semibold text-gray-900">Reconhecimento Facial</h2></div><div class="space-y-6"><div class="space-y-4 mb-6"><div class="relative bg-gray-900 rounded-lg overflow-hidden"><video autoPlay="" playsInline="" muted="" class="w-full h-64 object-cover"></video><canvas width="640" height="480" class="absolute inset-0 w-full h-full object-cover"></canvas><div class="absolute inset-0 flex items-center justify-center"></div></div><div class="text-center text-blue-600"><p class="font-medium">Clique para iniciar</p></div><div class="flex justify-center space-x-3"></div><div class="text-sm text-gray-600 text-center space-y-1"><p>• Mantenha o rosto bem iluminado</p><p>• Olhe diretamente para a câmera</p><p>• Mantenha-se a uma distância confortável</p></div></div><div class="bg-gray-50 rounded-lg p-4"><div class="flex items-start space-x-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle-alert h-5 w-5 text-yellow-600 mt-0.5" aria-hidden="true"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg><div class="text-sm text-gray-700"><p class="font-medium mb-1">Instruções:</p><ul class="space-y-1 text-xs"><li>• Posicione o rosto centralizado na câmera</li><li>• Mantenha-se imóvel durante o scan</li><li>• Certifique-se de ter boa iluminação</li></ul></div></div></div></div></div></div><div class="bg-white rounded-lg shadow p-6"><div class="flex items-center space-x-2 mb-6"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-6 w-6 text-gray-600" aria-hidden="true"><path d="M12 6v6l4 2"></path><circle cx="12" cy="12" r="10"></circle></svg><h2 class="text-xl font-semibold text-gray-900">Registros Recentes</h2></div><!--$?--><template id="B:0"></template><div class="space-y-3"><div class="flex items-center space-x-4 p-4 border rounded-lg"><div class="w-12 h-12 bg-gray-200 rounded-full animate-pulse"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 rounded animate-pulse w-1/4"></div><div class="h-3 bg-gray-200 rounded animate-pulse w-1/3"></div></div><div class="w-20 h-8 bg-gray-200 rounded animate-pulse"></div></div><div class="flex items-center space-x-4 p-4 border rounded-lg"><div class="w-12 h-12 bg-gray-200 rounded-full animate-pulse"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 rounded animate-pulse w-1/4"></div><div class="h-3 bg-gray-200 rounded animate-pulse w-1/3"></div></div><div class="w-20 h-8 bg-gray-200 rounded animate-pulse"></div></div><div class="flex items-center space-x-4 p-4 border rounded-lg"><div class="w-12 h-12 bg-gray-200 rounded-full animate-pulse"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 rounded animate-pulse w-1/4"></div><div class="h-3 bg-gray-200 rounded animate-pulse w-1/3"></div></div><div class="w-20 h-8 bg-gray-200 rounded animate-pulse"></div></div></div><!--/$--></div></div></div></div><!--$--><!--/$--></main></div></div><script>requestAnimationFrame(function(){$RT=performance.now()});</script><script src="/_next/static/chunks/webpack-e4553bd4c3ca5db6.js" id="_R_" async=""></script><div hidden id="S:0"><div class="space-y-3"><div class="flex items-center space-x-4 p-4 border rounded-lg animate-pulse"><div class="w-12 h-12 bg-gray-200 rounded-full"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 rounded w-1/4"></div><div class="h-3 bg-gray-200 rounded w-1/3"></div></div><div class="w-20 h-8 bg-gray-200 rounded"></div></div><div class="flex items-center space-x-4 p-4 border rounded-lg animate-pulse"><div class="w-12 h-12 bg-gray-200 rounded-full"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 rounded w-1/4"></div><div class="h-3 bg-gray-200 rounded w-1/3"></div></div><div class="w-20 h-8 bg-gray-200 rounded"></div></div><div class="flex items-center space-x-4 p-4 border rounded-lg animate-pulse"><div class="w-12 h-12 bg-gray-200 rounded-full"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 rounded w-1/4"></div><div class="h-3 bg-gray-200 rounded w-1/3"></div></div><div class="w-20 h-8 bg-gray-200 rounded"></div></div><div class="flex items-center space-x-4 p-4 border rounded-lg animate-pulse"><div class="w-12 h-12 bg-gray-200 rounded-full"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 rounded w-1/4"></div><div class="h-3 bg-gray-200 rounded w-1/3"></div></div><div class="w-20 h-8 bg-gray-200 rounded"></div></div></div></div><script>$RB=[];$RV=function(b){$RT=performance.now();for(var a=0;a<b.length;a+=2){var c=b[a],e=b[a+1];null!==e.parentNode&&e.parentNode.removeChild(e);var f=c.parentNode;if(f){var g=c.previousSibling,h=0;do{if(c&&8===c.nodeType){var d=c.data;if("/$"===d||"/&"===d)if(0===h)break;else h--;else"$"!==d&&"$?"!==d&&"$~"!==d&&"$!"!==d&&"&"!==d||h++}d=c.nextSibling;f.removeChild(c);c=d}while(c);for(;e.firstChild;)f.insertBefore(e.firstChild,c);g.data="$";g._reactRetry&&g._reactRetry()}}b.length=0};
$RC=function(b,a){if(a=document.getElementById(a))(b=document.getElementById(b))?(b.previousSibling.data="$~",$RB.push(b,a),2===$RB.length&&(b="number"!==typeof $RT?0:$RT,a=performance.now(),setTimeout($RV.bind(null,$RB),2300>a&&2E3<a?2300-a:b+300-a))):a.parentNode.removeChild(a)};$RC("B:0","S:0")</script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[6062,[\"7177\",\"static/chunks/app/layout-7e63b6183d7635de.js\"],\"SessionProvider\"]\n3:I[6403,[\"7177\",\"static/chunks/app/layout-7e63b6183d7635de.js\"],\"TextFormattingProvider\"]\n4:I[7555,[],\"\"]\n5:I[1901,[\"6874\",\"static/chunks/6874-d27b54d0b28e3259.js\",\"4285\",\"static/chunks/4285-3cbfa4a2f71248be.js\",\"3769\",\"static/chunks/3769-e62c0e969fff9246.js\",\"8039\",\"static/chunks/app/error-da7a176665770fd1.js\"],\"default\"]\n6:I[1295,[],\"\"]\n7:I[2558,[\"6874\",\"static/chunks/6874-d27b54d0b28e3259.js\",\"4285\",\"static/chunks/4285-3cbfa4a2f71248be.js\",\"3769\",\"static/chunks/3769-e62c0e969fff9246.js\",\"4345\",\"static/chunks/app/not-found-0d232e224bc38f5f.js\"],\"default\"]\n8:I[277,[\"6874\",\"static/chunks/6874-d27b54d0b28e3259.js\",\"4285\",\"static/chunks/4285-3cbfa4a2f71248be.js\",\"3769\",\"static/chunks/3769-e62c0e969fff9246.js\",\"9305\",\"static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js\"],\"DashboardHeader\"]\n9:I[2623,[\"6874\",\"static/chunks/6874-d27b54d0b28e3259.js\",\"4285\",\"static/chunks/4285-3cbfa4a2f71248be.js\",\"3769\",\"static/chunks/3769-e62c0e969fff9246.js\",\"9305\",\"static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js\"],\"DashboardNav\"]\na:I[6874,[\"6874\",\"static/chunks/6874-d27b54d0b28e3259.js\",\"4285\",\"static/chunks/4285-3cbfa4a2f71248be.js\",\"3769\",\"static/chunks/3769-e62c0e969fff9246.js\",\"6011\",\"static/chunks/6011-2c5bc7b6ccc9fe2f.js\",\"5509\",\"static/chunks/app/(dashboard)/ponto/biometrico/page-24ad808f5fc34a25.js\"],\"\"]\nb:\"$Sreact.suspense\"\nc:I[4772,[\"6874\",\"static/chunks/6874-d27b54d0b28e3259.js\",\"4285\",\"static/chunks/4285-3cbfa4a2f71248be.js\",\"3769\",\"static/chunks/3769-e62c0e969fff9246.js\",\"6011\",\"static/chunks/6011-2c5bc7b6ccc9fe2f.js\",\"5509\",\"static/chunks/app/(dashboard)/ponto/biometrico/page-24ad808f5fc34a25.js\"],\"PontoStatus\"]\n13:I[8393,[],\"\"]\n:HL[\"/_next/static/css/b293d1867f231925.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"57groY_ofPMaGR4OOS9UN\",\"p\":\"\",\"c\":[\"\",\"ponto\",\"biometrico\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"(dashboard)\",{\"children\":[\"ponto\",{\"children\":[\"biometrico\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/b293d1867f231925.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$5\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L7\",null,{}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]}]]}],{\"children\":[\"(dashboard)\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gray-50\",\"children\":[[\"$\",\"$L8\",null,{}],[\"$\",\"div\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"$L9\",null,{}],[\"$\",\"main\",null,{\"className\":\"flex-1 overflow-auto\",\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}]]}]]}],{\"children\":[\"ponto\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"biometrico\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gray-50\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"space-y-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center space-x-4\",\"children\":[[\"$\",\"$La\",null,{\"href\":\"/dashboard\",\"children\":[\"$\",\"button\",null,{\"type\":\"button\",\"className\":\"inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none border border-gray-400 bg-transparent text-gray-700 hover:bg-gray-50 hover:border-gray-500 h-8 px-3 text-sm\",\"disabled\":false,\"onClick\":\"$undefined\",\"children\":[false,[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-left h-4 w-4 mr-2\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"1l729n\",{\"d\":\"m12 19-7-7 7-7\"}],[\"$\",\"path\",\"x3x0zl\",{\"d\":\"M19 12H5\"}],\"$undefined\"]}],\"Voltar\"]]}]}],[\"$\",\"div\",null,{\"className\":\"flex items-center space-x-3\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-2 bg-blue-600 rounded-lg\",\"children\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-fingerprint h-8 w-8 text-white\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"1nerag\",{\"d\":\"M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4\"}],[\"$\",\"path\",\"o46ks0\",{\"d\":\"M14 13.12c0 2.38 0 6.38-1 8.88\"}],[\"$\",\"path\",\"ptglia\",{\"d\":\"M17.29 21.02c.12-.6.43-2.3.5-3.02\"}],[\"$\",\"path\",\"ydlgp0\",{\"d\":\"M2 12a10 10 0 0 1 18-6\"}],[\"$\",\"path\",\"1gqxmh\",{\"d\":\"M2 16h.01\"}],[\"$\",\"path\",\"drycrb\",{\"d\":\"M21.8 16c.2-2 .131-5.354 0-6\"}],[\"$\",\"path\",\"1tidbn\",{\"d\":\"M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2\"}],[\"$\",\"path\",\"13wd9y\",{\"d\":\"M8.65 22c.21-.66.45-1.32.57-2\"}],[\"$\",\"path\",\"1fr1j5\",{\"d\":\"M9 6.8a6 6 0 0 1 9 5.2v2\"}],\"$undefined\"]}]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-3xl font-bold text-gray-900\",\"children\":\"Ponto Biométrico\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"Registre seu ponto usando biometria digital ou facial\"}]]}]]}]]}]}],[\"$\",\"$b\",null,{\"fallback\":[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow p-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"h-32 bg-gray-200 rounded-lg animate-pulse\"}]}],\"children\":[\"$\",\"$Lc\",null,{}]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 lg:grid-cols-2 gap-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow p-6\",\"children\":[\"$Ld\",\"$Le\"]}],\"$Lf\"]}],\"$L10\"]}]}]}],null,\"$L11\"]}],{},null,false]},null,false]},null,false]},null,false]},null,false],\"$L12\",false]],\"m\":\"$undefined\",\"G\":[\"$13\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"14:I[9706,[\"6874\",\"static/chunks/6874-d27b54d0b28e3259.js\",\"4285\",\"static/chunks/4285-3cbfa4a2f71248be.js\",\"3769\",\"static/chunks/3769-e62c0e969fff9246.js\",\"6011\",\"static/chunks/6011-2c5bc7b6ccc9fe2f.js\",\"5509\",\"static/chunks/app/(dashboard)/ponto/biometrico/page-24ad808f5fc34a25.js\"],\"BiometricScanner\"]\n15:I[4541,[\"6874\",\"static/chunks/6874-d27b54d0b28e3259.js\",\"4285\",\"static/chunks/4285-3cbfa4a2f71248be.js\",\"3769\",\"static/chunks/3769-e62c0e969fff9246.js\",\"6011\",\"static/chunks/6011-2c5bc7b6ccc9fe2f.js\",\"5509\",\"static/chunks/app/(dashboard)/ponto/biometrico/page-24ad808f5fc34a25.js\"],\"HistoricoRecente\"]\n16:I[9665,[],\"OutletBoundary\"]\n18:I[4911,[],\"AsyncMetadataOutlet\"]\n1a:I[9665,[],\"ViewportBoundary\"]\n1c:I[9665,[],\"MetadataBoundary\"]\n"])</script><script>self.__next_f.push([1,"d:[\"$\",\"div\",null,{\"className\":\"flex items-center space-x-2 mb-6\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-fingerprint h-6 w-6 text-blue-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"1nerag\",{\"d\":\"M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4\"}],[\"$\",\"path\",\"o46ks0\",{\"d\":\"M14 13.12c0 2.38 0 6.38-1 8.88\"}],[\"$\",\"path\",\"ptglia\",{\"d\":\"M17.29 21.02c.12-.6.43-2.3.5-3.02\"}],[\"$\",\"path\",\"ydlgp0\",{\"d\":\"M2 12a10 10 0 0 1 18-6\"}],[\"$\",\"path\",\"1gqxmh\",{\"d\":\"M2 16h.01\"}],[\"$\",\"path\",\"drycrb\",{\"d\":\"M21.8 16c.2-2 .131-5.354 0-6\"}],[\"$\",\"path\",\"1tidbn\",{\"d\":\"M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2\"}],[\"$\",\"path\",\"13wd9y\",{\"d\":\"M8.65 22c.21-.66.45-1.32.57-2\"}],[\"$\",\"path\",\"1fr1j5\",{\"d\":\"M9 6.8a6 6 0 0 1 9 5.2v2\"}],\"$undefined\"]}],[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold text-gray-900\",\"children\":\"Biometria Digital\"}]]}]\n"])</script><script>self.__next_f.push([1,"e:[\"$\",\"$L14\",null,{\"type\":\"fingerprint\"}]\n"])</script><script>self.__next_f.push([1,"f:[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow p-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center space-x-2 mb-6\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-camera h-6 w-6 text-green-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"1tc9qg\",{\"d\":\"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z\"}],[\"$\",\"circle\",\"1vg3eu\",{\"cx\":\"12\",\"cy\":\"13\",\"r\":\"3\"}],\"$undefined\"]}],[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold text-gray-900\",\"children\":\"Reconhecimento Facial\"}]]}],[\"$\",\"$L14\",null,{\"type\":\"facial\"}]]}]\n"])</script><script>self.__next_f.push([1,"10:[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow p-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center space-x-2 mb-6\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-clock h-6 w-6 text-gray-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"mmk7yg\",{\"d\":\"M12 6v6l4 2\"}],[\"$\",\"circle\",\"1mglay\",{\"cx\":\"12\",\"cy\":\"12\",\"r\":\"10\"}],\"$undefined\"]}],[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold text-gray-900\",\"children\":\"Registros Recentes\"}]]}],[\"$\",\"$b\",null,{\"fallback\":[\"$\",\"div\",null,{\"className\":\"space-y-3\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"flex items-center space-x-4 p-4 border rounded-lg\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-12 h-12 bg-gray-200 rounded-full animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"flex-1 space-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-1/4\"}],[\"$\",\"div\",null,{\"className\":\"h-3 bg-gray-200 rounded animate-pulse w-1/3\"}]]}],[\"$\",\"div\",null,{\"className\":\"w-20 h-8 bg-gray-200 rounded animate-pulse\"}]]}],[\"$\",\"div\",\"1\",{\"className\":\"flex items-center space-x-4 p-4 border rounded-lg\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-12 h-12 bg-gray-200 rounded-full animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"flex-1 space-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-1/4\"}],[\"$\",\"div\",null,{\"className\":\"h-3 bg-gray-200 rounded animate-pulse w-1/3\"}]]}],[\"$\",\"div\",null,{\"className\":\"w-20 h-8 bg-gray-200 rounded animate-pulse\"}]]}],[\"$\",\"div\",\"2\",{\"className\":\"flex items-center space-x-4 p-4 border rounded-lg\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-12 h-12 bg-gray-200 rounded-full animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"flex-1 space-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-1/4\"}],[\"$\",\"div\",null,{\"className\":\"h-3 bg-gray-200 rounded animate-pulse w-1/3\"}]]}],[\"$\",\"div\",null,{\"className\":\"w-20 h-8 bg-gray-200 rounded animate-pulse\"}]]}]]}],\"children\":[\"$\",\"$L15\",null,{}]}]]}]\n"])</script><script>self.__next_f.push([1,"11:[\"$\",\"$L16\",null,{\"children\":[\"$L17\",[\"$\",\"$L18\",null,{\"promise\":\"$@19\"}]]}]\n12:[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$L1a\",null,{\"children\":\"$L1b\"}],null],[\"$\",\"$L1c\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$b\",null,{\"fallback\":null,\"children\":\"$L1d\"}]}]}]]}]\n"])</script><script>self.__next_f.push([1,"1b:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n17:null\n"])</script><script>self.__next_f.push([1,"1e:I[8175,[],\"IconMark\"]\n"])</script><script>self.__next_f.push([1,"19:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Ponto Biométrico - RLPONTO\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Registro de ponto através de biometria\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"Your Name\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"Next.js,React,TypeScript,Tailwind CSS\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"Your Name\"}],[\"$\",\"meta\",\"5\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:title\",\"content\":\"My App\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:description\",\"content\":\"A professional Next.js application\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:url\",\"content\":\"http://************\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:site_name\",\"content\":\"My App\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"12\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:title\",\"content\":\"My App\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:description\",\"content\":\"A professional Next.js application\"}],[\"$\",\"link\",\"15\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"$L1e\",\"16\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"1d:\"$19:metadata\"\n"])</script></body></html>