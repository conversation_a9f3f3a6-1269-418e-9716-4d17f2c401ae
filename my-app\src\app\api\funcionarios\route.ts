import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Funcionario } from '@/types';
import { logFuncionarioAction } from '@/lib/audit-logger';
import { applyRateLimit } from '@/lib/rate-limit';
import { logger } from '@/lib/logger';
import { withSecurityHeaders } from '@/lib/security-headers';

// Helper para criar resposta segura
function createSecureResponse(body: any, init?: ResponseInit, request?: NextRequest) {
  const response = NextResponse.json(body, init);
  const origin = request?.headers.get('origin') || undefined;
  return withSecurityHeaders(response, origin);
}

// CORREÇÃO: Usar validador unificado de CPF
import { validateCPF, unformatCPF } from '@/lib/cpf-validator';

// Sistema robusto de geração de matrícula automática

// Schema de validação para funcionário
const funcionarioSchema = z.object({
  nomeCompleto: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  cpf: z.string()
    .transform(cpf => unformatCPF(cpf)) // Remove formatação
    .refine(cpf => cpf.length === 11, 'CPF deve ter 11 dígitos')
    .refine(validateCPF, 'CPF inválido'),
  rg: z.string().optional(),
  email: z.string().email('Email inválido').optional(),
  telefone: z.string().optional(),
  celular: z.string().optional(),
  endereco: z.object({
    cep: z.string().optional(),
    logradouro: z.string().optional(),
    numero: z.string().optional(),
    complemento: z.string().optional(),
    bairro: z.string().optional(),
    cidade: z.string().optional(),
    uf: z.string().optional(),
  }).optional(),
  dadosProfissionais: z.object({
    matricula: z.string().optional(), // Matrícula será gerada automaticamente
    cargo: z.string().min(1, 'Cargo é obrigatório'),
    setor: z.string().min(1, 'Setor é obrigatório'),
    dataAdmissao: z.string().transform((str) => new Date(str)),
    salario: z.number().optional(),
    cargaHoraria: z.number().min(1, 'Carga horária é obrigatória'),
    horarioTrabalho: z.object({
      entrada: z.string(),
      saida: z.string(),
      intervaloInicio: z.string().optional(),
      intervaloFim: z.string().optional(),
    }),
  }),
  observacoes: z.string().optional(),
});

// CORREÇÃO CRÍTICA: Migrar para persistência real com banco de dados
import { funcionarioStorageDB } from '@/lib/funcionario-storage-db';
import { funcionarioStorageFallback } from '@/lib/funcionario-storage-fallback';

// Sistema de fallback para quando o banco não estiver disponível
let funcionarioStorage: any = null;
let storageInitialized = false;

async function getStorage() {
  if (funcionarioStorage && storageInitialized) return funcionarioStorage;

  try {
    // Testar se o banco está disponível
    await funcionarioStorageDB.findAll();
    funcionarioStorage = funcionarioStorageDB;
    storageInitialized = true;
    logger.info('Usando storage com banco de dados');
  } catch (error) {
    funcionarioStorage = funcionarioStorageFallback;
    storageInitialized = true;
    logger.warn('Banco indisponível, usando fallback em memória', { error: error instanceof Error ? error.message : String(error) });
  }

  return funcionarioStorage;
}

export async function GET(request: NextRequest) {
  try {
    // 🔒 Verificar autenticação
    const session = await getServerSession(authOptions);
    if (!session) {
      return createSecureResponse(
        { success: false, error: 'Não autorizado' },
        { status: 401 },
        request
      );
    }

    // 🚦 Aplicar rate limiting para consultas
    const rateLimitResult = await applyRateLimit(request, 'consultas');
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: rateLimitResult.error,
          type: 'rate_limit_exceeded'
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': rateLimitResult.limit?.toString() || '60',
            'X-RateLimit-Remaining': rateLimitResult.remaining?.toString() || '0',
            'X-RateLimit-Reset': new Date(rateLimitResult.resetTime || Date.now() + 60000).toISOString()
          }
        }
      );
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const setor = searchParams.get('setor');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Log de auditoria para leitura de funcionários
    await logFuncionarioAction(
      'READ',
      'list',
      session.user?.id || 'unknown', // userId obtido da sessão
      {
        search,
        setor,
        status,
        page,
        limit,
      },
      request
    );

    // Buscar todos os funcionários ativos do storage
    const storage = await getStorage();
    let filteredFuncionarios = await storage.findAll ? await storage.findAll() : await storage.getAll();

    // Filtro por busca (nome, CPF, matrícula)
    if (search) {
      const searchLower = search.toLowerCase();
      filteredFuncionarios = filteredFuncionarios.filter(
        (funcionario) =>
          funcionario.nomeCompleto.toLowerCase().includes(searchLower) ||
          funcionario.cpf.includes(search) ||
          funcionario.dadosProfissionais.matricula.toLowerCase().includes(searchLower)
      );
    }

    // Filtro por setor
    if (setor) {
      filteredFuncionarios = filteredFuncionarios.filter(
        (funcionario) => funcionario.dadosProfissionais.setor.toLowerCase() === setor.toLowerCase()
      );
    }

    // Filtro por status
    if (status) {
      filteredFuncionarios = filteredFuncionarios.filter(
        (funcionario) => funcionario.status === status
      );
    }

    // Paginação
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedFuncionarios = filteredFuncionarios.slice(startIndex, endIndex);

    return createSecureResponse({
      success: true,
      funcionarios: paginatedFuncionarios,
      pagination: {
        page,
        limit,
        total: filteredFuncionarios.length,
        totalPages: Math.ceil(filteredFuncionarios.length / limit),
      },
    }, undefined, request);

  } catch (error) {
    logger.error('Erro ao buscar funcionários', {
      component: 'FuncionariosAPI',
      action: 'GET'
    }, error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // 🔒 Verificar autenticação
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // 🚦 Aplicar rate limiting para cadastros
    const rateLimitResult = await applyRateLimit(request, 'funcionarios');
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: rateLimitResult.error,
          type: 'rate_limit_exceeded'
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': rateLimitResult.limit?.toString() || '5',
            'X-RateLimit-Remaining': rateLimitResult.remaining?.toString() || '0',
            'X-RateLimit-Reset': new Date(rateLimitResult.resetTime || Date.now() + 60000).toISOString(),
            'Retry-After': Math.ceil(((rateLimitResult.resetTime || Date.now() + 60000) - Date.now()) / 1000).toString()
          }
        }
      );
    }

    const body = await request.json();

    // Validar dados de entrada
    const validationResult = funcionarioSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Dados inválidos',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    // Gerar matrícula automaticamente
    logger.debug('Gerando matrícula automaticamente', {
      component: 'FuncionariosAPI',
      action: 'generateMatricula'
    });
    const novaMatricula = await funcionarioStorage.getNextMatricula();
    logger.info('Matrícula gerada', {
      component: 'FuncionariosAPI',
      matricula: novaMatricula
    });

    try {
      // Verificar se CPF já existe no storage
      const cpfExistente = await funcionarioStorage.findByCpf(validationResult.data.cpf);

    if (cpfExistente) {
      logger.warn('Tentativa de cadastro com CPF duplicado', {
        component: 'FuncionariosAPI',
        action: 'validateCPF',
        cpf: validationResult.data.cpf.substring(0, 3) + '***' // Log parcial por segurança
      });
      return NextResponse.json(
        {
          success: false,
          error: 'CPF já está cadastrado',
          details: [`CPF ${validationResult.data.cpf} já está em uso por outro funcionário`],
        },
        { status: 409 }
      );
    }

    logger.debug('Validação concluída, prosseguindo com cadastro', {
      component: 'FuncionariosAPI',
      action: 'validateComplete'
    });

    // Gerar ID único e seguro
    const generateSecureId = async () => {
      const timestamp = Date.now().toString(36);
      const randomPart = Math.random().toString(36).substring(2, 15);
      const allFuncionarios = await funcionarioStorage.findAll();
      const counter = (allFuncionarios.length + 1).toString(36);
      return `func_${timestamp}_${randomPart}_${counter}`;
    };

    const novoFuncionario: Funcionario = {
      id: await generateSecureId(),
      ...validationResult.data,
      dadosProfissionais: {
        ...validationResult.data.dadosProfissionais,
        matricula: novaMatricula, // Usar a matrícula gerada automaticamente
      },
      foto: undefined,
      biometria: {
        cadastrada: false,
        templates: 0,
      },
      status: 'ativo',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Salvar no storage persistente
    const saveSuccess = await funcionarioStorage.save(novoFuncionario);

    if (!saveSuccess) {
      throw new Error('Falha ao salvar funcionário no storage');
    }

    // Log de auditoria para criação de funcionário
    await logFuncionarioAction(
      'CREATE',
      novoFuncionario.id,
      (session.user as any)?.id || session.user?.email || 'unknown', // userId obtido da sessão
      {
        nomeCompleto: novoFuncionario.nomeCompleto,
        cpf: novoFuncionario.cpf,
        matricula: novoFuncionario.dadosProfissionais.matricula,
        cargo: novoFuncionario.dadosProfissionais.cargo,
        setor: novoFuncionario.dadosProfissionais.setor,
      },
      request
    );

    logger.info('Funcionário cadastrado com sucesso', {
      component: 'FuncionariosAPI',
      action: 'create',
      matricula: novoFuncionario.dadosProfissionais.matricula,
      funcionarioId: novoFuncionario.id
    });

    const totalFuncionarios = (await funcionarioStorage.findAll()).length;
    logger.info('Estatísticas atualizadas', {
      component: 'FuncionariosAPI',
      totalFuncionarios
    });

      return createSecureResponse({
        success: true,
        funcionario: novoFuncionario,
        message: 'Funcionário cadastrado com sucesso',
      }, undefined, request);

    } catch (innerError) {
      logger.error('Erro ao criar funcionário', {
        component: 'FuncionariosAPI',
        action: 'POST'
      }, innerError instanceof Error ? innerError : new Error(String(innerError)));
      return NextResponse.json(
        {
          success: false,
          error: 'Erro interno do servidor',
        },
        { status: 500 }
      );
    }

  } catch (error) {
    logger.error('Erro na validação', {
      component: 'FuncionariosAPI',
      action: 'POST'
    }, error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}

// Suporte para requisições OPTIONS (CORS preflight)
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('origin');
  const response = new NextResponse(null, { status: 204 });
  return withSecurityHeaders(response, origin || undefined);
}
