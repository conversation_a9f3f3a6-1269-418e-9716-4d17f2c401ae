{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "dynamicRoutes": [{"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/funcionarios/[id]", "regex": "^/api/funcionarios/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/funcionarios/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/funcionarios/[id]", "regex": "^/funcionarios/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/funcionarios/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/funcionarios/[id]/biometria", "regex": "^/funcionarios/([^/]+?)/biometria(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/funcionarios/(?<nxtPid>[^/]+?)/biometria(?:/)?$"}, {"page": "/funcionarios/[id]/editar", "regex": "^/funcionarios/([^/]+?)/editar(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/funcionarios/(?<nxtPid>[^/]+?)/editar(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/administracao", "regex": "^/administracao(?:/)?$", "routeKeys": {}, "namedRegex": "^/administracao(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/estatisticas", "regex": "^/estatisticas(?:/)?$", "routeKeys": {}, "namedRegex": "^/estatisticas(?:/)?$"}, {"page": "/estatisticas/absenteismo", "regex": "^/estatisticas/absenteismo(?:/)?$", "routeKeys": {}, "namedRegex": "^/estatisticas/absenteismo(?:/)?$"}, {"page": "/estatisticas/comparativos", "regex": "^/estatisticas/comparativos(?:/)?$", "routeKeys": {}, "namedRegex": "^/estatisticas/comparativos(?:/)?$"}, {"page": "/estatisticas/produtividade", "regex": "^/estatisticas/produtividade(?:/)?$", "routeKeys": {}, "namedRegex": "^/estatisticas/produtividade(?:/)?$"}, {"page": "/estatisticas/tendencias", "regex": "^/estatisticas/tendencias(?:/)?$", "routeKeys": {}, "namedRegex": "^/estatisticas/tendencias(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/funcionarios", "regex": "^/funcionarios(?:/)?$", "routeKeys": {}, "namedRegex": "^/funcionarios(?:/)?$"}, {"page": "/funcionarios/novo", "regex": "^/funcionarios/novo(?:/)?$", "routeKeys": {}, "namedRegex": "^/funcionarios/novo(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/periodo-apuracao", "regex": "^/periodo\\-apuracao(?:/)?$", "routeKeys": {}, "namedRegex": "^/periodo\\-apuracao(?:/)?$"}, {"page": "/ponto", "regex": "^/ponto(?:/)?$", "routeKeys": {}, "namedRegex": "^/ponto(?:/)?$"}, {"page": "/ponto/biometrico", "regex": "^/ponto/biometrico(?:/)?$", "routeKeys": {}, "namedRegex": "^/ponto/biometrico(?:/)?$"}, {"page": "/ponto/manual", "regex": "^/ponto/manual(?:/)?$", "routeKeys": {}, "namedRegex": "^/ponto/manual(?:/)?$"}, {"page": "/relatorios", "regex": "^/relatorios(?:/)?$", "routeKeys": {}, "namedRegex": "^/relatorios(?:/)?$"}, {"page": "/relatorios/agendamentos", "regex": "^/relatorios/agendamentos(?:/)?$", "routeKeys": {}, "namedRegex": "^/relatorios/agendamentos(?:/)?$"}, {"page": "/relatorios/analiticos", "regex": "^/relatorios/analiticos(?:/)?$", "routeKeys": {}, "namedRegex": "^/relatorios/analiticos(?:/)?$"}, {"page": "/relatorios/construtor", "regex": "^/relatorios/construtor(?:/)?$", "routeKeys": {}, "namedRegex": "^/relatorios/construtor(?:/)?$"}, {"page": "/relatorios/funcionario", "regex": "^/relatorios/funcionario(?:/)?$", "routeKeys": {}, "namedRegex": "^/relatorios/funcionario(?:/)?$"}, {"page": "/relatorios/insights", "regex": "^/relatorios/insights(?:/)?$", "routeKeys": {}, "namedRegex": "^/relatorios/insights(?:/)?$"}, {"page": "/relatorios/periodo", "regex": "^/relatorios/periodo(?:/)?$", "routeKeys": {}, "namedRegex": "^/relatorios/periodo(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}}