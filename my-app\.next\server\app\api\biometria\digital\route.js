(()=>{var a={};a.id=595,a.ids=[595],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},20500:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>I,patchFetch:()=>H,routeModule:()=>D,serverHooks:()=>G,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>F});var d={};c.r(d),c.d(d,{DELETE:()=>C,GET:()=>B,POST:()=>z,PUT:()=>A});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(50639),w=c(14250);let x=v.Ik({credentialId:v.Yj(),rawId:v.YO(v.ai()),type:v.Yj(),timestamp:v.ai()}),y=new Map;async function z(a){try{let b=await a.json(),{credentialId:c,rawId:d,type:e,timestamp:f}=x.parse(b),g=y.get(c);if(!g)return u.NextResponse.json({success:!1,error:"Credencial biom\xe9trica n\xe3o encontrada",details:"Funcion\xe1rio n\xe3o cadastrado ou biometria n\xe3o registrada"},{status:404});if(Date.now()-f>3e5)return u.NextResponse.json({success:!1,error:"Credencial expirada",details:"Tente novamente"},{status:400});let h=new Date().getHours(),i="Entrada";h>=12&&h<14?i="Sa\xedda para Almo\xe7o":h>=14&&h<18?i="Retorno do Almo\xe7o":h>=18&&(i="Sa\xedda");let j=await fetch(`${a.nextUrl.origin}/api/ponto/biometrico`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({funcionarioId:g.funcionarioId,tipoRegistro:i,metodo:"biometria_digital",credentialId:c,timestamp:new Date().toISOString()})}),k=await j.json();if(!k.success)return u.NextResponse.json({success:!1,error:"Erro ao registrar ponto",details:k.error},{status:500});return u.NextResponse.json({success:!0,funcionario:g.funcionario,tipoRegistro:i,horario:new Date().toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"}),message:"Ponto registrado com sucesso via biometria digital"})}catch(a){if(console.error("Erro na biometria digital:",a),a instanceof w.G)return u.NextResponse.json({success:!1,error:"Dados inv\xe1lidos",details:a.errors.map(a=>a.message)},{status:400});return u.NextResponse.json({success:!1,error:"Erro interno do servidor",details:"Falha no processamento da biometria digital"},{status:500})}}async function A(a){try{let{funcionarioId:b,funcionario:c,credentialId:d,publicKey:e}=await a.json();if(!b||!c||!d||!e)return u.NextResponse.json({success:!1,error:"Dados obrigat\xf3rios n\xe3o fornecidos",details:"funcionarioId, funcionario, credentialId e publicKey s\xe3o obrigat\xf3rios"},{status:400});if(y.has(d))return u.NextResponse.json({success:!1,error:"Credencial j\xe1 cadastrada",details:"Esta biometria digital j\xe1 est\xe1 registrada"},{status:409});return y.set(d,{funcionarioId:b,funcionario:c,credentialId:d,publicKey:new Uint8Array(e),counter:0,createdAt:new Date}),u.NextResponse.json({success:!0,message:"Biometria digital cadastrada com sucesso",credentialId:d,funcionarioId:b})}catch(a){return console.error("Erro ao cadastrar biometria digital:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor",details:"Falha no cadastro da biometria digital"},{status:500})}}async function B(){try{let a=Array.from(y.entries()).map(([a,b])=>({credentialId:a,funcionarioId:b.funcionarioId,funcionario:b.funcionario,createdAt:b.createdAt}));return u.NextResponse.json({success:!0,credentials:a,total:a.length})}catch(a){return console.error("Erro ao listar credenciais:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}async function C(a){try{let{searchParams:b}=new URL(a.url),c=b.get("credentialId");if(!c)return u.NextResponse.json({success:!1,error:"credentialId \xe9 obrigat\xf3rio"},{status:400});if(!y.delete(c))return u.NextResponse.json({success:!1,error:"Credencial n\xe3o encontrada"},{status:404});return u.NextResponse.json({success:!0,message:"Credencial removida com sucesso"})}catch(a){return console.error("Erro ao remover credencial:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}let D=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/biometria/digital/route",pathname:"/api/biometria/digital",filename:"route",bundlePath:"app/api/biometria/digital/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\biometria\\digital\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:E,workUnitAsyncStorage:F,serverHooks:G}=D;function H(){return(0,g.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:F})}async function I(a,b,c){var d;let e="/api/biometria/digital/route";"/index"===e&&(e="/");let g=await D.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[C]);if(F&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||D.isDev||x||(G="/index"===(G=C)?"/":G);let H=!0===D.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>D.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>D.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&B&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await D.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await D.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await D.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,6055,639],()=>b(b.s=20500));module.exports=c})();