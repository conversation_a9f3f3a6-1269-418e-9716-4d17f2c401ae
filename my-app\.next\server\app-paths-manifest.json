{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/audit/route": "app/api/audit/route.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/auth/callback/credentials/route": "app/api/auth/callback/credentials/route.js", "/api/biometria/digital/route": "app/api/biometria/digital/route.js", "/api/biometria/facial/route": "app/api/biometria/facial/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/funcionarios/[id]/route": "app/api/funcionarios/[id]/route.js", "/api/funcionarios/next-matricula/route": "app/api/funcionarios/next-matricula/route.js", "/api/errors/route": "app/api/errors/route.js", "/api/funcionarios/reset/route": "app/api/funcionarios/reset/route.js", "/api/funcionarios/route": "app/api/funcionarios/route.js", "/api/health/route": "app/api/health/route.js", "/api/matricula/route": "app/api/matricula/route.js", "/api/ponto/manual/funcionarios/route": "app/api/ponto/manual/funcionarios/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/api/ponto/biometrico/route": "app/api/ponto/biometrico/route.js", "/api/ponto/manual/route": "app/api/ponto/manual/route.js", "/_not-found/page": "app/_not-found/page.js", "/dashboard/page": "app/dashboard/page.js", "/login/page": "app/login/page.js", "/page": "app/page.js", "/(dashboard)/ponto/page": "app/(dashboard)/ponto/page.js", "/(dashboard)/funcionarios/[id]/editar/page": "app/(dashboard)/funcionarios/[id]/editar/page.js", "/(dashboard)/funcionarios/[id]/page": "app/(dashboard)/funcionarios/[id]/page.js", "/(dashboard)/estatisticas/page": "app/(dashboard)/estatisticas/page.js", "/(dashboard)/administracao/page": "app/(dashboard)/administracao/page.js", "/(dashboard)/estatisticas/comparativos/page": "app/(dashboard)/estatisticas/comparativos/page.js", "/(dashboard)/funcionarios/novo/page": "app/(dashboard)/funcionarios/novo/page.js", "/(dashboard)/ponto/biometrico/page": "app/(dashboard)/ponto/biometrico/page.js", "/(dashboard)/estatisticas/absenteismo/page": "app/(dashboard)/estatisticas/absenteismo/page.js", "/(dashboard)/funcionarios/page": "app/(dashboard)/funcionarios/page.js", "/(dashboard)/estatisticas/produtividade/page": "app/(dashboard)/estatisticas/produtividade/page.js", "/(dashboard)/periodo-apuracao/page": "app/(dashboard)/periodo-apuracao/page.js", "/(dashboard)/relatorios/analiticos/page": "app/(dashboard)/relatorios/analiticos/page.js", "/(dashboard)/estatisticas/tendencias/page": "app/(dashboard)/estatisticas/tendencias/page.js", "/(dashboard)/relatorios/insights/page": "app/(dashboard)/relatorios/insights/page.js", "/(dashboard)/relatorios/construtor/page": "app/(dashboard)/relatorios/construtor/page.js", "/(dashboard)/relatorios/agendamentos/page": "app/(dashboard)/relatorios/agendamentos/page.js", "/(dashboard)/relatorios/periodo/page": "app/(dashboard)/relatorios/periodo/page.js", "/(dashboard)/relatorios/page": "app/(dashboard)/relatorios/page.js", "/(dashboard)/funcionarios/[id]/biometria/page": "app/(dashboard)/funcionarios/[id]/biometria/page.js", "/(dashboard)/ponto/manual/page": "app/(dashboard)/ponto/manual/page.js", "/(dashboard)/relatorios/funcionario/page": "app/(dashboard)/relatorios/funcionario/page.js"}