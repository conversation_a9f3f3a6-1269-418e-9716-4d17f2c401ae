(()=>{var a={};a.id=7502,a.ids=[7502],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33175:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>J,patchFetch:()=>I,routeModule:()=>E,serverHooks:()=>H,workAsyncStorage:()=>F,workUnitAsyncStorage:()=>G});var d={};c.r(d),c.d(d,{GET:()=>B,POST:()=>C,PUT:()=>D});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190);let v=new Set,w=new Map;async function x(){try{let a=global.funcionariosCadastrados||[];for(let b of(v.clear(),a))b.dadosProfissionais?.matricula&&v.add(b.dadosProfissionais.matricula);console.log(`[MATRICULA] Matr\xedculas em uso: ${Array.from(v).sort().join(", ")}`)}catch(a){console.error("[MATRICULA] Erro ao atualizar matr\xedculas utilizadas:",a)}}async function y(){if(await x(),0===v.size)return console.log("[MATRICULA] Nenhuma matr\xedcula encontrada. Iniciando com 0001"),"0001";let a=0;for(let b of v){let c=parseInt(b,10);!isNaN(c)&&c>a&&(a=c)}let b=(a+1).toString().padStart(4,"0");return console.log(`[MATRICULA] Matr\xedculas existentes: ${Array.from(v).sort().join(", ")}`),console.log(`[MATRICULA] Maior matr\xedcula encontrada: ${a.toString().padStart(4,"0")}`),console.log(`[MATRICULA] Pr\xf3xima matr\xedcula gerada: ${b}`),b}async function z(a){return await x(),!!/^\d{4}$/.test(a)&&!v.has(a)}async function A(a){return!(!await z(a)||w.has(a))&&(w.set(a,!0),setTimeout(()=>{w.delete(a),console.log(`[MATRICULA] Reserva da matr\xedcula ${a} expirou`)},3e5),console.log(`[MATRICULA] Matr\xedcula ${a} reservada temporariamente`),!0)}async function B(a){try{let a=await y();return u.NextResponse.json({success:!0,matricula:a,formato:"Matr\xedcula gerada automaticamente no formato 0001, 0002, etc.",regras:{sequencial:!0,unica:!0,naoReutilizada:!0,formato:"4 d\xedgitos com zeros \xe0 esquerda"}})}catch(a){return console.error("[MATRICULA] Erro ao gerar pr\xf3xima matr\xedcula:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor ao gerar matr\xedcula"},{status:500})}}async function C(a){try{let{action:b,matricula:c}=await a.json();if("reserve"===b){if(!c)return u.NextResponse.json({success:!1,error:"Matr\xedcula \xe9 obrigat\xf3ria para reserva"},{status:400});if(!await A(c))return u.NextResponse.json({success:!1,error:"Matr\xedcula j\xe1 est\xe1 em uso ou sendo processada",matricula:c},{status:409});return u.NextResponse.json({success:!0,matricula:c,message:"Matr\xedcula reservada com sucesso",action:"reserved"})}if("generate-and-reserve"!==b)return u.NextResponse.json({success:!1,error:'A\xe7\xe3o inv\xe1lida. Use "reserve" ou "generate-and-reserve"'},{status:400});{let a=await y();if(!await A(a))return u.NextResponse.json({success:!1,error:"Erro interno: matr\xedcula gerada j\xe1 estava em uso"},{status:500});return u.NextResponse.json({success:!0,matricula:a,message:"Matr\xedcula gerada e reservada automaticamente",action:"generated-and-reserved"})}}catch(a){return console.error("[MATRICULA] Erro ao processar requisi\xe7\xe3o POST:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}async function D(a){try{let{matricula:b}=await a.json();if(!b)return u.NextResponse.json({success:!1,error:"Matr\xedcula \xe9 obrigat\xf3ria"},{status:400});let c=await z(b);return u.NextResponse.json({success:!0,matricula:b,disponivel:c,message:c?"Matr\xedcula dispon\xedvel":"Matr\xedcula j\xe1 est\xe1 em uso"})}catch(a){return console.error("[MATRICULA] Erro ao verificar disponibilidade:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}let E=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/matricula/route",pathname:"/api/matricula",filename:"route",bundlePath:"app/api/matricula/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\matricula\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:F,workUnitAsyncStorage:G,serverHooks:H}=E;function I(){return(0,g.patchFetch)({workAsyncStorage:F,workUnitAsyncStorage:G})}async function J(a,b,c){var d;let e="/api/matricula/route";"/index"===e&&(e="/");let g=await E.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=g,D=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[D]||y.routes[C]);if(F&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[D];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||E.isDev||x||(G="/index"===(G=C)?"/":G);let H=!0===E.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>E.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>E.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&B&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await E.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await E.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await E.onRequestError(a,b,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,6055],()=>b(b.s=33175));module.exports=c})();