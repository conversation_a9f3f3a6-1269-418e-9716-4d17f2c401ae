1:"$Sreact.fragment"
2:I[6062,["7177","static/chunks/app/layout-7e63b6183d7635de.js"],"SessionProvider"]
3:I[6403,["7177","static/chunks/app/layout-7e63b6183d7635de.js"],"TextFormattingProvider"]
4:I[7555,[],""]
5:I[1901,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","8039","static/chunks/app/error-da7a176665770fd1.js"],"default"]
6:I[1295,[],""]
7:I[2558,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","4345","static/chunks/app/not-found-0d232e224bc38f5f.js"],"default"]
8:I[277,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","9305","static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js"],"DashboardHeader"]
9:I[2623,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","9305","static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js"],"DashboardNav"]
a:I[6874,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","7772","static/chunks/app/(dashboard)/ponto/manual/page-ff2461993c60ff32.js"],""]
14:I[8393,[],""]
:HL["/_next/static/css/b293d1867f231925.css","style"]
0:{"P":null,"b":"57groY_ofPMaGR4OOS9UN","p":"","c":["","ponto","manual"],"i":false,"f":[[["",{"children":["(dashboard)",{"children":["ponto",{"children":["manual",{"children":["__PAGE__",{}]}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/b293d1867f231925.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$5","errorStyles":[],"errorScripts":[],"template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","$L7",null,{}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]}]]}],{"children":["(dashboard)",["$","$1","c",{"children":[null,["$","div",null,{"className":"min-h-screen bg-gray-50","children":[["$","$L8",null,{}],["$","div",null,{"className":"flex","children":[["$","$L9",null,{}],["$","main",null,{"className":"flex-1 overflow-auto","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]]}]]}],{"children":["ponto",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["manual",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-gray-50","children":["$","div",null,{"className":"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8","children":["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"className":"flex items-center justify-between","children":["$","div",null,{"className":"flex items-center space-x-4","children":[["$","$La",null,{"href":"/ponto/biometrico","children":["$","button",null,{"type":"button","className":"inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none border border-gray-400 bg-transparent text-gray-700 hover:bg-gray-50 hover:border-gray-500 h-8 px-3 text-sm","disabled":false,"onClick":"$undefined","children":[false,[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-left h-4 w-4 mr-2","aria-hidden":"true","children":[["$","path","1l729n",{"d":"m12 19-7-7 7-7"}],["$","path","x3x0zl",{"d":"M19 12H5"}],"$undefined"]}],"Voltar"]]}]}],["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"p-2 bg-orange-600 rounded-lg","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-hand h-8 w-8 text-white","aria-hidden":"true","children":[["$","path","1fvzgz",{"d":"M18 11V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2"}],["$","path","1kc0my",{"d":"M14 10V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v2"}],["$","path","10h0bg",{"d":"M10 10.5V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2v8"}],["$","path","1s1gnw",{"d":"M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15"}],"$undefined"]}]}],["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold text-gray-900","children":"Ponto Manual"}],["$","p",null,{"className":"text-gray-600","children":"Registre o ponto manualmente quando necessário"}]]}]]}]]}]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-4","children":[["$","div",null,{"className":"bg-yellow-50 border border-yellow-200 rounded-lg p-4","children":["$","div",null,{"className":"flex items-start space-x-3","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-triangle-alert h-5 w-5 text-yellow-600 mt-0.5","aria-hidden":"true","children":["$Lb","$Lc","$Ld","$undefined"]}],"$Le"]}]}],"$Lf"]}],"$L10","$L11"]}]}]}],null,"$L12"]}],{},null,false]},null,false]},null,false]},null,false]},null,false],"$L13",false]],"m":"$undefined","G":["$14",[]],"s":false,"S":true}
15:"$Sreact.suspense"
16:I[2952,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","7772","static/chunks/app/(dashboard)/ponto/manual/page-ff2461993c60ff32.js"],"ManualForm"]
17:I[9665,[],"OutletBoundary"]
19:I[4911,[],"AsyncMetadataOutlet"]
1b:I[9665,[],"ViewportBoundary"]
1d:I[9665,[],"MetadataBoundary"]
b:["$","path","wmoenq",{"d":"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"}]
c:["$","path","juzpu7",{"d":"M12 9v4"}]
d:["$","path","p32p05",{"d":"M12 17h.01"}]
e:["$","div",null,{"children":[["$","h3",null,{"className":"text-sm font-medium text-yellow-800","children":"Atenção"}],["$","p",null,{"className":"text-sm text-yellow-700 mt-1","children":"O registro manual requer justificativa e pode necessitar aprovação do supervisor."}]]}]
f:["$","div",null,{"className":"bg-blue-50 border border-blue-200 rounded-lg p-4","children":["$","div",null,{"className":"flex items-start space-x-3","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-info h-5 w-5 text-blue-600 mt-0.5","aria-hidden":"true","children":[["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],["$","path","1dtifu",{"d":"M12 16v-4"}],["$","path","e9boi3",{"d":"M12 8h.01"}],"$undefined"]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-sm font-medium text-blue-800","children":"Quando usar"}],["$","p",null,{"className":"text-sm text-blue-700 mt-1","children":"Use apenas quando a biometria não estiver disponível ou em situações excepcionais."}]]}]]}]}]
10:["$","div",null,{"className":"bg-white rounded-lg shadow","children":[["$","div",null,{"className":"px-6 py-4 border-b border-gray-200","children":["$","div",null,{"className":"flex items-center space-x-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-5 w-5 text-orange-600","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}],["$","h2",null,{"className":"text-xl font-semibold text-gray-900","children":"Registro Manual"}]]}]}],["$","div",null,{"className":"p-6","children":["$","$15",null,{"fallback":["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-6","children":[["$","div","0",{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/4"}],["$","div",null,{"className":"h-10 bg-gray-200 rounded"}]]}],["$","div","1",{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/4"}],["$","div",null,{"className":"h-10 bg-gray-200 rounded"}]]}],["$","div","2",{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/4"}],["$","div",null,{"className":"h-10 bg-gray-200 rounded"}]]}],["$","div","3",{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/4"}],["$","div",null,{"className":"h-10 bg-gray-200 rounded"}]]}]]}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/4"}],["$","div",null,{"className":"h-24 bg-gray-200 rounded"}]]}],["$","div",null,{"className":"h-10 bg-gray-200 rounded w-32"}]]}],"children":["$","$L16",null,{}]}]}]]}]
11:["$","div",null,{"className":"bg-gray-50 rounded-lg p-6","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 mb-4","children":"Informações Importantes"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-6","children":[["$","div",null,{"children":[["$","h4",null,{"className":"text-sm font-medium text-gray-900 mb-2","children":"Documentação Necessária"}],["$","ul",null,{"className":"text-sm text-gray-600 space-y-1","children":[["$","li",null,{"children":"• Justificativa detalhada do motivo"}],["$","li",null,{"children":"• Foto comprobatória (opcional)"}],["$","li",null,{"children":"• Localização atual (se disponível)"}]]}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"text-sm font-medium text-gray-900 mb-2","children":"Processo de Aprovação"}],["$","ul",null,{"className":"text-sm text-gray-600 space-y-1","children":[["$","li",null,{"children":"• Registro enviado para análise"}],["$","li",null,{"children":"• Supervisor recebe notificação"}],["$","li",null,{"children":"• Aprovação em até 24 horas"}]]}]]}]]}]]}]
12:["$","$L17",null,{"children":["$L18",["$","$L19",null,{"promise":"$@1a"}]]}]
13:["$","$1","h",{"children":[null,[["$","$L1b",null,{"children":"$L1c"}],null],["$","$L1d",null,{"children":["$","div",null,{"hidden":true,"children":["$","$15",null,{"fallback":null,"children":"$L1e"}]}]}]]}]
1c:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
18:null
1f:I[8175,[],"IconMark"]
1a:{"metadata":[["$","title","0",{"children":"Ponto Manual - RLPONTO"}],["$","meta","1",{"name":"description","content":"Registro manual de ponto"}],["$","meta","2",{"name":"author","content":"Your Name"}],["$","meta","3",{"name":"keywords","content":"Next.js,React,TypeScript,Tailwind CSS"}],["$","meta","4",{"name":"creator","content":"Your Name"}],["$","meta","5",{"name":"robots","content":"index, follow"}],["$","meta","6",{"property":"og:title","content":"My App"}],["$","meta","7",{"property":"og:description","content":"A professional Next.js application"}],["$","meta","8",{"property":"og:url","content":"http://************"}],["$","meta","9",{"property":"og:site_name","content":"My App"}],["$","meta","10",{"property":"og:locale","content":"en_US"}],["$","meta","11",{"property":"og:type","content":"website"}],["$","meta","12",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","13",{"name":"twitter:title","content":"My App"}],["$","meta","14",{"name":"twitter:description","content":"A professional Next.js application"}],["$","link","15",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L1f","16",{}]],"error":null,"digest":"$undefined"}
1e:"$1a:metadata"
