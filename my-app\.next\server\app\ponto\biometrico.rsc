1:"$Sreact.fragment"
2:I[6062,["7177","static/chunks/app/layout-7e63b6183d7635de.js"],"SessionProvider"]
3:I[6403,["7177","static/chunks/app/layout-7e63b6183d7635de.js"],"TextFormattingProvider"]
4:I[7555,[],""]
5:I[1901,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","8039","static/chunks/app/error-da7a176665770fd1.js"],"default"]
6:I[1295,[],""]
7:I[2558,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","4345","static/chunks/app/not-found-0d232e224bc38f5f.js"],"default"]
8:I[277,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","9305","static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js"],"DashboardHeader"]
9:I[2623,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","9305","static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js"],"DashboardNav"]
a:I[6874,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","6011","static/chunks/6011-2c5bc7b6ccc9fe2f.js","5509","static/chunks/app/(dashboard)/ponto/biometrico/page-24ad808f5fc34a25.js"],""]
b:"$Sreact.suspense"
c:I[4772,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","6011","static/chunks/6011-2c5bc7b6ccc9fe2f.js","5509","static/chunks/app/(dashboard)/ponto/biometrico/page-24ad808f5fc34a25.js"],"PontoStatus"]
13:I[8393,[],""]
:HL["/_next/static/css/b293d1867f231925.css","style"]
0:{"P":null,"b":"57groY_ofPMaGR4OOS9UN","p":"","c":["","ponto","biometrico"],"i":false,"f":[[["",{"children":["(dashboard)",{"children":["ponto",{"children":["biometrico",{"children":["__PAGE__",{}]}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/b293d1867f231925.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$5","errorStyles":[],"errorScripts":[],"template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","$L7",null,{}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]}]]}],{"children":["(dashboard)",["$","$1","c",{"children":[null,["$","div",null,{"className":"min-h-screen bg-gray-50","children":[["$","$L8",null,{}],["$","div",null,{"className":"flex","children":[["$","$L9",null,{}],["$","main",null,{"className":"flex-1 overflow-auto","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]]}]]}],{"children":["ponto",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["biometrico",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-gray-50","children":["$","div",null,{"className":"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8","children":["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"className":"flex items-center justify-between","children":["$","div",null,{"className":"flex items-center space-x-4","children":[["$","$La",null,{"href":"/dashboard","children":["$","button",null,{"type":"button","className":"inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none border border-gray-400 bg-transparent text-gray-700 hover:bg-gray-50 hover:border-gray-500 h-8 px-3 text-sm","disabled":false,"onClick":"$undefined","children":[false,[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-left h-4 w-4 mr-2","aria-hidden":"true","children":[["$","path","1l729n",{"d":"m12 19-7-7 7-7"}],["$","path","x3x0zl",{"d":"M19 12H5"}],"$undefined"]}],"Voltar"]]}]}],["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"p-2 bg-blue-600 rounded-lg","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-fingerprint h-8 w-8 text-white","aria-hidden":"true","children":[["$","path","1nerag",{"d":"M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4"}],["$","path","o46ks0",{"d":"M14 13.12c0 2.38 0 6.38-1 8.88"}],["$","path","ptglia",{"d":"M17.29 21.02c.12-.6.43-2.3.5-3.02"}],["$","path","ydlgp0",{"d":"M2 12a10 10 0 0 1 18-6"}],["$","path","1gqxmh",{"d":"M2 16h.01"}],["$","path","drycrb",{"d":"M21.8 16c.2-2 .131-5.354 0-6"}],["$","path","1tidbn",{"d":"M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2"}],["$","path","13wd9y",{"d":"M8.65 22c.21-.66.45-1.32.57-2"}],["$","path","1fr1j5",{"d":"M9 6.8a6 6 0 0 1 9 5.2v2"}],"$undefined"]}]}],["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold text-gray-900","children":"Ponto Biométrico"}],["$","p",null,{"className":"text-gray-600","children":"Registre seu ponto usando biometria digital ou facial"}]]}]]}]]}]}],["$","$b",null,{"fallback":["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":["$","div",null,{"className":"h-32 bg-gray-200 rounded-lg animate-pulse"}]}],"children":["$","$Lc",null,{}]}],["$","div",null,{"className":"grid grid-cols-1 lg:grid-cols-2 gap-6","children":[["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":["$Ld","$Le"]}],"$Lf"]}],"$L10"]}]}]}],null,"$L11"]}],{},null,false]},null,false]},null,false]},null,false]},null,false],"$L12",false]],"m":"$undefined","G":["$13",[]],"s":false,"S":true}
14:I[9706,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","6011","static/chunks/6011-2c5bc7b6ccc9fe2f.js","5509","static/chunks/app/(dashboard)/ponto/biometrico/page-24ad808f5fc34a25.js"],"BiometricScanner"]
15:I[4541,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","6011","static/chunks/6011-2c5bc7b6ccc9fe2f.js","5509","static/chunks/app/(dashboard)/ponto/biometrico/page-24ad808f5fc34a25.js"],"HistoricoRecente"]
16:I[9665,[],"OutletBoundary"]
18:I[4911,[],"AsyncMetadataOutlet"]
1a:I[9665,[],"ViewportBoundary"]
1c:I[9665,[],"MetadataBoundary"]
d:["$","div",null,{"className":"flex items-center space-x-2 mb-6","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-fingerprint h-6 w-6 text-blue-600","aria-hidden":"true","children":[["$","path","1nerag",{"d":"M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4"}],["$","path","o46ks0",{"d":"M14 13.12c0 2.38 0 6.38-1 8.88"}],["$","path","ptglia",{"d":"M17.29 21.02c.12-.6.43-2.3.5-3.02"}],["$","path","ydlgp0",{"d":"M2 12a10 10 0 0 1 18-6"}],["$","path","1gqxmh",{"d":"M2 16h.01"}],["$","path","drycrb",{"d":"M21.8 16c.2-2 .131-5.354 0-6"}],["$","path","1tidbn",{"d":"M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2"}],["$","path","13wd9y",{"d":"M8.65 22c.21-.66.45-1.32.57-2"}],["$","path","1fr1j5",{"d":"M9 6.8a6 6 0 0 1 9 5.2v2"}],"$undefined"]}],["$","h2",null,{"className":"text-xl font-semibold text-gray-900","children":"Biometria Digital"}]]}]
e:["$","$L14",null,{"type":"fingerprint"}]
f:["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":[["$","div",null,{"className":"flex items-center space-x-2 mb-6","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-camera h-6 w-6 text-green-600","aria-hidden":"true","children":[["$","path","1tc9qg",{"d":"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"}],["$","circle","1vg3eu",{"cx":"12","cy":"13","r":"3"}],"$undefined"]}],["$","h2",null,{"className":"text-xl font-semibold text-gray-900","children":"Reconhecimento Facial"}]]}],["$","$L14",null,{"type":"facial"}]]}]
10:["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":[["$","div",null,{"className":"flex items-center space-x-2 mb-6","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-6 w-6 text-gray-600","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}],["$","h2",null,{"className":"text-xl font-semibold text-gray-900","children":"Registros Recentes"}]]}],["$","$b",null,{"fallback":["$","div",null,{"className":"space-y-3","children":[["$","div","0",{"className":"flex items-center space-x-4 p-4 border rounded-lg","children":[["$","div",null,{"className":"w-12 h-12 bg-gray-200 rounded-full animate-pulse"}],["$","div",null,{"className":"flex-1 space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded animate-pulse w-1/4"}],["$","div",null,{"className":"h-3 bg-gray-200 rounded animate-pulse w-1/3"}]]}],["$","div",null,{"className":"w-20 h-8 bg-gray-200 rounded animate-pulse"}]]}],["$","div","1",{"className":"flex items-center space-x-4 p-4 border rounded-lg","children":[["$","div",null,{"className":"w-12 h-12 bg-gray-200 rounded-full animate-pulse"}],["$","div",null,{"className":"flex-1 space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded animate-pulse w-1/4"}],["$","div",null,{"className":"h-3 bg-gray-200 rounded animate-pulse w-1/3"}]]}],["$","div",null,{"className":"w-20 h-8 bg-gray-200 rounded animate-pulse"}]]}],["$","div","2",{"className":"flex items-center space-x-4 p-4 border rounded-lg","children":[["$","div",null,{"className":"w-12 h-12 bg-gray-200 rounded-full animate-pulse"}],["$","div",null,{"className":"flex-1 space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded animate-pulse w-1/4"}],["$","div",null,{"className":"h-3 bg-gray-200 rounded animate-pulse w-1/3"}]]}],["$","div",null,{"className":"w-20 h-8 bg-gray-200 rounded animate-pulse"}]]}]]}],"children":["$","$L15",null,{}]}]]}]
11:["$","$L16",null,{"children":["$L17",["$","$L18",null,{"promise":"$@19"}]]}]
12:["$","$1","h",{"children":[null,[["$","$L1a",null,{"children":"$L1b"}],null],["$","$L1c",null,{"children":["$","div",null,{"hidden":true,"children":["$","$b",null,{"fallback":null,"children":"$L1d"}]}]}]]}]
1b:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
17:null
1e:I[8175,[],"IconMark"]
19:{"metadata":[["$","title","0",{"children":"Ponto Biométrico - RLPONTO"}],["$","meta","1",{"name":"description","content":"Registro de ponto através de biometria"}],["$","meta","2",{"name":"author","content":"Your Name"}],["$","meta","3",{"name":"keywords","content":"Next.js,React,TypeScript,Tailwind CSS"}],["$","meta","4",{"name":"creator","content":"Your Name"}],["$","meta","5",{"name":"robots","content":"index, follow"}],["$","meta","6",{"property":"og:title","content":"My App"}],["$","meta","7",{"property":"og:description","content":"A professional Next.js application"}],["$","meta","8",{"property":"og:url","content":"http://************"}],["$","meta","9",{"property":"og:site_name","content":"My App"}],["$","meta","10",{"property":"og:locale","content":"en_US"}],["$","meta","11",{"property":"og:type","content":"website"}],["$","meta","12",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","13",{"name":"twitter:title","content":"My App"}],["$","meta","14",{"name":"twitter:description","content":"A professional Next.js application"}],["$","link","15",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L1e","16",{}]],"error":null,"digest":"$undefined"}
1d:"$19:metadata"
