(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7103],{7103:function(t,e,r){(function(){"use strict";function t(t){var e=0;return function(){return e<t.length?{done:!1,value:t[e++]}:{done:!0}}}var e,n="function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,r){return t==Array.prototype||t==Object.prototype||(t[e]=r.value),t},i=function(t){t=["object"==typeof globalThis&&globalThis,t,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof r.g&&r.g];for(var e=0;e<t.length;++e){var n=t[e];if(n&&n.Math==Math)return n}throw Error("Cannot find global object")}(this);function o(t,e){if(e)t:{var r=i;t=t.split(".");for(var o=0;o<t.length-1;o++){var a=t[o];if(!(a in r))break t;r=r[a]}(e=e(o=r[t=t[t.length-1]]))!=o&&null!=e&&n(r,t,{configurable:!0,writable:!0,value:e})}}function a(e){var r="undefined"!=typeof Symbol&&Symbol.iterator&&e[Symbol.iterator];return r?r.call(e):{next:t(e)}}function s(t){if(!(t instanceof Array)){t=a(t);for(var e,r=[];!(e=t.next()).done;)r.push(e.value);t=r}return t}o("Symbol",function(t){function e(t,e){this.g=t,n(this,"description",{configurable:!0,writable:!0,value:e})}if(t)return t;e.prototype.toString=function(){return this.g};var r="jscomp_symbol_"+(1e9*Math.random()>>>0)+"_",i=0;return function t(n){if(this instanceof t)throw TypeError("Symbol is not a constructor");return new e(r+(n||"")+"_"+i++,n)}}),o("Symbol.iterator",function(e){if(e)return e;e=Symbol("Symbol.iterator");for(var r="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),o=0;o<r.length;o++){var a=i[r[o]];"function"==typeof a&&"function"!=typeof a.prototype[e]&&n(a.prototype,e,{configurable:!0,writable:!0,value:function(){var e;return(e={next:e=t(this)})[Symbol.iterator]=function(){return this},e}})}return e});var u,c,f="function"==typeof Object.create?Object.create:function(t){function e(){}return e.prototype=t,new e};if("function"==typeof Object.setPrototypeOf)c=Object.setPrototypeOf;else{t:{var l={};try{l.__proto__={a:!0},T=l.a;break t}catch(t){}T=!1}c=T?function(t,e){if(t.__proto__=e,t.__proto__!==e)throw TypeError(t+" is not extensible");return t}:null}var h=c;function g(t,e){if(t.prototype=f(e.prototype),t.prototype.constructor=t,h)h(t,e);else for(var r in e)if("prototype"!=r)if(Object.defineProperties){var n=Object.getOwnPropertyDescriptor(e,r);n&&Object.defineProperty(t,r,n)}else t[r]=e[r];t.ea=e.prototype}function p(){this.l=!1,this.i=null,this.h=void 0,this.g=1,this.s=this.m=0,this.j=null}function d(t){if(t.l)throw TypeError("Generator is already running");t.l=!0}function v(t,e){t.j={U:e,V:!0},t.g=t.m||t.s}function y(t,e,r){return t.g=r,{value:e}}function m(t){this.g=new p,this.h=t}function b(t,e,r,n){try{var i=e.call(t.g.i,r);if(!(i instanceof Object))throw TypeError("Iterator result "+i+" is not an object");if(!i.done)return t.g.l=!1,i;var o=i.value}catch(e){return t.g.i=null,v(t.g,e),w(t)}return t.g.i=null,n.call(t.g,o),w(t)}function w(t){for(;t.g.g;)try{var e=t.h(t.g);if(e)return t.g.l=!1,{value:e.value,done:!1}}catch(e){t.g.h=void 0,v(t.g,e)}if(t.g.l=!1,t.g.j){if(e=t.g.j,t.g.j=null,e.V)throw e.U;return{value:e.return,done:!0}}return{value:void 0,done:!0}}function A(t){this.next=function(e){return d(t.g),t.g.i?e=b(t,t.g.i.next,e,t.g.o):(t.g.o(e),e=w(t)),e},this.throw=function(e){return d(t.g),t.g.i?e=b(t,t.g.i.throw,e,t.g.o):(v(t.g,e),e=w(t)),e},this.return=function(e){var r;return d(t.g),(r=t.g.i)?b(t,"return"in r?r.return:function(t){return{value:t,done:!0}},e,t.g.return):(t.g.return(e),w(t))},this[Symbol.iterator]=function(){return this}}function _(t,e){return e=new A(new m(e)),h&&t.prototype&&h(e,t.prototype),e}p.prototype.o=function(t){this.h=t},p.prototype.return=function(t){this.j={return:t},this.g=this.s};var x="function"==typeof Object.assign?Object.assign:function(t,e){for(var r=1;r<arguments.length;r++){var n=arguments[r];if(n)for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t};o("Object.assign",function(t){return t||x}),o("Promise",function(t){function e(t){this.h=0,this.i=void 0,this.g=[],this.o=!1;var e=this.j();try{t(e.resolve,e.reject)}catch(t){e.reject(t)}}function r(){this.g=null}function n(t){return t instanceof e?t:new e(function(e){e(t)})}if(t)return t;r.prototype.h=function(t){if(null==this.g){this.g=[];var e=this;this.i(function(){e.l()})}this.g.push(t)};var o=i.setTimeout;r.prototype.i=function(t){o(t,0)},r.prototype.l=function(){for(;this.g&&this.g.length;){var t=this.g;this.g=[];for(var e=0;e<t.length;++e){var r=t[e];t[e]=null;try{r()}catch(t){this.j(t)}}}this.g=null},r.prototype.j=function(t){this.i(function(){throw t})},e.prototype.j=function(){function t(t){return function(n){r||(r=!0,t.call(e,n))}}var e=this,r=!1;return{resolve:t(this.C),reject:t(this.l)}},e.prototype.C=function(t){if(t===this)this.l(TypeError("A Promise cannot resolve to itself"));else if(t instanceof e)this.F(t);else{switch(typeof t){case"object":var r=null!=t;break;case"function":r=!0;break;default:r=!1}r?this.u(t):this.m(t)}},e.prototype.u=function(t){var e=void 0;try{e=t.then}catch(t){this.l(t);return}"function"==typeof e?this.G(e,t):this.m(t)},e.prototype.l=function(t){this.s(2,t)},e.prototype.m=function(t){this.s(1,t)},e.prototype.s=function(t,e){if(0!=this.h)throw Error("Cannot settle("+t+", "+e+"): Promise already settled in state"+this.h);this.h=t,this.i=e,2===this.h&&this.D(),this.A()},e.prototype.D=function(){var t=this;o(function(){if(t.B()){var e=i.console;void 0!==e&&e.error(t.i)}},1)},e.prototype.B=function(){if(this.o)return!1;var t=i.CustomEvent,e=i.Event,r=i.dispatchEvent;return void 0===r||("function"==typeof t?t=new t("unhandledrejection",{cancelable:!0}):"function"==typeof e?t=new e("unhandledrejection",{cancelable:!0}):(t=i.document.createEvent("CustomEvent")).initCustomEvent("unhandledrejection",!1,!0,t),t.promise=this,t.reason=this.i,r(t))},e.prototype.A=function(){if(null!=this.g){for(var t=0;t<this.g.length;++t)s.h(this.g[t]);this.g=null}};var s=new r;return e.prototype.F=function(t){var e=this.j();t.J(e.resolve,e.reject)},e.prototype.G=function(t,e){var r=this.j();try{t.call(e,r.resolve,r.reject)}catch(t){r.reject(t)}},e.prototype.then=function(t,r){function n(t,e){return"function"==typeof t?function(e){try{i(t(e))}catch(t){o(t)}}:e}var i,o,a=new e(function(t,e){i=t,o=e});return this.J(n(t,i),n(r,o)),a},e.prototype.catch=function(t){return this.then(void 0,t)},e.prototype.J=function(t,e){function r(){switch(n.h){case 1:t(n.i);break;case 2:e(n.i);break;default:throw Error("Unexpected state: "+n.h)}}var n=this;null==this.g?s.h(r):this.g.push(r),this.o=!0},e.resolve=n,e.reject=function(t){return new e(function(e,r){r(t)})},e.race=function(t){return new e(function(e,r){for(var i=a(t),o=i.next();!o.done;o=i.next())n(o.value).J(e,r)})},e.all=function(t){var r=a(t),i=r.next();return i.done?n([]):new e(function(t,e){var o=[],a=0;do o.push(void 0),a++,n(i.value).J(function(e){return function(r){o[e]=r,0==--a&&t(o)}}(o.length-1),e),i=r.next();while(!i.done)})},e}),o("Object.is",function(t){return t||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}}),o("Array.prototype.includes",function(t){return t||function(t,e){var r=this;r instanceof String&&(r=String(r));var n=r.length;for(0>(e=e||0)&&(e=Math.max(e+n,0));e<n;e++){var i=r[e];if(i===t||Object.is(i,t))return!0}return!1}}),o("String.prototype.includes",function(t){return t||function(t,e){if(null==this)throw TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(t instanceof RegExp)throw TypeError("First argument to String.prototype.includes must not be a regular expression");return -1!==this.indexOf(t,e||0)}}),o("Array.prototype.keys",function(t){return t||function(){var t,e,r,n,i;return t=this,e=function(t){return t},t instanceof String&&(t+=""),r=0,n=!1,(i={next:function(){if(!n&&r<t.length){var i=r++;return{value:e(i,t[i]),done:!1}}return n=!0,{done:!0,value:void 0}}})[Symbol.iterator]=function(){return i},i}});var E=this||self;function j(t,e){t=t.split(".");var r,n=E;for((t[0]in n)||void 0===n.execScript||n.execScript("var "+t[0]);t.length&&(r=t.shift());)t.length||void 0===e?n=n[r]&&n[r]!==Object.prototype[r]?n[r]:n[r]={}:n[r]=e}function k(t,e){return e=String.fromCharCode.apply(null,e),null==t?e:t+e}var T,O,F,R="undefined"!=typeof TextDecoder,S="undefined"!=typeof TextEncoder;function C(t){if(S)t=(F||(F=new TextEncoder)).encode(t);else{var e=void 0;e=void 0!==e&&e;for(var r=0,n=new Uint8Array(3*t.length),i=0;i<t.length;i++){var o=t.charCodeAt(i);if(128>o)n[r++]=o;else{if(2048>o)n[r++]=o>>6|192;else{if(55296<=o&&57343>=o){if(56319>=o&&i<t.length){var a=t.charCodeAt(++i);if(56320<=a&&57343>=a){o=1024*(o-55296)+a-56320+65536,n[r++]=o>>18|240,n[r++]=o>>12&63|128,n[r++]=o>>6&63|128,n[r++]=63&o|128;continue}i--}if(e)throw Error("Found an unpaired surrogate");o=65533}n[r++]=o>>12|224,n[r++]=o>>6&63|128}n[r++]=63&o|128}}t=n.subarray(0,r)}return t}var L={},P=null;function M(t,e){void 0===e&&(e=0),B(),e=L[e];for(var r=Array(Math.floor(t.length/3)),n=e[64]||"",i=0,o=0;i<t.length-2;i+=3){var a=t[i],s=t[i+1],u=t[i+2],c=e[a>>2];a=e[(3&a)<<4|s>>4],s=e[(15&s)<<2|u>>6],u=e[63&u],r[o++]=c+a+s+u}switch(c=0,u=n,t.length-i){case 2:u=e[(15&(c=t[i+1]))<<2]||n;case 1:t=t[i],r[o]=e[t>>2]+e[(3&t)<<4|c>>4]+u+n}return r.join("")}function B(){if(!P){P={};for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),e=["+/=","+/","-_=","-_.","-_"],r=0;5>r;r++){var n=t.concat(e[r].split(""));L[r]=n;for(var i=0;i<n.length;i++){var o=n[i];void 0===P[o]&&(P[o]=i)}}}}var U,N="function"==typeof Uint8Array.prototype.slice;function I(t,e,r){return e===r?U||(U=new Uint8Array(0)):N?t.slice(e,r):new Uint8Array(t.subarray(e,r))}var D=0,G=0;function H(t,e){e=void 0!==(e=void 0===e?{}:e).v&&e.v,this.h=null,this.g=this.j=this.l=0,this.m=!1,this.v=e,t&&V(this,t)}function V(t,e){var r,n,i,o,a;t.h=e=e.constructor===Uint8Array?e:e.constructor===ArrayBuffer||e.constructor===Array?new Uint8Array(e):e.constructor===String?((i=3*(n=(r=e).length)/4)%3?i=Math.floor(i):-1!="=.".indexOf(r[n-1])&&(i=-1!="=.".indexOf(r[n-2])?i-2:i-1),o=new Uint8Array(i),a=0,function(t,e){function r(e){for(;n<t.length;){var r=t.charAt(n++),i=P[r];if(null!=i)return i;if(!/^[\s\xa0]*$/.test(r))throw Error("Unknown base64 encoding at char: "+r)}return e}B();for(var n=0;;){var i=r(-1),o=r(0),a=r(64),s=r(64);if(64===s&&-1===i)break;e(i<<2|o>>4),64!=a&&(e(o<<4&240|a>>2),64!=s&&e(a<<6&192|s))}}(r,function(t){o[a++]=t}),o.subarray(0,a)):e instanceof Uint8Array?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):new Uint8Array(0),t.l=0,t.j=t.h.length,t.g=t.l}function W(t){for(var e=128,r=0,n=0,i=0;4>i&&128<=e;i++)r|=(127&(e=t.h[t.g++]))<<7*i;if(128<=e&&(r|=(127&(e=t.h[t.g++]))<<28,n|=(127&e)>>4),128<=e)for(i=0;5>i&&128<=e;i++)n|=(127&(e=t.h[t.g++]))<<7*i+3;if(128>e)return t=r>>>0,(n=0x80000000&(e=n>>>0))&&(e=~e>>>0,0==(t=~t+1>>>0)&&(e=e+1>>>0)),t=0x100000000*e+(t>>>0),n?-t:t;t.m=!0}H.prototype.reset=function(){this.g=this.l},H.prototype.i=function(){var t=this.h,e=t[this.g],r=127&e;return 128>e?(this.g+=1,r):(r|=(127&(e=t[this.g+1]))<<7,128>e)?(this.g+=2,r):(r|=(127&(e=t[this.g+2]))<<14,128>e)?(this.g+=3,r):(r|=(127&(e=t[this.g+3]))<<21,128>e)?(this.g+=4,r):(r|=(15&(e=t[this.g+4]))<<28,128>e)?(this.g+=5,r>>>0):(this.g+=5,128<=t[this.g++]&&128<=t[this.g++]&&128<=t[this.g++]&&128<=t[this.g++]&&this.g++,r)},H.prototype.o=function(){var t=this.h[this.g],e=this.h[this.g+1],r=this.h[this.g+2],n=this.h[this.g+3];return this.g+=4,t=2*((r=(0|t|e<<8|r<<16|n<<24)>>>0)>>31)+1,e=r>>>23&255,r&=8388607,255==e?r?NaN:1/0*t:0==e?1401298464324817e-60*t*r:t*Math.pow(2,e-150)*(r+8388608)};var X=[];function Y(){this.g=new Uint8Array(64),this.h=0}function z(t,e){for(;127<e;)t.push(127&e|128),e>>>=7;t.push(e)}function J(t){var e={},r=void 0!==e.N&&e.N;this.o={v:void 0!==e.v&&e.v},this.N=r,e=this.o,X.length?(r=X.pop(),e&&(r.v=e.v),t&&V(r,t),t=r):t=new H(t,e),this.g=t,this.m=this.g.g,this.h=this.i=this.l=-1,this.j=!1}function K(t){var e=t.g;if((e=e.g==e.j)||(e=t.j)||(e=(e=t.g).m||0>e.g||e.g>e.j),e)return!1;t.m=t.g.g;var r=7&(e=t.g.i());return 0!=r&&5!=r&&1!=r&&2!=r&&3!=r&&4!=r?(t.j=!0,!1):(t.i=e,t.l=e>>>3,t.h=r,!0)}function $(t,e,r){var n=t.g.j,i=t.g.i(),o=t.g.g+i;if(t.g.j=o,r(e,t),0!=(r=o-t.g.g))throw Error("Message parsing ended unexpectedly. Expected to read "+i+" bytes, instead read "+(i-r)+" bytes, either the data ended unexpectedly or the message misreported its own length");return t.g.g=o,t.g.j=n,e}function Z(t){return t.g.o()}function q(t){var e=t.g.i(),r=(t=t.g).g;if(t.g+=e,t=t.h,R)(n=O)||(n=O=new TextDecoder("utf-8",{fatal:!1})),n=n.decode(t.subarray(r,r+e));else{e=r+e;for(var n,i,o,a,s=[],u=null;r<e;)128>(i=t[r++])?s.push(i):224>i?r>=e?s.push(65533):(o=t[r++],194>i||128!=(192&o)?(r--,s.push(65533)):s.push((31&i)<<6|63&o)):240>i?r>=e-1?s.push(65533):128!=(192&(o=t[r++]))||224===i&&160>o||237===i&&160<=o||128!=(192&(n=t[r++]))?(r--,s.push(65533)):s.push((15&i)<<12|(63&o)<<6|63&n):244>=i?r>=e-2?s.push(65533):128!=(192&(o=t[r++]))||0!=(i<<28)+(o-144)>>30||128!=(192&(n=t[r++]))||128!=(192&(a=t[r++]))?(r--,s.push(65533)):(i=((7&i)<<18|(63&o)<<12|(63&n)<<6|63&a)-65536,s.push((i>>10&1023)+55296,(1023&i)+56320)):s.push(65533),8192<=s.length&&(u=k(u,s),s.length=0);n=k(u,s)}return n}function Q(t,e,r){var n=t.g.i();for(n=t.g.g+n;t.g.g<n;)r.push(e.call(t.g))}function tt(t,e){2==t.h?Q(t,H.prototype.o,e):e.push(Z(t))}function te(){this.h=[],this.i=0,this.g=new Y}function tr(t,e){0!==e.length&&(t.h.push(e),t.i+=e.length)}function tn(t){var e=t.i+t.g.length();if(0===e)return new Uint8Array(0);e=new Uint8Array(e);for(var r=t.h,n=r.length,i=0,o=0;o<n;o++){var a=r[o];0!==a.length&&(e.set(a,i),i+=a.length)}return 0!==(n=(r=t.g).h)&&(e.set(r.g.subarray(0,n),i),r.h=0),t.h=[e],e}function ti(t,e,r){if(null!=r){z(t.g,8*e+5),t=t.g;var n=r;0===(n=(r=+(0>n))?-n:n)?0<1/n?D=G=0:(G=0,D=0x80000000):isNaN(n)?(G=0,D=0x7fffffff):34028234663852886e22<n?(G=0,D=(r<<31|0x7f800000)>>>0):11754943508222875e-54>n?(G=0,D=(r<<31|(n=Math.round(n/1401298464324817e-60)))>>>0):(e=Math.floor(Math.log(n)/Math.LN2),n*=Math.pow(2,-e),0x1000000<=(n=Math.round(8388608*n))&&++e,G=0,D=(r<<31|e+127<<23|8388607&n)>>>0),r=D,t.push(r>>>0&255),t.push(r>>>8&255),t.push(r>>>16&255),t.push(r>>>24&255)}}Y.prototype.push=function(t){if(!(this.h+1<this.g.length)){var e=this.g;this.g=new Uint8Array(Math.ceil(1+2*this.g.length)),this.g.set(e)}this.g[this.h++]=t},Y.prototype.length=function(){return this.h},Y.prototype.end=function(){var t=this.g,e=this.h;return this.h=0,I(t,0,e)},J.prototype.reset=function(){this.g.reset(),this.h=this.l=-1};var to="function"==typeof Uint8Array;function ta(t,e,r){if(null!=t)return"object"==typeof t?to&&t instanceof Uint8Array?r(t):ts(t,e,r):e(t)}function ts(t,e,r){if(Array.isArray(t)){for(var n=Array(t.length),i=0;i<t.length;i++)n[i]=ta(t[i],e,r);return Array.isArray(t)&&t.W&&tf(n),n}for(i in n={},t)n[i]=ta(t[i],e,r);return n}function tu(t){return"number"==typeof t?isFinite(t)?t:String(t):t}var tc={W:{value:!0,configurable:!0}};function tf(t){return Array.isArray(t)&&!Object.isFrozen(t)&&Object.defineProperties(t,tc),t}function tl(t,r,n){var i=e;e=null,t||(t=i),i=this.constructor.ca,t||(t=i?[i]:[]),this.j=i?0:-1,this.m=this.g=null,this.h=t;t:{if(t=(i=this.h.length)-1,i&&!(null===(i=this.h[t])||"object"!=typeof i||Array.isArray(i)||to&&i instanceof Uint8Array)){this.l=t-this.j,this.i=i;break t}void 0!==r&&-1<r?(this.l=Math.max(r,t+1-this.j),this.i=null):this.l=Number.MAX_VALUE}if(n)for(r=0;r<n.length;r++)(t=n[r])<this.l?(t+=this.j,(i=this.h[t])?tf(i):this.h[t]=th):(tg(this),(i=this.i[t])?tf(i):this.i[t]=th)}var th=Object.freeze(tf([]));function tg(t){var e=t.l+t.j;t.h[e]||(t.i=t.h[e]={})}function tp(t,e,r){return -1===e?null:(void 0===r?0:r)||e>=t.l?t.i?t.i[e]:void 0:t.h[e+t.j]}function td(t,e){var r=void 0!==r&&r,n=tp(t,e,r);return null==n&&(n=th),n===th&&tb(t,e,n=tf([]),r),n}function tv(t){var e=td(t,3);if(t.m||(t.m={}),!t.m[3]){for(var r=0;r<e.length;r++)e[r]=+e[r];t.m[3]=!0}return e}function ty(t,e,r){return null==(t=tp(t,e))?r:t}function tm(t,e,r){return null==(t=null==(t=tp(t,e))?t:+t)?void 0===r?0:r:t}function tb(t,e,r,n){(void 0===n?0:n)||e>=t.l?(tg(t),t.i[e]=r):t.h[e+t.j]=r}function tw(t,e,r){if(-1===r)return null;if(t.g||(t.g={}),!t.g[r]){var n=tp(t,r,!1);n&&(t.g[r]=new e(n))}return t.g[r]}function tA(t,e){t.g||(t.g={});var r=t.g[1];if(!r){var n=td(t,1);r=[];for(var i=0;i<n.length;i++)r[i]=new e(n[i]);t.g[1]=r}return r}function t_(t,e,r){var n=void 0!==n&&n;t.g||(t.g={});var i=r?tE(r,!1):r;t.g[e]=r,tb(t,e,i,n)}function tx(t,e,r,n){var i=tA(t,r);e=e||new r,t=td(t,1),void 0!=n?(i.splice(n,0,e),t.splice(n,0,tE(e,!1))):(i.push(e),t.push(tE(e,!1)))}function tE(t,e){if(t.g)for(var r in t.g){var n=t.g[r];if(Array.isArray(n))for(var i=0;i<n.length;i++)n[i]&&tE(n[i],e);else n&&tE(n,e)}return t.h}function tj(t,e){if(t=t.o){tr(e,e.g.end());for(var r=0;r<t.length;r++)tr(e,t[r])}}function tk(t,e){if(4==e.h)return!1;var r=e.m;return!function t(e){switch(e.h){case 0:if(0!=e.h)t(e);else{for(e=e.g;128&e.h[e.g];)e.g++;e.g++}break;case 1:1!=e.h?t(e):(e=e.g,e.g+=8);break;case 2:if(2!=e.h)t(e);else{var r=e.g.i();e=e.g,e.g+=r}break;case 5:5!=e.h?t(e):(e=e.g,e.g+=4);break;case 3:for(r=e.l;;){if(!K(e)){e.j=!0;break}if(4==e.h){e.l!=r&&(e.j=!0);break}t(e)}break;default:e.j=!0}}(e),e.N||(e=I(e.g.h,r,e.g.g),(r=t.o)?r.push(e):t.o=[e]),!0}function tT(t){tl.call(this,t,-1,tF)}function tO(t,e){for(;K(e);)switch(e.i){case 8:var r=e.g.i();tb(t,1,r);break;case 16:tb(t,2,r=e.g.i());break;case 29:case 26:tt(e,t.getPackedDataList());break;case 32:tb(t,4,r=W(e.g));break;default:if(!tk(t,e))return t}return t}tl.prototype.toJSON=function(){return ts(tE(this,!1),tu,M)},tl.prototype.toString=function(){return tE(this,!1).toString()},g(tT,tl),tT.prototype.getRows=function(){return tp(this,1)},tT.prototype.getCols=function(){return tp(this,2)},tT.prototype.getPackedDataList=function(){return tv(this)},tT.prototype.getLayout=function(){return ty(this,4,0)};var tF=[3];function tR(t,e){var r=void 0;return new(r||(r=Promise))(function(n,i){function o(t){try{s(e.next(t))}catch(t){i(t)}}function a(t){try{s(e.throw(t))}catch(t){i(t)}}function s(t){t.done?n(t.value):new r(function(e){e(t.value)}).then(o,a)}s((e=e.apply(t,void 0)).next())})}function tS(t){tl.call(this,t)}function tC(t,e){for(;K(e);)switch(e.i){case 8:var r=e.g.i();tb(t,1,r);break;case 21:tb(t,2,r=Z(e));break;case 26:tb(t,3,r=q(e));break;case 34:tb(t,4,r=q(e));break;default:if(!tk(t,e))return t}return t}function tL(t){tl.call(this,t,-1,tP)}g(tS,tl),g(tL,tl),tL.prototype.addClassification=function(t,e){return tx(this,t,tS,e),this};var tP=[1];function tM(t){tl.call(this,t)}function tB(t,e){for(;K(e);)switch(e.i){case 13:var r=Z(e);tb(t,1,r);break;case 21:tb(t,2,r=Z(e));break;case 29:tb(t,3,r=Z(e));break;case 37:tb(t,4,r=Z(e));break;case 45:tb(t,5,r=Z(e));break;default:if(!tk(t,e))return t}return t}function tU(t){tl.call(this,t,-1,tI)}function tN(t){t:{var e=new tU;for(t=new J(t);K(t);)if(10===t.i)tx(e,$(t,new tM,tB),tM,void 0);else if(!tk(e,t))break t}return e}g(tM,tl),g(tU,tl);var tI=[1];function tD(t){tl.call(this,t)}function tG(t){tl.call(this,t,-1,tV)}function tH(t,e){for(;K(e);)switch(e.i){case 8:var r=W(e.g);tb(t,1,r);break;case 16:tb(t,2,r=W(e.g));break;case 29:case 26:tt(e,t.getVertexBufferList());break;case 32:case 34:r=e;var n=t.getIndexBufferList();2==r.h?Q(r,H.prototype.i,n):n.push(r.g.i());break;default:if(!tk(t,e))return t}return t}g(tD,tl),g(tG,tl),tG.prototype.getVertexType=function(){return ty(this,1,0)},tG.prototype.getPrimitiveType=function(){return ty(this,2,0)},tG.prototype.getVertexBufferList=function(){return tv(this)},tG.prototype.getIndexBufferList=function(){return td(this,4)};var tV=[3,4];function tW(t){tl.call(this,t)}function tX(t,e,r){if(r=t.createShader(0===r?t.VERTEX_SHADER:t.FRAGMENT_SHADER),t.shaderSource(r,e),t.compileShader(r),!t.getShaderParameter(r,t.COMPILE_STATUS))throw Error("Could not compile WebGL shader.\n\n"+t.getShaderInfoLog(r));return r}function tY(t){return{x:tm(t,1),y:tm(t,2),z:tm(t,3),visibility:null!=tp(t,4)?tm(t,4):void 0}}function tz(t,e){this.h=t,this.g=e,this.l=0}function tJ(t,e,r){return(function(t,e){var r=t.g;if(void 0===t.m){var n=tX(r,"\n  attribute vec2 aVertex;\n  attribute vec2 aTex;\n  varying vec2 vTex;\n  void main(void) {\n    gl_Position = vec4(aVertex, 0.0, 1.0);\n    vTex = aTex;\n  }",0),i=tX(r,"\n  precision mediump float;\n  varying vec2 vTex;\n  uniform sampler2D sampler0;\n  void main(){\n    gl_FragColor = texture2D(sampler0, vTex);\n  }",1),o=r.createProgram();if(r.attachShader(o,n),r.attachShader(o,i),r.linkProgram(o),!r.getProgramParameter(o,r.LINK_STATUS))throw Error("Could not compile WebGL program.\n\n"+r.getProgramInfoLog(o));n=t.m=o,r.useProgram(n),i=r.getUniformLocation(n,"sampler0"),t.j={I:r.getAttribLocation(n,"aVertex"),H:r.getAttribLocation(n,"aTex"),da:i},t.s=r.createBuffer(),r.bindBuffer(r.ARRAY_BUFFER,t.s),r.enableVertexAttribArray(t.j.I),r.vertexAttribPointer(t.j.I,2,r.FLOAT,!1,0,0),r.bufferData(r.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),r.STATIC_DRAW),r.bindBuffer(r.ARRAY_BUFFER,null),t.o=r.createBuffer(),r.bindBuffer(r.ARRAY_BUFFER,t.o),r.enableVertexAttribArray(t.j.H),r.vertexAttribPointer(t.j.H,2,r.FLOAT,!1,0,0),r.bufferData(r.ARRAY_BUFFER,new Float32Array([0,1,0,0,1,0,1,1]),r.STATIC_DRAW),r.bindBuffer(r.ARRAY_BUFFER,null),r.uniform1i(i,0)}n=t.j,r.useProgram(t.m),r.canvas.width=e.width,r.canvas.height=e.height,r.viewport(0,0,e.width,e.height),r.activeTexture(r.TEXTURE0),t.h.bindTexture2d(e.glName),r.enableVertexAttribArray(n.I),r.bindBuffer(r.ARRAY_BUFFER,t.s),r.vertexAttribPointer(n.I,2,r.FLOAT,!1,0,0),r.enableVertexAttribArray(n.H),r.bindBuffer(r.ARRAY_BUFFER,t.o),r.vertexAttribPointer(n.H,2,r.FLOAT,!1,0,0),r.bindFramebuffer(r.DRAW_FRAMEBUFFER?r.DRAW_FRAMEBUFFER:r.FRAMEBUFFER,null),r.clearColor(0,0,0,0),r.clear(r.COLOR_BUFFER_BIT),r.colorMask(!0,!0,!0,!0),r.drawArrays(r.TRIANGLE_FAN,0,4),r.disableVertexAttribArray(n.I),r.disableVertexAttribArray(n.H),r.bindBuffer(r.ARRAY_BUFFER,null),t.h.bindTexture2d(0)}(t,e),"function"==typeof t.g.canvas.transferToImageBitmap)?Promise.resolve(t.g.canvas.transferToImageBitmap()):r?Promise.resolve(t.g.canvas):"function"==typeof createImageBitmap?createImageBitmap(t.g.canvas):(void 0===t.i&&(t.i=document.createElement("canvas")),new Promise(function(e){t.i.height=t.g.canvas.height,t.i.width=t.g.canvas.width,t.i.getContext("2d",{}).drawImage(t.g.canvas,0,0,t.g.canvas.width,t.g.canvas.height),e(t.i)}))}function tK(t){this.g=t}g(tW,tl),tW.prototype.getMesh=function(){return tw(this,tG,1)},tW.prototype.getPoseTransformMatrix=function(){return tw(this,tT,2)};var t$=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,9,1,7,0,65,0,253,15,26,11]);function tZ(t,e){return e+t}function tq(t){if(this.g=t,this.listeners={},this.j={},this.F={},this.m={},this.s={},this.G=this.o=this.R=!0,this.C=Promise.resolve(),this.P="",this.B={},this.locateFile=t&&t.locateFile||tZ,"object"==typeof window)var e=window.location.pathname.toString().substring(0,window.location.pathname.toString().lastIndexOf("/"))+"/";else if("undefined"!=typeof location)e=location.pathname.toString().substring(0,location.pathname.toString().lastIndexOf("/"))+"/";else throw Error("solutions can only be loaded on a web page or in a web worker");if(this.S=e,t.options){e=a(Object.keys(t.options));for(var r=e.next();!r.done;r=e.next()){r=r.value;var n=t.options[r].default;void 0!==n&&(this.j[r]="function"==typeof n?n():n)}}}function tQ(t,e){return tR(t,function t(){var r,n=this;return _(t,function(t){return e in n.F?t.return(n.F[e]):(r=fetch(n.locateFile(e,"")).then(function(t){return t.arrayBuffer()}),n.F[e]=r,t.return(r))})})}function t2(t){var e=(t=function(t){t:{var e=new tW;for(t=new J(t);K(t);)switch(t.i){case 10:var r=$(t,new tG,tH);t_(e,1,r);break;case 18:t_(e,2,r=$(t,new tT,tO));break;default:if(!tk(e,t))break t}}return e}(t)).getMesh();if(!e)return t;var r=new Float32Array(e.getVertexBufferList());e.getVertexBufferList=function(){return r};var n=new Uint32Array(e.getIndexBufferList());return e.getIndexBufferList=function(){return n},t}(u=tq.prototype).close=function(){return this.i&&this.i.delete(),Promise.resolve()},u.reset=function(){return tR(this,function t(){var e=this;return _(t,function(t){e.i&&(e.i.reset(),e.m={},e.s={}),t.g=0})})},u.setOptions=function(t,e){var r=this;if(e=e||this.g.options){for(var n=[],i=[],o={},s=a(Object.keys(t)),u=s.next();!u.done;o={K:o.K,L:o.L},u=s.next()){var c=u.value;c in this.j&&this.j[c]===t[c]||(this.j[c]=t[c],void 0!==(u=e[c])&&(u.onChange&&(o.K=u.onChange,o.L=t[c],n.push(function(t){return function(){return tR(r,function e(){var r=this;return _(e,function(e){if(1==e.g)return y(e,t.K(t.L),2);!0===e.h&&(r.o=!0),e.g=0})})}}(o))),u.graphOptionXref&&(c={valueNumber:1===u.type?t[c]:0,valueBoolean:0===u.type&&t[c],valueString:2===u.type?t[c]:""},u=Object.assign(Object.assign(Object.assign({},{calculatorName:"",calculatorIndex:0}),u.graphOptionXref),c),i.push(u))))}(0!==n.length||0!==i.length)&&(this.o=!0,this.A=(void 0===this.A?[]:this.A).concat(i),this.u=(void 0===this.u?[]:this.u).concat(n))}},u.initialize=function(){return tR(this,function t(){var e=this;return _(t,function(t){return 1==t.g?y(t,tR(e,function t(){var e,r,n,i,o,a,u,c,f,l,h,g=this;return _(t,function(t){var p,d,v;switch(t.g){case 1:if(e=g,!g.R)return t.return();return p=g.j,r=void 0===g.g.files?[]:"function"==typeof g.g.files?g.g.files(p):g.g.files,y(t,function(){return tR(this,function t(){return _(t,function(t){switch(t.g){case 1:return t.m=2,y(t,WebAssembly.instantiate(t$),4);case 4:t.g=3,t.m=0;break;case 2:return t.m=0,t.j=null,t.return(!1);case 3:return t.return(!0)}})})}(),2);case 2:if(n=t.h,"object"==typeof window)return d={locateFile:g.locateFile},window.createMediapipeSolutionsWasm=d,v={locateFile:g.locateFile},window.createMediapipeSolutionsPackedAssets=v,a=r.filter(function(t){return void 0!==t.data}),u=r.filter(function(t){return void 0===t.data}),c=Promise.all(a.map(function(t){var r=tQ(e,t.url);if(void 0!==t.path){var n=t.path;r=r.then(function(t){return e.overrideFile(n,t),Promise.resolve(t)})}return r})),f=Promise.all(u.map(function(t){var r,i;return void 0===t.simd||t.simd&&n||!t.simd&&!n?(r=e.locateFile(t.url,e.S),(i=document.createElement("script")).setAttribute("src",r),i.setAttribute("crossorigin","anonymous"),new Promise(function(t){i.addEventListener("load",function(){t()},!1),i.addEventListener("error",function(){t()},!1),document.body.appendChild(i)})):Promise.resolve()})).then(function(){return tR(e,function t(){var e=this;return _(t,function(t){if(1==t.g)return y(t,(0,window.createMediapipeSolutionsWasm)(window.createMediapipeSolutionsPackedAssets),2);e.h=t.h,t.g=0})})}),l=tR(e,function t(){var e=this;return _(t,function(t){return e.g.graph&&e.g.graph.url?t=y(t,tQ(e,e.g.graph.url),0):(t.g=0,t=void 0),t})}),y(t,Promise.all([f,c,l]),7);if("function"!=typeof importScripts)throw Error("solutions can only be loaded on a web page or in a web worker");return i=r.filter(function(t){return void 0===t.simd||t.simd&&n||!t.simd&&!n}).map(function(t){return e.locateFile(t.url,e.S)}),importScripts.apply(null,s(i)),y(t,createMediapipeSolutionsWasm(Module),6);case 6:g.h=t.h,g.l=new OffscreenCanvas(1,1),g.h.canvas=g.l,o=g.h.GL.createContext(g.l,{antialias:!1,alpha:!1,ba:"undefined"!=typeof WebGL2RenderingContext?2:1}),g.h.GL.makeContextCurrent(o),t.g=4;break;case 7:if(g.l=document.createElement("canvas"),!(h=g.l.getContext("webgl2",{}))&&!(h=g.l.getContext("webgl",{})))return alert("Failed to create WebGL canvas context when passing video frame."),t.return();g.D=h,g.h.canvas=g.l,g.h.createContext(g.l,!0,!0,{});case 4:g.i=new g.h.SolutionWasm,g.R=!1,t.g=0}})}),2):3!=t.g?y(t,tR(e,function t(){var e,r,n,i,o,u,c,f=this;return _(t,function(t){if(1==t.g){if(f.g.graph&&f.g.graph.url&&f.P===f.g.graph.url)return t.return();if(f.o=!0,!f.g.graph||!f.g.graph.url){t.g=2;return}return f.P=f.g.graph.url,y(t,tQ(f,f.g.graph.url),3)}for(2!=t.g&&(e=t.h,f.i.loadGraph(e)),n=(r=a(Object.keys(f.B))).next();!n.done;n=r.next())i=n.value,f.i.overrideFile(i,f.B[i]);if(f.B={},f.g.listeners)for(u=(o=a(f.g.listeners)).next();!u.done;u=o.next())!function(t,e){for(var r=e.name||"$",n=[].concat(s(e.wants)),i=new t.h.StringList,o=a(e.wants),u=o.next();!u.done;u=o.next())i.push_back(u.value);o=t.h.PacketListener.implement({onResults:function(i){for(var o,s={},u=0;u<e.wants.length;++u)s[n[u]]=i.get(u);var c=t.listeners[r];c&&(t.C=(o=e.outs,tR(t,function t(){var e,r,n,i,u,c,f,l,h,g,p,d,v,m=this;return _(t,function(t){switch(t.g){case 1:if(!o)return t.return(s);for(e={},r=0,i=(n=a(Object.keys(o))).next();!i.done;i=n.next())"string"!=typeof(u=o[i.value])&&"texture"===u.type&&void 0!==s[u.stream]&&++r;1<r&&(m.G=!1),i=(c=a(Object.keys(o))).next();case 2:if(i.done){t.g=4;break}if("string"==typeof(l=o[f=i.value]))return d=e,v=f,y(t,function(t,e,r){return tR(t,function t(){var n,i=this;return _(t,function(t){return"number"==typeof r||r instanceof Uint8Array||r instanceof i.h.Uint8BlobList?t.return(r):r instanceof i.h.Texture2dDataOut?((n=i.s[e])||(n=new tz(i.h,i.D),i.s[e]=n),t.return(tJ(n,r,i.G))):t.return(void 0)})})}(m,f,s[l]),14);if(h=s[l.stream],"detection_list"===l.type){if(h){for(var b=h.getRectList(),w=h.getLandmarksList(),A=h.getClassificationsList(),x=[],E=0;E<b.size();++E){var j=b.get(E);t:{var k=new tD;for(j=new J(j);K(j);)switch(j.i){case 13:var T=Z(j);tb(k,1,T);break;case 21:tb(k,2,T=Z(j));break;case 29:tb(k,3,T=Z(j));break;case 37:tb(k,4,T=Z(j));break;case 45:tb(k,5,T=Z(j));break;case 48:tb(k,6,T=W(j.g));break;default:if(!tk(k,j))break t}}k={Z:tm(k,1),$:tm(k,2),height:tm(k,3),width:tm(k,4),rotation:tm(k,5,0),X:ty(k,6,0)},j=tA(tN(w.get(E)),tM).map(tY);var O=A.get(E);for(T=new tL,O=new J(O);K(O);)if(10===O.i)T.addClassification($(O,new tS,tC));else if(!tk(T,O))break;k={T:k,O:j,M:tA(T,tS).map(function(t){return{index:ty(t,1,0),Y:tm(t,2),label:null!=tp(t,3)?ty(t,3,""):void 0,displayName:null!=tp(t,4)?ty(t,4,""):void 0}})},x.push(k)}b=x}else b=[];e[f]=b,t.g=7;break}if("proto_list"===l.type){if(h){for(w=0,b=Array(h.size());w<h.size();w++)b[w]=h.get(w);h.delete()}else b=[];e[f]=b,t.g=7;break}if(void 0===h){t.g=3;break}if("float_list"===l.type||"proto"===l.type){e[f]=h,t.g=7;break}if("texture"!==l.type)throw Error("Unknown output config type: '"+l.type+"'");return(g=m.s[f])||(g=new tz(m.h,m.D),m.s[f]=g),y(t,tJ(g,h,m.G),13);case 13:p=t.h,e[f]=p;case 7:l.transform&&e[f]&&(e[f]=l.transform(e[f])),t.g=3;break;case 14:d[v]=t.h;case 3:i=c.next(),t.g=2;break;case 4:return t.return(e)}})})).then(function(r){r=c(r);for(var i=0;i<e.wants.length;++i){var o=s[n[i]];"object"==typeof o&&o.hasOwnProperty&&o.hasOwnProperty("delete")&&o.delete()}r&&(t.C=r)}))}}),t.i.attachMultiListener(i,o),i.delete()}(f,u.value);c=f.j,f.j={},f.setOptions(c),t.g=0})}),3):y(t,tR(e,function t(){var e,r,n,i,o,s,u=this;return _(t,function(t){switch(t.g){case 1:if(!u.o)return t.return();if(!u.u){t.g=2;break}r=(e=a(u.u)).next();case 3:if(r.done){t.g=5;break}return y(t,(0,r.value)(),4);case 4:r=e.next(),t.g=3;break;case 5:u.u=void 0;case 2:if(u.A){for(n=new u.h.GraphOptionChangeRequestList,o=(i=a(u.A)).next();!o.done;o=i.next())s=o.value,n.push_back(s);u.i.changeOptions(n),n.delete(),u.A=void 0}u.o=!1,t.g=0}})}),0)})})},u.overrideFile=function(t,e){this.i?this.i.overrideFile(t,e):this.B[t]=e},u.clearOverriddenFiles=function(){this.B={},this.i&&this.i.clearOverriddenFiles()},u.send=function(t,e){return tR(this,function r(){var n,i,o,s,u,c,f,l,h,g=this;return _(r,function(r){switch(r.g){case 1:if(!g.g.inputs)return r.return();return n=1e3*(null==e?performance.now():e),y(r,g.C,2);case 2:return y(r,g.initialize(),3);case 3:for(i=new g.h.PacketDataList,s=(o=a(Object.keys(t))).next();!s.done;s=o.next())if(u=s.value,c=g.g.inputs[u]){t:{var p=g,d=t[u];switch(c.type){case"video":var v=p.m[c.stream];if(v||(v=new tz(p.h,p.D),p.m[c.stream]=v),0===(p=v).l&&(p.l=p.h.createTexture()),"undefined"!=typeof HTMLVideoElement&&d instanceof HTMLVideoElement){var m=d.videoWidth;v=d.videoHeight}else"undefined"!=typeof HTMLImageElement&&d instanceof HTMLImageElement?(m=d.naturalWidth,v=d.naturalHeight):(m=d.width,v=d.height);v={glName:p.l,width:m,height:v},(m=p.g).canvas.width=v.width,m.canvas.height=v.height,m.activeTexture(m.TEXTURE0),p.h.bindTexture2d(p.l),m.texImage2D(m.TEXTURE_2D,0,m.RGBA,m.RGBA,m.UNSIGNED_BYTE,d),p.h.bindTexture2d(0),p=v;break t;case"detections":for((v=p.m[c.stream])||(v=new tK(p.h),p.m[c.stream]=v),(p=v).data||(p.data=new p.g.DetectionListData),p.data.reset(d.length),v=0;v<d.length;++v){m=d[v];var b=p.data,w=b.setBoundingBox,A=v,_=m.T,x=new tD;tb(x,1,_.Z),tb(x,2,_.$),tb(x,3,_.height),tb(x,4,_.width),tb(x,5,_.rotation),tb(x,6,_.X);var E=_=new te;ti(E,1,tp(x,1)),ti(E,2,tp(x,2)),ti(E,3,tp(x,3)),ti(E,4,tp(x,4)),ti(E,5,tp(x,5));var j=tp(x,6);if(null!=j&&null!=j){z(E.g,48);var k=E.g,T=j;j=0>T;var O=(T=Math.abs(T))>>>0;for(T=Math.floor((T-O)/0x100000000)>>>0,j&&(T=~T>>>0,0xffffffff<(O=(~O>>>0)+1)&&(O=0,0xffffffff<++T&&(T=0))),D=O,G=T,j=D,O=G;0<O||127<j;)k.push(127&j|128),j=(j>>>7|O<<25)>>>0,O>>>=7;k.push(j)}if(tj(x,E),_=tn(_),w.call(b,A,_),m.O)for(b=0;b<m.O.length;++b)E=!!(x=m.O[b]).visibility,A=(w=p.data).addNormalizedLandmark,_=v,x=Object.assign(Object.assign({},x),{visibility:E?x.visibility:0}),tb(E=new tM,1,x.x),tb(E,2,x.y),tb(E,3,x.z),x.visibility&&tb(E,4,x.visibility),ti(k=x=new te,1,tp(E,1)),ti(k,2,tp(E,2)),ti(k,3,tp(E,3)),ti(k,4,tp(E,4)),ti(k,5,tp(E,5)),tj(E,k),x=tn(x),A.call(w,_,x);if(m.M)for(b=0;b<m.M.length;++b){if(A=(w=p.data).addClassification,_=v,x=m.M[b],tb(E=new tS,2,x.Y),x.index&&tb(E,1,x.index),x.label&&tb(E,3,x.label),x.displayName&&tb(E,4,x.displayName),k=x=new te,null!=(O=tp(E,1))&&null!=O)if(z(k.g,8),j=k.g,0<=O)z(j,O);else{for(T=0;9>T;T++)j.push(127&O|128),O>>=7;j.push(1)}ti(k,2,tp(E,2)),null!=(j=tp(E,3))&&(j=C(j),z(k.g,26),z(k.g,j.length),tr(k,k.g.end()),tr(k,j)),null!=(j=tp(E,4))&&(j=C(j),z(k.g,34),z(k.g,j.length),tr(k,k.g.end()),tr(k,j)),tj(E,k),x=tn(x),A.call(w,_,x)}}p=p.data;break t;default:p={}}}switch(f=p,l=c.stream,c.type){case"video":i.pushTexture2d(Object.assign(Object.assign({},f),{stream:l,timestamp:n}));break;case"detections":(h=f).stream=l,h.timestamp=n,i.pushDetectionList(h);break;default:throw Error("Unknown input config type: '"+c.type+"'")}}return g.i.send(i),y(r,g.C,4);case 4:i.delete(),r.g=0}})})},u.onResults=function(t,e){this.listeners[e||"$"]=t},j("Solution",tq),j("OptionType",{BOOL:0,NUMBER:1,aa:2,0:"BOOL",1:"NUMBER",2:"STRING"});var t1={files:[{url:"face_mesh_solution_packed_assets_loader.js"},{simd:!0,url:"face_mesh_solution_simd_wasm_bin.js"},{simd:!1,url:"face_mesh_solution_wasm_bin.js"}],graph:{url:"face_mesh.binarypb"},listeners:[{wants:["multi_face_geometry","image_transformed","multi_face_landmarks"],outs:{image:"image_transformed",multiFaceGeometry:{type:"proto_list",stream:"multi_face_geometry",transform:function(t){return t.map(t2)}},multiFaceLandmarks:{type:"proto_list",stream:"multi_face_landmarks",transform:function(t){return t.map(function(t){return tA(tN(t),tM).map(tY)})}}}}],inputs:{image:{type:"video",stream:"input_frames_gpu"}},options:{useCpuInference:{type:0,graphOptionXref:{calculatorType:"InferenceCalculator",fieldName:"use_cpu_inference"},default:"iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod".split(";").includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document},enableFaceGeometry:{type:0,graphOptionXref:{calculatorName:"EnableFaceGeometryConstant",calculatorType:"ConstantSidePacketCalculator",fieldName:"bool_value"}},selfieMode:{type:0,graphOptionXref:{calculatorType:"GlScalerCalculator",calculatorIndex:1,fieldName:"flip_horizontal"}},maxNumFaces:{type:1,graphOptionXref:{calculatorType:"ConstantSidePacketCalculator",calculatorName:"ConstantSidePacketCalculatorNumFaces",fieldName:"int_value"}},refineLandmarks:{type:0,graphOptionXref:{calculatorType:"ConstantSidePacketCalculator",calculatorName:"ConstantSidePacketCalculatorRefineLandmarks",fieldName:"bool_value"}},minDetectionConfidence:{type:1,graphOptionXref:{calculatorType:"TensorsToDetectionsCalculator",calculatorName:"facelandmarkfrontgpu__facedetectionshortrangegpu__facedetectionshortrangecommon__TensorsToDetectionsCalculator",fieldName:"min_score_thresh"}},minTrackingConfidence:{type:1,graphOptionXref:{calculatorType:"ThresholdingCalculator",calculatorName:"facelandmarkfrontgpu__facelandmarkgpu__ThresholdingCalculator",fieldName:"threshold"}},cameraNear:{type:1,graphOptionXref:{calculatorType:"FaceGeometryEnvGeneratorCalculator",fieldName:"near"}},cameraFar:{type:1,graphOptionXref:{calculatorType:"FaceGeometryEnvGeneratorCalculator",fieldName:"far"}},cameraVerticalFovDegrees:{type:1,graphOptionXref:{calculatorType:"FaceGeometryEnvGeneratorCalculator",fieldName:"vertical_fov_degrees"}}}},t3=[[61,146],[146,91],[91,181],[181,84],[84,17],[17,314],[314,405],[405,321],[321,375],[375,291],[61,185],[185,40],[40,39],[39,37],[37,0],[0,267],[267,269],[269,270],[270,409],[409,291],[78,95],[95,88],[88,178],[178,87],[87,14],[14,317],[317,402],[402,318],[318,324],[324,308],[78,191],[191,80],[80,81],[81,82],[82,13],[13,312],[312,311],[311,310],[310,415],[415,308]],t4=[[263,249],[249,390],[390,373],[373,374],[374,380],[380,381],[381,382],[382,362],[263,466],[466,388],[388,387],[387,386],[386,385],[385,384],[384,398],[398,362]],t0=[[276,283],[283,282],[282,295],[295,285],[300,293],[293,334],[334,296],[296,336]],t6=[[33,7],[7,163],[163,144],[144,145],[145,153],[153,154],[154,155],[155,133],[33,246],[246,161],[161,160],[160,159],[159,158],[158,157],[157,173],[173,133]],t5=[[46,53],[53,52],[52,65],[65,55],[70,63],[63,105],[105,66],[66,107]],t7=[[10,338],[338,297],[297,332],[332,284],[284,251],[251,389],[389,356],[356,454],[454,323],[323,361],[361,288],[288,397],[397,365],[365,379],[379,378],[378,400],[400,377],[377,152],[152,148],[148,176],[176,149],[149,150],[150,136],[136,172],[172,58],[58,132],[132,93],[93,234],[234,127],[127,162],[162,21],[21,54],[54,103],[103,67],[67,109],[109,10]],t9=[].concat(s(t3),s(t4),s(t0),s(t6),s(t5),s(t7));function t8(t){t=t||{},t=Object.assign(Object.assign({},t1),t),this.g=new tq(t)}(u=t8.prototype).close=function(){return this.g.close(),Promise.resolve()},u.onResults=function(t){this.g.onResults(t)},u.initialize=function(){return tR(this,function t(){var e=this;return _(t,function(t){return y(t,e.g.initialize(),0)})})},u.reset=function(){this.g.reset()},u.send=function(t){return tR(this,function e(){var r=this;return _(e,function(e){return y(e,r.g.send(t),0)})})},u.setOptions=function(t){this.g.setOptions(t)},j("FACE_GEOMETRY",{Layout:{COLUMN_MAJOR:0,ROW_MAJOR:1,0:"COLUMN_MAJOR",1:"ROW_MAJOR"},PrimitiveType:{TRIANGLE:0,0:"TRIANGLE"},VertexType:{VERTEX_PT:0,0:"VERTEX_PT"},DEFAULT_CAMERA_PARAMS:{verticalFovDegrees:63,near:1,far:1e4}}),j("FaceMesh",t8),j("FACEMESH_LIPS",t3),j("FACEMESH_LEFT_EYE",t4),j("FACEMESH_LEFT_EYEBROW",t0),j("FACEMESH_LEFT_IRIS",[[474,475],[475,476],[476,477],[477,474]]),j("FACEMESH_RIGHT_EYE",t6),j("FACEMESH_RIGHT_EYEBROW",t5),j("FACEMESH_RIGHT_IRIS",[[469,470],[470,471],[471,472],[472,469]]),j("FACEMESH_FACE_OVAL",t7),j("FACEMESH_CONTOURS",t9),j("FACEMESH_TESSELATION",[[127,34],[34,139],[139,127],[11,0],[0,37],[37,11],[232,231],[231,120],[120,232],[72,37],[37,39],[39,72],[128,121],[121,47],[47,128],[232,121],[121,128],[128,232],[104,69],[69,67],[67,104],[175,171],[171,148],[148,175],[118,50],[50,101],[101,118],[73,39],[39,40],[40,73],[9,151],[151,108],[108,9],[48,115],[115,131],[131,48],[194,204],[204,211],[211,194],[74,40],[40,185],[185,74],[80,42],[42,183],[183,80],[40,92],[92,186],[186,40],[230,229],[229,118],[118,230],[202,212],[212,214],[214,202],[83,18],[18,17],[17,83],[76,61],[61,146],[146,76],[160,29],[29,30],[30,160],[56,157],[157,173],[173,56],[106,204],[204,194],[194,106],[135,214],[214,192],[192,135],[203,165],[165,98],[98,203],[21,71],[71,68],[68,21],[51,45],[45,4],[4,51],[144,24],[24,23],[23,144],[77,146],[146,91],[91,77],[205,50],[50,187],[187,205],[201,200],[200,18],[18,201],[91,106],[106,182],[182,91],[90,91],[91,181],[181,90],[85,84],[84,17],[17,85],[206,203],[203,36],[36,206],[148,171],[171,140],[140,148],[92,40],[40,39],[39,92],[193,189],[189,244],[244,193],[159,158],[158,28],[28,159],[247,246],[246,161],[161,247],[236,3],[3,196],[196,236],[54,68],[68,104],[104,54],[193,168],[168,8],[8,193],[117,228],[228,31],[31,117],[189,193],[193,55],[55,189],[98,97],[97,99],[99,98],[126,47],[47,100],[100,126],[166,79],[79,218],[218,166],[155,154],[154,26],[26,155],[209,49],[49,131],[131,209],[135,136],[136,150],[150,135],[47,126],[126,217],[217,47],[223,52],[52,53],[53,223],[45,51],[51,134],[134,45],[211,170],[170,140],[140,211],[67,69],[69,108],[108,67],[43,106],[106,91],[91,43],[230,119],[119,120],[120,230],[226,130],[130,247],[247,226],[63,53],[53,52],[52,63],[238,20],[20,242],[242,238],[46,70],[70,156],[156,46],[78,62],[62,96],[96,78],[46,53],[53,63],[63,46],[143,34],[34,227],[227,143],[123,117],[117,111],[111,123],[44,125],[125,19],[19,44],[236,134],[134,51],[51,236],[216,206],[206,205],[205,216],[154,153],[153,22],[22,154],[39,37],[37,167],[167,39],[200,201],[201,208],[208,200],[36,142],[142,100],[100,36],[57,212],[212,202],[202,57],[20,60],[60,99],[99,20],[28,158],[158,157],[157,28],[35,226],[226,113],[113,35],[160,159],[159,27],[27,160],[204,202],[202,210],[210,204],[113,225],[225,46],[46,113],[43,202],[202,204],[204,43],[62,76],[76,77],[77,62],[137,123],[123,116],[116,137],[41,38],[38,72],[72,41],[203,129],[129,142],[142,203],[64,98],[98,240],[240,64],[49,102],[102,64],[64,49],[41,73],[73,74],[74,41],[212,216],[216,207],[207,212],[42,74],[74,184],[184,42],[169,170],[170,211],[211,169],[170,149],[149,176],[176,170],[105,66],[66,69],[69,105],[122,6],[6,168],[168,122],[123,147],[147,187],[187,123],[96,77],[77,90],[90,96],[65,55],[55,107],[107,65],[89,90],[90,180],[180,89],[101,100],[100,120],[120,101],[63,105],[105,104],[104,63],[93,137],[137,227],[227,93],[15,86],[86,85],[85,15],[129,102],[102,49],[49,129],[14,87],[87,86],[86,14],[55,8],[8,9],[9,55],[100,47],[47,121],[121,100],[145,23],[23,22],[22,145],[88,89],[89,179],[179,88],[6,122],[122,196],[196,6],[88,95],[95,96],[96,88],[138,172],[172,136],[136,138],[215,58],[58,172],[172,215],[115,48],[48,219],[219,115],[42,80],[80,81],[81,42],[195,3],[3,51],[51,195],[43,146],[146,61],[61,43],[171,175],[175,199],[199,171],[81,82],[82,38],[38,81],[53,46],[46,225],[225,53],[144,163],[163,110],[110,144],[52,65],[65,66],[66,52],[229,228],[228,117],[117,229],[34,127],[127,234],[234,34],[107,108],[108,69],[69,107],[109,108],[108,151],[151,109],[48,64],[64,235],[235,48],[62,78],[78,191],[191,62],[129,209],[209,126],[126,129],[111,35],[35,143],[143,111],[117,123],[123,50],[50,117],[222,65],[65,52],[52,222],[19,125],[125,141],[141,19],[221,55],[55,65],[65,221],[3,195],[195,197],[197,3],[25,7],[7,33],[33,25],[220,237],[237,44],[44,220],[70,71],[71,139],[139,70],[122,193],[193,245],[245,122],[247,130],[130,33],[33,247],[71,21],[21,162],[162,71],[170,169],[169,150],[150,170],[188,174],[174,196],[196,188],[216,186],[186,92],[92,216],[2,97],[97,167],[167,2],[141,125],[125,241],[241,141],[164,167],[167,37],[37,164],[72,38],[38,12],[12,72],[38,82],[82,13],[13,38],[63,68],[68,71],[71,63],[226,35],[35,111],[111,226],[101,50],[50,205],[205,101],[206,92],[92,165],[165,206],[209,198],[198,217],[217,209],[165,167],[167,97],[97,165],[220,115],[115,218],[218,220],[133,112],[112,243],[243,133],[239,238],[238,241],[241,239],[214,135],[135,169],[169,214],[190,173],[173,133],[133,190],[171,208],[208,32],[32,171],[125,44],[44,237],[237,125],[86,87],[87,178],[178,86],[85,86],[86,179],[179,85],[84,85],[85,180],[180,84],[83,84],[84,181],[181,83],[201,83],[83,182],[182,201],[137,93],[93,132],[132,137],[76,62],[62,183],[183,76],[61,76],[76,184],[184,61],[57,61],[61,185],[185,57],[212,57],[57,186],[186,212],[214,207],[207,187],[187,214],[34,143],[143,156],[156,34],[79,239],[239,237],[237,79],[123,137],[137,177],[177,123],[44,1],[1,4],[4,44],[201,194],[194,32],[32,201],[64,102],[102,129],[129,64],[213,215],[215,138],[138,213],[59,166],[166,219],[219,59],[242,99],[99,97],[97,242],[2,94],[94,141],[141,2],[75,59],[59,235],[235,75],[24,110],[110,228],[228,24],[25,130],[130,226],[226,25],[23,24],[24,229],[229,23],[22,23],[23,230],[230,22],[26,22],[22,231],[231,26],[112,26],[26,232],[232,112],[189,190],[190,243],[243,189],[221,56],[56,190],[190,221],[28,56],[56,221],[221,28],[27,28],[28,222],[222,27],[29,27],[27,223],[223,29],[30,29],[29,224],[224,30],[247,30],[30,225],[225,247],[238,79],[79,20],[20,238],[166,59],[59,75],[75,166],[60,75],[75,240],[240,60],[147,177],[177,215],[215,147],[20,79],[79,166],[166,20],[187,147],[147,213],[213,187],[112,233],[233,244],[244,112],[233,128],[128,245],[245,233],[128,114],[114,188],[188,128],[114,217],[217,174],[174,114],[131,115],[115,220],[220,131],[217,198],[198,236],[236,217],[198,131],[131,134],[134,198],[177,132],[132,58],[58,177],[143,35],[35,124],[124,143],[110,163],[163,7],[7,110],[228,110],[110,25],[25,228],[356,389],[389,368],[368,356],[11,302],[302,267],[267,11],[452,350],[350,349],[349,452],[302,303],[303,269],[269,302],[357,343],[343,277],[277,357],[452,453],[453,357],[357,452],[333,332],[332,297],[297,333],[175,152],[152,377],[377,175],[347,348],[348,330],[330,347],[303,304],[304,270],[270,303],[9,336],[336,337],[337,9],[278,279],[279,360],[360,278],[418,262],[262,431],[431,418],[304,408],[408,409],[409,304],[310,415],[415,407],[407,310],[270,409],[409,410],[410,270],[450,348],[348,347],[347,450],[422,430],[430,434],[434,422],[313,314],[314,17],[17,313],[306,307],[307,375],[375,306],[387,388],[388,260],[260,387],[286,414],[414,398],[398,286],[335,406],[406,418],[418,335],[364,367],[367,416],[416,364],[423,358],[358,327],[327,423],[251,284],[284,298],[298,251],[281,5],[5,4],[4,281],[373,374],[374,253],[253,373],[307,320],[320,321],[321,307],[425,427],[427,411],[411,425],[421,313],[313,18],[18,421],[321,405],[405,406],[406,321],[320,404],[404,405],[405,320],[315,16],[16,17],[17,315],[426,425],[425,266],[266,426],[377,400],[400,369],[369,377],[322,391],[391,269],[269,322],[417,465],[465,464],[464,417],[386,257],[257,258],[258,386],[466,260],[260,388],[388,466],[456,399],[399,419],[419,456],[284,332],[332,333],[333,284],[417,285],[285,8],[8,417],[346,340],[340,261],[261,346],[413,441],[441,285],[285,413],[327,460],[460,328],[328,327],[355,371],[371,329],[329,355],[392,439],[439,438],[438,392],[382,341],[341,256],[256,382],[429,420],[420,360],[360,429],[364,394],[394,379],[379,364],[277,343],[343,437],[437,277],[443,444],[444,283],[283,443],[275,440],[440,363],[363,275],[431,262],[262,369],[369,431],[297,338],[338,337],[337,297],[273,375],[375,321],[321,273],[450,451],[451,349],[349,450],[446,342],[342,467],[467,446],[293,334],[334,282],[282,293],[458,461],[461,462],[462,458],[276,353],[353,383],[383,276],[308,324],[324,325],[325,308],[276,300],[300,293],[293,276],[372,345],[345,447],[447,372],[352,345],[345,340],[340,352],[274,1],[1,19],[19,274],[456,248],[248,281],[281,456],[436,427],[427,425],[425,436],[381,256],[256,252],[252,381],[269,391],[391,393],[393,269],[200,199],[199,428],[428,200],[266,330],[330,329],[329,266],[287,273],[273,422],[422,287],[250,462],[462,328],[328,250],[258,286],[286,384],[384,258],[265,353],[353,342],[342,265],[387,259],[259,257],[257,387],[424,431],[431,430],[430,424],[342,353],[353,276],[276,342],[273,335],[335,424],[424,273],[292,325],[325,307],[307,292],[366,447],[447,345],[345,366],[271,303],[303,302],[302,271],[423,266],[266,371],[371,423],[294,455],[455,460],[460,294],[279,278],[278,294],[294,279],[271,272],[272,304],[304,271],[432,434],[434,427],[427,432],[272,407],[407,408],[408,272],[394,430],[430,431],[431,394],[395,369],[369,400],[400,395],[334,333],[333,299],[299,334],[351,417],[417,168],[168,351],[352,280],[280,411],[411,352],[325,319],[319,320],[320,325],[295,296],[296,336],[336,295],[319,403],[403,404],[404,319],[330,348],[348,349],[349,330],[293,298],[298,333],[333,293],[323,454],[454,447],[447,323],[15,16],[16,315],[315,15],[358,429],[429,279],[279,358],[14,15],[15,316],[316,14],[285,336],[336,9],[9,285],[329,349],[349,350],[350,329],[374,380],[380,252],[252,374],[318,402],[402,403],[403,318],[6,197],[197,419],[419,6],[318,319],[319,325],[325,318],[367,364],[364,365],[365,367],[435,367],[367,397],[397,435],[344,438],[438,439],[439,344],[272,271],[271,311],[311,272],[195,5],[5,281],[281,195],[273,287],[287,291],[291,273],[396,428],[428,199],[199,396],[311,271],[271,268],[268,311],[283,444],[444,445],[445,283],[373,254],[254,339],[339,373],[282,334],[334,296],[296,282],[449,347],[347,346],[346,449],[264,447],[447,454],[454,264],[336,296],[296,299],[299,336],[338,10],[10,151],[151,338],[278,439],[439,455],[455,278],[292,407],[407,415],[415,292],[358,371],[371,355],[355,358],[340,345],[345,372],[372,340],[346,347],[347,280],[280,346],[442,443],[443,282],[282,442],[19,94],[94,370],[370,19],[441,442],[442,295],[295,441],[248,419],[419,197],[197,248],[263,255],[255,359],[359,263],[440,275],[275,274],[274,440],[300,383],[383,368],[368,300],[351,412],[412,465],[465,351],[263,467],[467,466],[466,263],[301,368],[368,389],[389,301],[395,378],[378,379],[379,395],[412,351],[351,419],[419,412],[436,426],[426,322],[322,436],[2,164],[164,393],[393,2],[370,462],[462,461],[461,370],[164,0],[0,267],[267,164],[302,11],[11,12],[12,302],[268,12],[12,13],[13,268],[293,300],[300,301],[301,293],[446,261],[261,340],[340,446],[330,266],[266,425],[425,330],[426,423],[423,391],[391,426],[429,355],[355,437],[437,429],[391,327],[327,326],[326,391],[440,457],[457,438],[438,440],[341,382],[382,362],[362,341],[459,457],[457,461],[461,459],[434,430],[430,394],[394,434],[414,463],[463,362],[362,414],[396,369],[369,262],[262,396],[354,461],[461,457],[457,354],[316,403],[403,402],[402,316],[315,404],[404,403],[403,315],[314,405],[405,404],[404,314],[313,406],[406,405],[405,313],[421,418],[418,406],[406,421],[366,401],[401,361],[361,366],[306,408],[408,407],[407,306],[291,409],[409,408],[408,291],[287,410],[410,409],[409,287],[432,436],[436,410],[410,432],[434,416],[416,411],[411,434],[264,368],[368,383],[383,264],[309,438],[438,457],[457,309],[352,376],[376,401],[401,352],[274,275],[275,4],[4,274],[421,428],[428,262],[262,421],[294,327],[327,358],[358,294],[433,416],[416,367],[367,433],[289,455],[455,439],[439,289],[462,370],[370,326],[326,462],[2,326],[326,370],[370,2],[305,460],[460,455],[455,305],[254,449],[449,448],[448,254],[255,261],[261,446],[446,255],[253,450],[450,449],[449,253],[252,451],[451,450],[450,252],[256,452],[452,451],[451,256],[341,453],[453,452],[452,341],[413,464],[464,463],[463,413],[441,413],[413,414],[414,441],[258,442],[442,441],[441,258],[257,443],[443,442],[442,257],[259,444],[444,443],[443,259],[260,445],[445,444],[444,260],[467,342],[342,445],[445,467],[459,458],[458,250],[250,459],[289,392],[392,290],[290,289],[290,328],[328,460],[460,290],[376,433],[433,435],[435,376],[250,290],[290,392],[392,250],[411,416],[416,433],[433,411],[341,463],[463,464],[464,341],[453,464],[464,465],[465,453],[357,465],[465,412],[412,357],[343,412],[412,399],[399,343],[360,363],[363,440],[440,360],[437,399],[399,456],[456,437],[420,456],[456,363],[363,420],[401,435],[435,288],[288,401],[372,383],[383,353],[353,372],[339,255],[255,249],[249,339],[448,261],[261,255],[255,448],[133,243],[243,190],[190,133],[133,155],[155,112],[112,133],[33,246],[246,247],[247,33],[33,130],[130,25],[25,33],[398,384],[384,286],[286,398],[362,398],[398,414],[414,362],[362,463],[463,341],[341,362],[263,359],[359,467],[467,263],[263,249],[249,255],[255,263],[466,467],[467,260],[260,466],[75,60],[60,166],[166,75],[238,239],[239,79],[79,238],[162,127],[127,139],[139,162],[72,11],[11,37],[37,72],[121,232],[232,120],[120,121],[73,72],[72,39],[39,73],[114,128],[128,47],[47,114],[233,232],[232,128],[128,233],[103,104],[104,67],[67,103],[152,175],[175,148],[148,152],[119,118],[118,101],[101,119],[74,73],[73,40],[40,74],[107,9],[9,108],[108,107],[49,48],[48,131],[131,49],[32,194],[194,211],[211,32],[184,74],[74,185],[185,184],[191,80],[80,183],[183,191],[185,40],[40,186],[186,185],[119,230],[230,118],[118,119],[210,202],[202,214],[214,210],[84,83],[83,17],[17,84],[77,76],[76,146],[146,77],[161,160],[160,30],[30,161],[190,56],[56,173],[173,190],[182,106],[106,194],[194,182],[138,135],[135,192],[192,138],[129,203],[203,98],[98,129],[54,21],[21,68],[68,54],[5,51],[51,4],[4,5],[145,144],[144,23],[23,145],[90,77],[77,91],[91,90],[207,205],[205,187],[187,207],[83,201],[201,18],[18,83],[181,91],[91,182],[182,181],[180,90],[90,181],[181,180],[16,85],[85,17],[17,16],[205,206],[206,36],[36,205],[176,148],[148,140],[140,176],[165,92],[92,39],[39,165],[245,193],[193,244],[244,245],[27,159],[159,28],[28,27],[30,247],[247,161],[161,30],[174,236],[236,196],[196,174],[103,54],[54,104],[104,103],[55,193],[193,8],[8,55],[111,117],[117,31],[31,111],[221,189],[189,55],[55,221],[240,98],[98,99],[99,240],[142,126],[126,100],[100,142],[219,166],[166,218],[218,219],[112,155],[155,26],[26,112],[198,209],[209,131],[131,198],[169,135],[135,150],[150,169],[114,47],[47,217],[217,114],[224,223],[223,53],[53,224],[220,45],[45,134],[134,220],[32,211],[211,140],[140,32],[109,67],[67,108],[108,109],[146,43],[43,91],[91,146],[231,230],[230,120],[120,231],[113,226],[226,247],[247,113],[105,63],[63,52],[52,105],[241,238],[238,242],[242,241],[124,46],[46,156],[156,124],[95,78],[78,96],[96,95],[70,46],[46,63],[63,70],[116,143],[143,227],[227,116],[116,123],[123,111],[111,116],[1,44],[44,19],[19,1],[3,236],[236,51],[51,3],[207,216],[216,205],[205,207],[26,154],[154,22],[22,26],[165,39],[39,167],[167,165],[199,200],[200,208],[208,199],[101,36],[36,100],[100,101],[43,57],[57,202],[202,43],[242,20],[20,99],[99,242],[56,28],[28,157],[157,56],[124,35],[35,113],[113,124],[29,160],[160,27],[27,29],[211,204],[204,210],[210,211],[124,113],[113,46],[46,124],[106,43],[43,204],[204,106],[96,62],[62,77],[77,96],[227,137],[137,116],[116,227],[73,41],[41,72],[72,73],[36,203],[203,142],[142,36],[235,64],[64,240],[240,235],[48,49],[49,64],[64,48],[42,41],[41,74],[74,42],[214,212],[212,207],[207,214],[183,42],[42,184],[184,183],[210,169],[169,211],[211,210],[140,170],[170,176],[176,140],[104,105],[105,69],[69,104],[193,122],[122,168],[168,193],[50,123],[123,187],[187,50],[89,96],[96,90],[90,89],[66,65],[65,107],[107,66],[179,89],[89,180],[180,179],[119,101],[101,120],[120,119],[68,63],[63,104],[104,68],[234,93],[93,227],[227,234],[16,15],[15,85],[85,16],[209,129],[129,49],[49,209],[15,14],[14,86],[86,15],[107,55],[55,9],[9,107],[120,100],[100,121],[121,120],[153,145],[145,22],[22,153],[178,88],[88,179],[179,178],[197,6],[6,196],[196,197],[89,88],[88,96],[96,89],[135,138],[138,136],[136,135],[138,215],[215,172],[172,138],[218,115],[115,219],[219,218],[41,42],[42,81],[81,41],[5,195],[195,51],[51,5],[57,43],[43,61],[61,57],[208,171],[171,199],[199,208],[41,81],[81,38],[38,41],[224,53],[53,225],[225,224],[24,144],[144,110],[110,24],[105,52],[52,66],[66,105],[118,229],[229,117],[117,118],[227,34],[34,234],[234,227],[66,107],[107,69],[69,66],[10,109],[109,151],[151,10],[219,48],[48,235],[235,219],[183,62],[62,191],[191,183],[142,129],[129,126],[126,142],[116,111],[111,143],[143,116],[118,117],[117,50],[50,118],[223,222],[222,52],[52,223],[94,19],[19,141],[141,94],[222,221],[221,65],[65,222],[196,3],[3,197],[197,196],[45,220],[220,44],[44,45],[156,70],[70,139],[139,156],[188,122],[122,245],[245,188],[139,71],[71,162],[162,139],[149,170],[170,150],[150,149],[122,188],[188,196],[196,122],[206,216],[216,92],[92,206],[164,2],[2,167],[167,164],[242,141],[141,241],[241,242],[0,164],[164,37],[37,0],[11,72],[72,12],[12,11],[12,38],[38,13],[13,12],[70,63],[63,71],[71,70],[31,226],[226,111],[111,31],[36,101],[101,205],[205,36],[203,206],[206,165],[165,203],[126,209],[209,217],[217,126],[98,165],[165,97],[97,98],[237,220],[220,218],[218,237],[237,239],[239,241],[241,237],[210,214],[214,169],[169,210],[140,171],[171,32],[32,140],[241,125],[125,237],[237,241],[179,86],[86,178],[178,179],[180,85],[85,179],[179,180],[181,84],[84,180],[180,181],[182,83],[83,181],[181,182],[194,201],[201,182],[182,194],[177,137],[137,132],[132,177],[184,76],[76,183],[183,184],[185,61],[61,184],[184,185],[186,57],[57,185],[185,186],[216,212],[212,186],[186,216],[192,214],[214,187],[187,192],[139,34],[34,156],[156,139],[218,79],[79,237],[237,218],[147,123],[123,177],[177,147],[45,44],[44,4],[4,45],[208,201],[201,32],[32,208],[98,64],[64,129],[129,98],[192,213],[213,138],[138,192],[235,59],[59,219],[219,235],[141,242],[242,97],[97,141],[97,2],[2,141],[141,97],[240,75],[75,235],[235,240],[229,24],[24,228],[228,229],[31,25],[25,226],[226,31],[230,23],[23,229],[229,230],[231,22],[22,230],[230,231],[232,26],[26,231],[231,232],[233,112],[112,232],[232,233],[244,189],[189,243],[243,244],[189,221],[221,190],[190,189],[222,28],[28,221],[221,222],[223,27],[27,222],[222,223],[224,29],[29,223],[223,224],[225,30],[30,224],[224,225],[113,247],[247,225],[225,113],[99,60],[60,240],[240,99],[213,147],[147,215],[215,213],[60,20],[20,166],[166,60],[192,187],[187,213],[213,192],[243,112],[112,244],[244,243],[244,233],[233,245],[245,244],[245,128],[128,188],[188,245],[188,114],[114,174],[174,188],[134,131],[131,220],[220,134],[174,217],[217,236],[236,174],[236,198],[198,134],[134,236],[215,177],[177,58],[58,215],[156,143],[143,124],[124,156],[25,110],[110,7],[7,25],[31,228],[228,25],[25,31],[264,356],[356,368],[368,264],[0,11],[11,267],[267,0],[451,452],[452,349],[349,451],[267,302],[302,269],[269,267],[350,357],[357,277],[277,350],[350,452],[452,357],[357,350],[299,333],[333,297],[297,299],[396,175],[175,377],[377,396],[280,347],[347,330],[330,280],[269,303],[303,270],[270,269],[151,9],[9,337],[337,151],[344,278],[278,360],[360,344],[424,418],[418,431],[431,424],[270,304],[304,409],[409,270],[272,310],[310,407],[407,272],[322,270],[270,410],[410,322],[449,450],[450,347],[347,449],[432,422],[422,434],[434,432],[18,313],[313,17],[17,18],[291,306],[306,375],[375,291],[259,387],[387,260],[260,259],[424,335],[335,418],[418,424],[434,364],[364,416],[416,434],[391,423],[423,327],[327,391],[301,251],[251,298],[298,301],[275,281],[281,4],[4,275],[254,373],[373,253],[253,254],[375,307],[307,321],[321,375],[280,425],[425,411],[411,280],[200,421],[421,18],[18,200],[335,321],[321,406],[406,335],[321,320],[320,405],[405,321],[314,315],[315,17],[17,314],[423,426],[426,266],[266,423],[396,377],[377,369],[369,396],[270,322],[322,269],[269,270],[413,417],[417,464],[464,413],[385,386],[386,258],[258,385],[248,456],[456,419],[419,248],[298,284],[284,333],[333,298],[168,417],[417,8],[8,168],[448,346],[346,261],[261,448],[417,413],[413,285],[285,417],[326,327],[327,328],[328,326],[277,355],[355,329],[329,277],[309,392],[392,438],[438,309],[381,382],[382,256],[256,381],[279,429],[429,360],[360,279],[365,364],[364,379],[379,365],[355,277],[277,437],[437,355],[282,443],[443,283],[283,282],[281,275],[275,363],[363,281],[395,431],[431,369],[369,395],[299,297],[297,337],[337,299],[335,273],[273,321],[321,335],[348,450],[450,349],[349,348],[359,446],[446,467],[467,359],[283,293],[293,282],[282,283],[250,458],[458,462],[462,250],[300,276],[276,383],[383,300],[292,308],[308,325],[325,292],[283,276],[276,293],[293,283],[264,372],[372,447],[447,264],[346,352],[352,340],[340,346],[354,274],[274,19],[19,354],[363,456],[456,281],[281,363],[426,436],[436,425],[425,426],[380,381],[381,252],[252,380],[267,269],[269,393],[393,267],[421,200],[200,428],[428,421],[371,266],[266,329],[329,371],[432,287],[287,422],[422,432],[290,250],[250,328],[328,290],[385,258],[258,384],[384,385],[446,265],[265,342],[342,446],[386,387],[387,257],[257,386],[422,424],[424,430],[430,422],[445,342],[342,276],[276,445],[422,273],[273,424],[424,422],[306,292],[292,307],[307,306],[352,366],[366,345],[345,352],[268,271],[271,302],[302,268],[358,423],[423,371],[371,358],[327,294],[294,460],[460,327],[331,279],[279,294],[294,331],[303,271],[271,304],[304,303],[436,432],[432,427],[427,436],[304,272],[272,408],[408,304],[395,394],[394,431],[431,395],[378,395],[395,400],[400,378],[296,334],[334,299],[299,296],[6,351],[351,168],[168,6],[376,352],[352,411],[411,376],[307,325],[325,320],[320,307],[285,295],[295,336],[336,285],[320,319],[319,404],[404,320],[329,330],[330,349],[349,329],[334,293],[293,333],[333,334],[366,323],[323,447],[447,366],[316,15],[15,315],[315,316],[331,358],[358,279],[279,331],[317,14],[14,316],[316,317],[8,285],[285,9],[9,8],[277,329],[329,350],[350,277],[253,374],[374,252],[252,253],[319,318],[318,403],[403,319],[351,6],[6,419],[419,351],[324,318],[318,325],[325,324],[397,367],[367,365],[365,397],[288,435],[435,397],[397,288],[278,344],[344,439],[439,278],[310,272],[272,311],[311,310],[248,195],[195,281],[281,248],[375,273],[273,291],[291,375],[175,396],[396,199],[199,175],[312,311],[311,268],[268,312],[276,283],[283,445],[445,276],[390,373],[373,339],[339,390],[295,282],[282,296],[296,295],[448,449],[449,346],[346,448],[356,264],[264,454],[454,356],[337,336],[336,299],[299,337],[337,338],[338,151],[151,337],[294,278],[278,455],[455,294],[308,292],[292,415],[415,308],[429,358],[358,355],[355,429],[265,340],[340,372],[372,265],[352,346],[346,280],[280,352],[295,442],[442,282],[282,295],[354,19],[19,370],[370,354],[285,441],[441,295],[295,285],[195,248],[248,197],[197,195],[457,440],[440,274],[274,457],[301,300],[300,368],[368,301],[417,351],[351,465],[465,417],[251,301],[301,389],[389,251],[394,395],[395,379],[379,394],[399,412],[412,419],[419,399],[410,436],[436,322],[322,410],[326,2],[2,393],[393,326],[354,370],[370,461],[461,354],[393,164],[164,267],[267,393],[268,302],[302,12],[12,268],[312,268],[268,13],[13,312],[298,293],[293,301],[301,298],[265,446],[446,340],[340,265],[280,330],[330,425],[425,280],[322,426],[426,391],[391,322],[420,429],[429,437],[437,420],[393,391],[391,326],[326,393],[344,440],[440,438],[438,344],[458,459],[459,461],[461,458],[364,434],[434,394],[394,364],[428,396],[396,262],[262,428],[274,354],[354,457],[457,274],[317,316],[316,402],[402,317],[316,315],[315,403],[403,316],[315,314],[314,404],[404,315],[314,313],[313,405],[405,314],[313,421],[421,406],[406,313],[323,366],[366,361],[361,323],[292,306],[306,407],[407,292],[306,291],[291,408],[408,306],[291,287],[287,409],[409,291],[287,432],[432,410],[410,287],[427,434],[434,411],[411,427],[372,264],[264,383],[383,372],[459,309],[309,457],[457,459],[366,352],[352,401],[401,366],[1,274],[274,4],[4,1],[418,421],[421,262],[262,418],[331,294],[294,358],[358,331],[435,433],[433,367],[367,435],[392,289],[289,439],[439,392],[328,462],[462,326],[326,328],[94,2],[2,370],[370,94],[289,305],[305,455],[455,289],[339,254],[254,448],[448,339],[359,255],[255,446],[446,359],[254,253],[253,449],[449,254],[253,252],[252,450],[450,253],[252,256],[256,451],[451,252],[256,341],[341,452],[452,256],[414,413],[413,463],[463,414],[286,441],[441,414],[414,286],[286,258],[258,441],[441,286],[258,257],[257,442],[442,258],[257,259],[259,443],[443,257],[259,260],[260,444],[444,259],[260,467],[467,445],[445,260],[309,459],[459,250],[250,309],[305,289],[289,290],[290,305],[305,290],[290,460],[460,305],[401,376],[376,435],[435,401],[309,250],[250,392],[392,309],[376,411],[411,433],[433,376],[453,341],[341,464],[464,453],[357,453],[453,465],[465,357],[343,357],[357,412],[412,343],[437,343],[343,399],[399,437],[344,360],[360,440],[440,344],[420,437],[437,456],[456,420],[360,420],[420,363],[363,360],[361,401],[401,288],[288,361],[265,372],[372,353],[353,265],[390,339],[339,249],[249,390],[339,448],[448,255],[255,339]]),j("matrixDataToMatrix",function(t){for(var e=t.getCols(),r=t.getRows(),n=t.getPackedDataList(),i=[],o=0;o<r;o++)i.push(Array(e));for(o=0;o<r;o++)for(var a=0;a<e;a++){var s=1===t.getLayout()?o*e+a:a*r+o;i[o][a]=n[s]}return i}),j("VERSION","0.4.1633559619")}).call(this)}}]);