{"/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/api/audit/route": "/api/audit", "/api/auth/logout/route": "/api/auth/logout", "/api/auth/callback/credentials/route": "/api/auth/callback/credentials", "/api/biometria/digital/route": "/api/biometria/digital", "/api/biometria/facial/route": "/api/biometria/facial", "/api/auth/login/route": "/api/auth/login", "/api/funcionarios/[id]/route": "/api/funcionarios/[id]", "/api/funcionarios/next-matricula/route": "/api/funcionarios/next-matricula", "/api/errors/route": "/api/errors", "/api/funcionarios/reset/route": "/api/funcionarios/reset", "/api/funcionarios/route": "/api/funcionarios", "/api/health/route": "/api/health", "/api/matricula/route": "/api/matricula", "/api/ponto/manual/funcionarios/route": "/api/ponto/manual/funcionarios", "/favicon.ico/route": "/favicon.ico", "/api/ponto/biometrico/route": "/api/ponto/biometrico", "/api/ponto/manual/route": "/api/ponto/manual", "/_not-found/page": "/_not-found", "/dashboard/page": "/dashboard", "/login/page": "/login", "/page": "/", "/(dashboard)/ponto/page": "/ponto", "/(dashboard)/funcionarios/[id]/editar/page": "/funcionarios/[id]/editar", "/(dashboard)/funcionarios/[id]/page": "/funcionarios/[id]", "/(dashboard)/estatisticas/page": "/estatisticas", "/(dashboard)/administracao/page": "/administracao", "/(dashboard)/estatisticas/comparativos/page": "/estatisticas/comparativos", "/(dashboard)/funcionarios/novo/page": "/funcionarios/novo", "/(dashboard)/ponto/biometrico/page": "/ponto/biometrico", "/(dashboard)/estatisticas/absenteismo/page": "/estatisticas/absenteismo", "/(dashboard)/funcionarios/page": "/funcionarios", "/(dashboard)/estatisticas/produtividade/page": "/estatisticas/produtividade", "/(dashboard)/periodo-apuracao/page": "/periodo-apuracao", "/(dashboard)/relatorios/analiticos/page": "/relatorios/analiticos", "/(dashboard)/estatisticas/tendencias/page": "/estatisticas/tendencias", "/(dashboard)/relatorios/insights/page": "/relatorios/insights", "/(dashboard)/relatorios/construtor/page": "/relatorios/construtor", "/(dashboard)/relatorios/agendamentos/page": "/relatorios/agendamentos", "/(dashboard)/relatorios/periodo/page": "/relatorios/periodo", "/(dashboard)/relatorios/page": "/relatorios", "/(dashboard)/funcionarios/[id]/biometria/page": "/funcionarios/[id]/biometria", "/(dashboard)/ponto/manual/page": "/ponto/manual", "/(dashboard)/relatorios/funcionario/page": "/relatorios/funcionario"}