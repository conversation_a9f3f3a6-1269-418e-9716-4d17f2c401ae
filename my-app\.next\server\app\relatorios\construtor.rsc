1:"$Sreact.fragment"
2:I[6062,["7177","static/chunks/app/layout-7e63b6183d7635de.js"],"SessionProvider"]
3:I[6403,["7177","static/chunks/app/layout-7e63b6183d7635de.js"],"TextFormattingProvider"]
4:I[7555,[],""]
5:I[1901,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","8039","static/chunks/app/error-da7a176665770fd1.js"],"default"]
6:I[1295,[],""]
7:I[2558,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","4345","static/chunks/app/not-found-0d232e224bc38f5f.js"],"default"]
8:I[277,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","9305","static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js"],"DashboardHeader"]
9:I[2623,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","9305","static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js"],"DashboardNav"]
a:I[6874,["1309","static/chunks/602dbae6-fc02795e07310262.js","6874","static/chunks/6874-d27b54d0b28e3259.js","9457","static/chunks/9457-7c17f115b3646ae3.js","567","static/chunks/app/(dashboard)/relatorios/construtor/page-e5c3bdc4cdff01e2.js"],""]
17:I[8393,[],""]
:HL["/_next/static/css/b293d1867f231925.css","style"]
0:{"P":null,"b":"57groY_ofPMaGR4OOS9UN","p":"","c":["","relatorios","construtor"],"i":false,"f":[[["",{"children":["(dashboard)",{"children":["relatorios",{"children":["construtor",{"children":["__PAGE__",{}]}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/b293d1867f231925.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$5","errorStyles":[],"errorScripts":[],"template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","$L7",null,{}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]}]]}],{"children":["(dashboard)",["$","$1","c",{"children":[null,["$","div",null,{"className":"min-h-screen bg-gray-50","children":[["$","$L8",null,{}],["$","div",null,{"className":"flex","children":[["$","$L9",null,{}],["$","main",null,{"className":"flex-1 overflow-auto","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]]}]]}],{"children":["relatorios",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["construtor",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-gray-50","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8","children":["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"className":"flex items-center justify-between","children":[["$","div",null,{"className":"flex items-center space-x-4","children":[["$","$La",null,{"href":"/relatorios","children":["$","button",null,{"className":"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-left h-4 w-4 mr-2 inline","aria-hidden":"true","children":[["$","path","1l729n",{"d":"m12 19-7-7 7-7"}],["$","path","x3x0zl",{"d":"M19 12H5"}],"$undefined"]}],"Voltar"]}]}],["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"p-2 bg-purple-600 rounded-lg","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-settings h-8 w-8 text-white","aria-hidden":"true","children":[["$","path","1qme2f",{"d":"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"}],["$","circle","1v7zrd",{"cx":"12","cy":"12","r":"3"}],"$undefined"]}]}],["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold text-gray-900","children":"Construtor de Relatórios"}],["$","p",null,{"className":"text-gray-600","children":"Crie relatórios personalizados com interface visual"}]]}]]}]]}],["$","div",null,{"className":"flex items-center space-x-3","children":[["$","button",null,{"className":"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-save h-4 w-4 mr-2 inline","aria-hidden":"true","children":["$Lb","$Lc","$Ld","$undefined"]}],"Salvar"]}],"$Le","$Lf"]}]]}],"$L10","$L11","$L12","$L13","$L14"]}]}]}],null,"$L15"]}],{},null,false]},null,false]},null,false]},null,false]},null,false],"$L16",false]],"m":"$undefined","G":["$17",[]],"s":false,"S":true}
18:I[1823,["1309","static/chunks/602dbae6-fc02795e07310262.js","6874","static/chunks/6874-d27b54d0b28e3259.js","9457","static/chunks/9457-7c17f115b3646ae3.js","567","static/chunks/app/(dashboard)/relatorios/construtor/page-e5c3bdc4cdff01e2.js"],"ReportBuilder"]
19:I[9665,[],"OutletBoundary"]
1b:I[4911,[],"AsyncMetadataOutlet"]
1d:I[9665,[],"ViewportBoundary"]
1f:I[9665,[],"MetadataBoundary"]
20:"$Sreact.suspense"
b:["$","path","1c8476",{"d":"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z"}]
c:["$","path","1ydtos",{"d":"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"}]
d:["$","path","t51u73",{"d":"M7 3v4a1 1 0 0 0 1 1h7"}]
e:["$","button",null,{"className":"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-play h-4 w-4 mr-2 inline","aria-hidden":"true","children":[["$","polygon","1oa8hb",{"points":"6 3 20 12 6 21 6 3"}],"$undefined"]}],"Executar"]}]
f:["$","button",null,{"className":"px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-download h-4 w-4 mr-2 inline","aria-hidden":"true","children":[["$","path","m9g1x1",{"d":"M12 15V3"}],["$","path","ih7n3h",{"d":"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}],["$","path","brsn70",{"d":"m7 10 5 5 5-5"}],"$undefined"]}],"Exportar"]}]
10:["$","div",null,{"className":"bg-purple-50 border border-purple-200 rounded-lg p-6","children":[["$","h3",null,{"className":"text-lg font-semibold text-purple-900 mb-3","children":"Construtor Visual de Relatórios"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-purple-700","children":[["$","div",null,{"children":[["$","strong",null,{"children":"🎨 Interface Visual"}],["$","p",null,{"className":"mt-1","children":"Arraste e solte campos para criar relatórios personalizados"}]]}],["$","div",null,{"children":[["$","strong",null,{"children":"📊 Múltiplas Visualizações"}],["$","p",null,{"className":"mt-1","children":"Tabelas, gráficos, cards e dashboards interativos"}]]}],["$","div",null,{"children":[["$","strong",null,{"children":"⚡ Tempo Real"}],["$","p",null,{"className":"mt-1","children":"Preview instantâneo das alterações conforme você constrói"}]]}]]}]]}]
11:["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-4 gap-6","children":[["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"flex-shrink-0","children":["$","div",null,{"className":"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-settings w-4 h-4 text-blue-600","aria-hidden":"true","children":[["$","path","1qme2f",{"d":"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"}],["$","circle","1v7zrd",{"cx":"12","cy":"12","r":"3"}],"$undefined"]}]}]}],["$","div",null,{"className":"ml-4","children":[["$","p",null,{"className":"text-sm font-medium text-gray-500","children":"Templates"}],["$","p",null,{"className":"text-2xl font-semibold text-gray-900","children":"12"}]]}]]}]}],["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"flex-shrink-0","children":["$","div",null,{"className":"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center","children":["$","div",null,{"className":"w-3 h-3 bg-green-600 rounded-full"}]}]}],["$","div",null,{"className":"ml-4","children":[["$","p",null,{"className":"text-sm font-medium text-gray-500","children":"Campos Disponíveis"}],["$","p",null,{"className":"text-2xl font-semibold text-gray-900","children":"45"}]]}]]}]}],["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"flex-shrink-0","children":["$","div",null,{"className":"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center","children":["$","div",null,{"className":"w-3 h-3 bg-yellow-600 rounded-full"}]}]}],["$","div",null,{"className":"ml-4","children":[["$","p",null,{"className":"text-sm font-medium text-gray-500","children":"Relatórios Salvos"}],["$","p",null,{"className":"text-2xl font-semibold text-gray-900","children":"8"}]]}]]}]}],["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"flex-shrink-0","children":["$","div",null,{"className":"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center","children":["$","div",null,{"className":"w-3 h-3 bg-purple-600 rounded-full"}]}]}],["$","div",null,{"className":"ml-4","children":[["$","p",null,{"className":"text-sm font-medium text-gray-500","children":"Execuções Hoje"}],["$","p",null,{"className":"text-2xl font-semibold text-gray-900","children":"23"}]]}]]}]}]]}]
12:["$","div",null,{"className":"bg-white rounded-lg shadow","children":[["$","div",null,{"className":"px-6 py-4 border-b border-gray-200","children":[["$","h2",null,{"className":"text-lg font-medium text-gray-900","children":"Construtor Visual"}],["$","p",null,{"className":"text-sm text-gray-500","children":"Arraste campos da biblioteca para a área de design e configure as visualizações"}]]}],["$","div",null,{"className":"p-6","children":["$","$L18",null,{}]}]]}]
13:["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 mb-4","children":"Templates Rápidos"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-4","children":[["$","div",null,{"className":"border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer","children":[["$","h4",null,{"className":"font-medium text-gray-900 mb-2","children":"Relatório de Presença"}],["$","p",null,{"className":"text-sm text-gray-600 mb-3","children":"Análise de frequência e pontualidade por funcionário"}],["$","div",null,{"className":"flex items-center text-xs text-gray-500","children":[["$","span",null,{"className":"bg-blue-100 text-blue-800 px-2 py-1 rounded","children":"Frequência"}],["$","span",null,{"className":"ml-2","children":"5 campos"}]]}]]}],["$","div",null,{"className":"border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer","children":[["$","h4",null,{"className":"font-medium text-gray-900 mb-2","children":"Horas Trabalhadas"}],["$","p",null,{"className":"text-sm text-gray-600 mb-3","children":"Consolidado de horas normais e extras por período"}],["$","div",null,{"className":"flex items-center text-xs text-gray-500","children":[["$","span",null,{"className":"bg-green-100 text-green-800 px-2 py-1 rounded","children":"Horas"}],["$","span",null,{"className":"ml-2","children":"7 campos"}]]}]]}],["$","div",null,{"className":"border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer","children":[["$","h4",null,{"className":"font-medium text-gray-900 mb-2","children":"Dashboard Executivo"}],["$","p",null,{"className":"text-sm text-gray-600 mb-3","children":"KPIs e métricas principais para gestores"}],["$","div",null,{"className":"flex items-center text-xs text-gray-500","children":[["$","span",null,{"className":"bg-purple-100 text-purple-800 px-2 py-1 rounded","children":"KPIs"}],["$","span",null,{"className":"ml-2","children":"12 campos"}]]}]]}]]}]]}]
14:["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 mb-4","children":"Como Usar o Construtor"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600","children":[["$","div",null,{"children":[["$","h4",null,{"className":"font-medium text-gray-900 mb-2","children":"Passos Básicos"}],["$","ol",null,{"className":"space-y-1 list-decimal list-inside","children":[["$","li",null,{"children":"Selecione um template ou comece do zero"}],["$","li",null,{"children":"Arraste campos da biblioteca para o design"}],["$","li",null,{"children":"Configure filtros e agrupamentos"}],["$","li",null,{"children":"Escolha o tipo de visualização"}],["$","li",null,{"children":"Execute o preview e ajuste conforme necessário"}],["$","li",null,{"children":"Salve e exporte o relatório"}]]}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"font-medium text-gray-900 mb-2","children":"Recursos Avançados"}],["$","ul",null,{"className":"space-y-1","children":[["$","li",null,{"children":"• Fórmulas personalizadas e cálculos"}],["$","li",null,{"children":"• Filtros dinâmicos e condicionais"}],["$","li",null,{"children":"• Agrupamentos e subtotais automáticos"}],["$","li",null,{"children":"• Gráficos interativos e dashboards"}],["$","li",null,{"children":"• Agendamento de execução automática"}],["$","li",null,{"children":"• Compartilhamento e colaboração"}]]}]]}]]}]]}]
15:["$","$L19",null,{"children":["$L1a",["$","$L1b",null,{"promise":"$@1c"}]]}]
16:["$","$1","h",{"children":[null,[["$","$L1d",null,{"children":"$L1e"}],null],["$","$L1f",null,{"children":["$","div",null,{"hidden":true,"children":["$","$20",null,{"fallback":null,"children":"$L21"}]}]}]]}]
1e:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
1a:null
22:I[8175,[],"IconMark"]
1c:{"metadata":[["$","title","0",{"children":"Construtor de Relatórios - RLPONTO"}],["$","meta","1",{"name":"description","content":"Construa relatórios personalizados com interface visual intuitiva"}],["$","meta","2",{"name":"author","content":"Your Name"}],["$","meta","3",{"name":"keywords","content":"Next.js,React,TypeScript,Tailwind CSS"}],["$","meta","4",{"name":"creator","content":"Your Name"}],["$","meta","5",{"name":"robots","content":"index, follow"}],["$","meta","6",{"property":"og:title","content":"My App"}],["$","meta","7",{"property":"og:description","content":"A professional Next.js application"}],["$","meta","8",{"property":"og:url","content":"http://************"}],["$","meta","9",{"property":"og:site_name","content":"My App"}],["$","meta","10",{"property":"og:locale","content":"en_US"}],["$","meta","11",{"property":"og:type","content":"website"}],["$","meta","12",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","13",{"name":"twitter:title","content":"My App"}],["$","meta","14",{"name":"twitter:description","content":"A professional Next.js application"}],["$","link","15",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L22","16",{}]],"error":null,"digest":"$undefined"}
21:"$1c:metadata"
