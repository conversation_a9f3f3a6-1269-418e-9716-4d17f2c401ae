"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6011],{1007:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1243:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4355:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(9946).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},6011:(e,a,r)=>{r.d(a,{q:()=>h});var t=r(5155),s=r(2115),c=r(3769),n=r(1154),i=r(1007),l=r(4355);let o=(0,r(9946).A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);var d=r(646),u=r(1243);function h(e){let{onCapture:a,onError:h,mode:m="recognize",className:p="",autoStart:x=!0}=e,g=(0,s.useRef)(null),f=(0,s.useRef)(null),y=(0,s.useRef)(null),[w,v]=(0,s.useState)("idle"),[j,N]=(0,s.useState)(!1),[b,C]=(0,s.useState)(0),[k,E]=(0,s.useState)(""),[M,A]=(0,s.useState)(!1),[R,z]=(0,s.useState)(!1),F=(0,s.useRef)(null),D=(0,s.useRef)(null),I=(0,s.useCallback)(async()=>{try{v("initializing");try{let{FaceDetection:e}=await r.e(6827).then(r.t.bind(r,6827,23)),{FaceMesh:a}=await r.e(7103).then(r.t.bind(r,7103,23)),t=new e({locateFile:e=>"https://cdn.jsdelivr.net/npm/@mediapipe/face_detection/".concat(e)});t.setOptions({model:"short",minDetectionConfidence:.5}),t.onResults(e=>{q(e)});let s=new a({locateFile:e=>"https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/".concat(e)});s.setOptions({maxNumFaces:1,refineLandmarks:!0,minDetectionConfidence:.5,minTrackingConfidence:.5}),s.onResults(e=>{L(e)}),F.current=t,D.current=s,console.log("✅ MediaPipe inicializado com sucesso")}catch(e){console.warn("⚠️ MediaPipe n\xe3o dispon\xedvel, usando modo simplificado:",e),F.current=null,D.current=null,N(!0),C(.8)}v("ready")}catch(e){console.error("Erro ao inicializar sistema de captura:",e),E("Erro ao carregar sistema de captura facial"),v("error"),null==h||h("Erro ao inicializar sistema de captura")}},[h]),S=(0,s.useCallback)(async()=>{try{if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)throw Error("C\xe2mera n\xe3o suportada neste navegador");let e=await navigator.permissions.query({name:"camera"});console.log("Permiss\xe3o da c\xe2mera:",e.state);let a=await navigator.mediaDevices.getUserMedia({video:{width:{ideal:640},height:{ideal:480},facingMode:"user"}});return g.current&&(g.current.srcObject=a,y.current=a),!0}catch(a){console.error("Erro ao acessar c\xe2mera:",a);let e="Erro ao acessar c\xe2mera";return"NotAllowedError"===a.name?e="Permiss\xe3o de c\xe2mera negada. Clique no \xedcone da c\xe2mera na barra de endere\xe7os e permita o acesso.":"NotFoundError"===a.name?e="Nenhuma c\xe2mera encontrada no dispositivo.":"NotSupportedError"===a.name?e="C\xe2mera n\xe3o suportada neste navegador.":"NotReadableError"===a.name?e="C\xe2mera est\xe1 sendo usada por outro aplicativo.":a.message&&(e=a.message),E(e),v("error"),null==h||h(e),!1}},[h]),q=(0,s.useCallback)(e=>{if(!f.current||!g.current)return;let a=f.current,r=a.getContext("2d");if(r)if(r.clearRect(0,0,a.width,a.height),r.drawImage(g.current,0,0,a.width,a.height),e.detections&&e.detections.length>0){let t=e.detections[0];N(!0);let s=P(t);C(s),T(r,t,a.width,a.height),s>.8&&"capturing"===w&&!R&&(z(!0),setTimeout(()=>{"capturing"!==w||R||_(t)},500))}else N(!1),C(0)},[w]),L=(0,s.useCallback)(e=>{e.multiFaceLandmarks&&e.multiFaceLandmarks.length>0&&e.multiFaceLandmarks[0]},[]),P=e=>{let a=e.boundingBox,r=e.score,t=a.width*a.height;return(t<.1||t>.6)&&(r*=.7),Math.sqrt(Math.pow(a.xCenter-.5,2)+Math.pow(a.yCenter-.5,2))>.2&&(r*=.8),Math.min(r,1)},T=(e,a,r,t)=>{let s=a.boundingBox,c=(s.xCenter-s.width/2)*r,n=(s.yCenter-s.height/2)*t,i=s.width*r,l=s.height*t,o=b>.8?"#00ff00":b>.5?"#ffff00":"#ff0000";e.strokeStyle=o,e.lineWidth=3,e.strokeRect(c,n,i,l),e.fillStyle=o,e.font="16px Arial",e.fillText("".concat(Math.round(100*b),"%"),c,n-10)},_=async e=>{try{v("processing");let r=await $(e);if(r)v("success"),null==a||a(r);else throw Error("Falha ao extrair caracter\xedsticas faciais")}catch(e){console.error("Erro ao processar captura:",e),v("error"),E("Erro ao processar face capturada"),null==h||h("Erro ao processar captura facial")}finally{z(!1)}},$=async e=>{if(!f.current||!g.current)return null;let a=f.current;if(!a.getContext("2d"))return null;let r=document.createElement("canvas");r.width=a.width,r.height=a.height;let t=r.getContext("2d");if(!t)return null;t.drawImage(g.current,0,0,a.width,a.height);let s=e.boundingBox,c=Math.max(0,(s.xCenter-s.width/2)*a.width),n=Math.max(0,(s.yCenter-s.height/2)*a.height),i=Math.min(a.width-c,s.width*a.width),l=Math.min(a.height-n,s.height*a.height),o=t.getImageData(c,n,i,l),d=document.createElement("canvas");d.width=i,d.height=l;let u=d.getContext("2d");return u?(u.putImageData(o,0,0),d.toDataURL("image/jpeg",.8)):null},O=(0,s.useCallback)(async()=>{"ready"===w&&(v("capturing"),E(""),F.current||setTimeout(()=>{U()},1e3))},[w]),U=(0,s.useCallback)(async()=>{if(g.current&&f.current)try{v("processing");let e=f.current,r=e.getContext("2d");if(!r)throw Error("Erro ao acessar canvas");r.drawImage(g.current,0,0,e.width,e.height);let t=e.toDataURL("image/jpeg",.8);v("success"),null==a||a(t)}catch(e){console.error("Erro na captura simples:",e),v("error"),E("Erro ao capturar imagem"),null==h||h("Erro ao capturar imagem")}},[a,h]),B=(0,s.useCallback)(()=>{v("ready"),E(""),z(!1)},[]),H=(0,s.useCallback)(async()=>{if(g.current&&F.current&&M)try{await F.current.send({image:g.current}),D.current&&await D.current.send({image:g.current})}catch(e){console.error("Erro ao processar frame:",e)}},[M]);return(0,s.useEffect)(()=>(I(),()=>{y.current&&y.current.getTracks().forEach(e=>e.stop())}),[I]),(0,s.useEffect)(()=>{"ready"===w&&x&&S()},[w,S,x]),(0,s.useEffect)(()=>{if(!M)return;let e=setInterval(H,100);return()=>clearInterval(e)},[M,H]),(0,t.jsxs)("div",{className:"space-y-4 ".concat(p),children:[(0,t.jsxs)("div",{className:"relative bg-gray-900 rounded-lg overflow-hidden",children:[(0,t.jsx)("video",{ref:g,autoPlay:!0,playsInline:!0,muted:!0,className:"w-full h-64 object-cover",onLoadedData:()=>{A(!0)}}),(0,t.jsx)("canvas",{ref:f,width:640,height:480,className:"absolute inset-0 w-full h-full object-cover"}),(0,t.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:["initializing"===w&&(0,t.jsxs)("div",{className:"bg-black bg-opacity-50 text-white p-4 rounded-lg flex items-center space-x-2",children:[(0,t.jsx)(n.A,{className:"h-5 w-5 animate-spin"}),(0,t.jsx)("span",{children:"Carregando..."})]}),"capturing"===w&&(0,t.jsx)("div",{className:"bg-red-500 bg-opacity-20 border-2 border-red-500 rounded-lg p-2",children:(0,t.jsx)("div",{className:"w-4 h-4 bg-red-500 rounded-full animate-pulse"})})]}),j&&(0,t.jsx)("div",{className:"absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{className:"text-sm",children:[Math.round(100*b),"%"]})]})})]}),(0,t.jsx)("div",{className:"text-center ".concat((()=>{switch(w){case"success":return"text-green-600";case"error":return"text-red-600";case"ready":return b>.8?"text-green-600":"text-yellow-600";default:return"text-blue-600"}})()),children:(0,t.jsx)("p",{className:"font-medium",children:(()=>{switch(w){case"initializing":return"Inicializando sistema de reconhecimento...";case"ready":return j?"Face detectada - Qualidade: ".concat(Math.round(100*b),"%"):"Posicione seu rosto na c\xe2mera";case"capturing":return"Capturando face...";case"processing":return"Processando caracter\xedsticas faciais...";case"success":return"Face capturada com sucesso!";case"error":return k||"Erro no sistema";default:return"Clique para iniciar"}})()})}),(0,t.jsxs)("div",{className:"flex justify-center space-x-3",children:["ready"===w&&!x&&!y.current&&(0,t.jsxs)(c.$n,{onClick:S,variant:"primary",className:"flex items-center space-x-2",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Iniciar C\xe2mera"})]}),"ready"===w&&y.current&&(0,t.jsxs)(c.$n,{onClick:O,disabled:!j||b<.5,variant:"primary",className:"flex items-center space-x-2",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"register"===m?"Cadastrar Face":"Reconhecer"})]}),"capturing"===w&&(0,t.jsxs)(c.$n,{onClick:B,variant:"secondary",className:"flex items-center space-x-2",children:[(0,t.jsx)(o,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Cancelar"})]}),"success"===w&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-green-600",children:[(0,t.jsx)(d.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Sucesso!"})]}),"error"===w&&(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsxs)(c.$n,{onClick:async()=>{v("ready"),E(""),await S()},variant:"primary",className:"flex items-center space-x-2",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Tentar Novamente"})]}),(0,t.jsxs)(c.$n,{onClick:()=>{v("ready"),E("")},variant:"secondary",className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Tentar Novamente"})]})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 text-center space-y-1",children:[(0,t.jsx)("p",{children:"• Mantenha o rosto bem iluminado"}),(0,t.jsx)("p",{children:"• Olhe diretamente para a c\xe2mera"}),(0,t.jsx)("p",{children:"• Mantenha-se a uma dist\xe2ncia confort\xe1vel"}),"register"===m&&(0,t.jsx)("p",{className:"text-blue-600 font-medium",children:"• Modo cadastro: sua face ser\xe1 salva no sistema"})]})]})}}}]);