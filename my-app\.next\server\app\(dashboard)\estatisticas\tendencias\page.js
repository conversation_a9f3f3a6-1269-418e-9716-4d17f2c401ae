"use strict";(()=>{var a={};a.id=9739,a.ids=[9739],a.modules={261:a=>{a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{a.exports=require("util")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},35862:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},37533:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},40918:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},51465:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},53148:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},59394:(a,b,c)=>{c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["estatisticas",{children:["tendencias",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,94881)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\estatisticas\\tendencias\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,57675)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,85389)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\estatisticas\\tendencias\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/estatisticas/tendencias/page",pathname:"/estatisticas/tendencias",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/estatisticas/tendencias/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65516:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},72845:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},83799:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94881:(a,b,c)=>{c.r(b),c.d(b,{default:()=>q,metadata:()=>o});var d=c(37413),e=c(51465),f=c(37533),g=c(65516),h=c(83799),i=c(72845),j=c(40918),k=c(53148),l=c(35862),m=c(4536),n=c.n(m);let o={title:"An\xe1lise de Tend\xeancias - RLPONTO",description:"An\xe1lise temporal e identifica\xe7\xe3o de tend\xeancias"},p={resumo:{tendenciaGeral:"positiva",crescimentoMensal:2.3,sazonalidade:"detectada",previsaoProximoMes:89.2},metricas:[{nome:"Pontualidade",atual:94.2,anterior:92.1,tendencia:"up",variacao:2.1},{nome:"Produtividade",atual:87.6,anterior:85.3,tendencia:"up",variacao:2.3},{nome:"Absente\xedsmo",atual:3.8,anterior:4.3,tendencia:"down",variacao:-.5},{nome:"Horas Extras",atual:127.5,anterior:112.3,tendencia:"up",variacao:15.2}],padroesSazonais:[{periodo:"Janeiro",pontualidade:91.2,produtividade:84.1,absenteismo:5.2},{periodo:"Fevereiro",pontualidade:89.8,produtividade:82.3,absenteismo:6.1},{periodo:"Mar\xe7o",pontualidade:92.5,produtividade:86.7,absenteismo:4.8},{periodo:"Abril",pontualidade:93.1,produtividade:87.2,absenteismo:4.2},{periodo:"Maio",pontualidade:94.2,produtividade:88.9,absenteismo:3.8},{periodo:"Junho",pontualidade:95.1,produtividade:89.5,absenteismo:3.2}],alertas:[{tipo:"tendencia_negativa",titulo:"Queda na Pontualidade - Sextas-feiras",descricao:"Detectada tend\xeancia de queda na pontualidade \xe0s sextas-feiras nos \xfaltimos 3 meses.",severidade:"media"},{tipo:"sazonalidade",titulo:"Padr\xe3o Sazonal - In\xedcio do Ano",descricao:"Historicamente, janeiro e fevereiro apresentam menor produtividade.",severidade:"baixa"},{tipo:"crescimento",titulo:"Melhoria Cont\xednua - Departamento TI",descricao:"Departamento de TI mostra crescimento consistente na produtividade.",severidade:"positiva"}]};function q(){return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(n(),{href:"/estatisticas",children:(0,d.jsx)("button",{className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,d.jsx)(e.A,{className:"h-5 w-5 text-gray-600"})})}),(0,d.jsx)("div",{className:"p-2 bg-purple-600 rounded-lg",children:(0,d.jsx)(f.A,{className:"h-8 w-8 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"An\xe1lise de Tend\xeancias"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Identifica\xe7\xe3o de padr\xf5es temporais e previs\xf5es"})]})]}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsxs)("select",{className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,d.jsx)("option",{children:"\xdaltimos 6 meses"}),(0,d.jsx)("option",{children:"\xdaltimo ano"}),(0,d.jsx)("option",{children:"\xdaltimos 2 anos"})]}),(0,d.jsxs)("button",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[(0,d.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Exportar"]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,d.jsx)(h.A,{className:"h-6 w-6 text-green-600"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Tend\xeancia Geral"}),(0,d.jsx)("p",{className:"text-lg font-semibold text-green-600 capitalize",children:p.resumo.tendenciaGeral})]})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(i.A,{className:"h-6 w-6 text-blue-600"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Crescimento Mensal"}),(0,d.jsxs)("p",{className:"text-lg font-semibold text-blue-600",children:["+",p.resumo.crescimentoMensal,"%"]})]})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,d.jsx)(j.A,{className:"h-6 w-6 text-yellow-600"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Sazonalidade"}),(0,d.jsx)("p",{className:"text-lg font-semibold text-yellow-600 capitalize",children:p.resumo.sazonalidade})]})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,d.jsx)(k.A,{className:"h-6 w-6 text-purple-600"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Previs\xe3o Pr\xf3ximo M\xeas"}),(0,d.jsxs)("p",{className:"text-lg font-semibold text-purple-600",children:[p.resumo.previsaoProximoMes,"%"]})]})]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Evolu\xe7\xe3o Temporal das M\xe9tricas"})}),(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)("div",{className:"h-80 bg-gray-100 rounded-lg flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(f.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-500 text-lg",children:"Gr\xe1fico de Tend\xeancias Temporais"}),(0,d.jsx)("p",{className:"text-sm text-gray-400",children:"Implementa\xe7\xe3o com Chart.js ou Recharts"}),(0,d.jsx)("p",{className:"text-xs text-gray-400 mt-2",children:"Mostrar\xe1 linhas de tend\xeancia para pontualidade, produtividade e absente\xedsmo"})]})})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Compara\xe7\xe3o Mensal"})}),(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)("div",{className:"space-y-4",children:p.metricas.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:`w-3 h-3 rounded-full mr-3 ${"up"===a.tendencia?"bg-green-500":"bg-red-500"}`}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:a.nome}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:["Anterior: ",a.anterior,"Horas Extras"===a.nome?"h":"%"]})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[a.atual,"Horas Extras"===a.nome?"h":"%"]}),(0,d.jsxs)("p",{className:`text-xs font-medium ${"up"===a.tendencia?"text-green-600":"text-red-600"}`,children:[a.variacao>0?"+":"",a.variacao,"Horas Extras"===a.nome?"h":"%"]})]})]},b))})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Padr\xf5es Sazonais"})}),(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)("div",{className:"space-y-3",children:p.padroesSazonais.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-2 hover:bg-gray-50 rounded",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-900",children:a.periodo}),(0,d.jsxs)("div",{className:"flex space-x-4 text-xs",children:[(0,d.jsxs)("span",{className:"text-green-600",children:["P: ",a.pontualidade,"%"]}),(0,d.jsxs)("span",{className:"text-blue-600",children:["Prod: ",a.produtividade,"%"]}),(0,d.jsxs)("span",{className:"text-red-600",children:["Abs: ",a.absenteismo,"%"]})]})]},b))})})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Alertas e Insights"})}),(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)("div",{className:"space-y-4",children:p.alertas.map((a,b)=>(0,d.jsx)("div",{className:`p-4 rounded-lg border ${"positiva"===a.severidade?"bg-green-50 border-green-200":"media"===a.severidade?"bg-yellow-50 border-yellow-200":"bg-blue-50 border-blue-200"}`,children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:"positiva"===a.severidade?(0,d.jsx)(h.A,{className:"h-5 w-5 text-green-600"}):"media"===a.severidade?(0,d.jsx)(l.A,{className:"h-5 w-5 text-yellow-600"}):(0,d.jsx)(f.A,{className:"h-5 w-5 text-blue-600"})}),(0,d.jsxs)("div",{className:"ml-3",children:[(0,d.jsx)("h3",{className:`text-sm font-medium ${"positiva"===a.severidade?"text-green-800":"media"===a.severidade?"text-yellow-800":"text-blue-800"}`,children:a.titulo}),(0,d.jsx)("p",{className:`text-sm mt-1 ${"positiva"===a.severidade?"text-green-700":"media"===a.severidade?"text-yellow-700":"text-blue-700"}`,children:a.descricao})]})]})},b))})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Previs\xf5es Baseadas em Tend\xeancias"})}),(0,d.jsx)("div",{className:"p-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,d.jsx)(h.A,{className:"h-8 w-8 text-green-600 mx-auto mb-2"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-green-800",children:"Pr\xf3ximo M\xeas"}),(0,d.jsx)("p",{className:"text-sm text-green-700",children:"Baseado na tend\xeancia atual, espera-se melhoria de 1.5% na produtividade geral."})]}),(0,d.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,d.jsx)(j.A,{className:"h-8 w-8 text-blue-600 mx-auto mb-2"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-blue-800",children:"Pr\xf3ximo Trimestre"}),(0,d.jsx)("p",{className:"text-sm text-blue-700",children:"Padr\xe3o sazonal indica poss\xedvel queda de 2% no absente\xedsmo durante o ver\xe3o."})]}),(0,d.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,d.jsx)(i.A,{className:"h-8 w-8 text-purple-600 mx-auto mb-2"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-purple-800",children:"Longo Prazo"}),(0,d.jsx)("p",{className:"text-sm text-purple-700",children:"Tend\xeancia de crescimento sustent\xe1vel com melhoria cont\xednua de 0.5% ao m\xeas."})]})]})})]})]})})})}}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,3327,1684,5203],()=>b(b.s=59394));module.exports=c})();