(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1332],{2355:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},3052:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3904:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4186:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4516:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5525:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5695:(e,a,r)=>{"use strict";var t=r(8999);r.o(t,"usePathname")&&r.d(a,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(a,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(a,{useSearchParams:function(){return t.useSearchParams}})},6533:(e,a,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,7194)),Promise.resolve().then(r.bind(r,9829))},7194:(e,a,r)=>{"use strict";r.d(a,{FuncionarioWizard:()=>J});var t=r(5155),s=r(2115),i=r(5695);class o{recordMetric(e,a,r){let t={name:e,value:a,timestamp:Date.now(),metadata:r};this.metrics.push(t),this.metrics.length>this.maxMetrics&&(this.metrics=this.metrics.slice(-this.maxMetrics))}async measureAsync(e,a,r){let t=performance.now();try{let s=await a(),i=performance.now()-t;return this.recordMetric(e,i,{...r,success:!0}),s}catch(s){let a=performance.now()-t;throw this.recordMetric(e,a,{...r,success:!1,error:s.message}),s}}measure(e,a,r){let t=performance.now();try{let s=a(),i=performance.now()-t;return this.recordMetric(e,i,{...r,success:!0}),s}catch(s){let a=performance.now()-t;throw this.recordMetric(e,a,{...r,success:!1,error:s.message}),s}}getMetrics(e){let a=[...this.metrics];return(null==e?void 0:e.name)&&(a=a.filter(a=>a.name.includes(e.name))),(null==e?void 0:e.since)&&(a=a.filter(a=>a.timestamp>=e.since)),(null==e?void 0:e.limit)&&(a=a.slice(-e.limit)),a.sort((e,a)=>a.timestamp-e.timestamp)}getStats(e){let a=this.metrics;if(e&&(a=a.filter(a=>a.name===e)),0===a.length)return{count:0,average:0,min:0,max:0,p95:0,p99:0};let r=a.map(e=>e.value).sort((e,a)=>e-a),t=r.length,s=r.reduce((e,a)=>e+a,0),i=r[0],o=r[t-1],l=Math.floor(.95*t),n=Math.floor(.99*t),c=r[l]||o,d=r[n]||o;return{count:t,average:s/t,min:i,max:o,p95:c,p99:d}}collectWebVitals(){let e=new PerformanceObserver(e=>{for(let a of e.getEntries())"paint"===a.entryType&&"first-contentful-paint"===a.name&&this.recordMetric("web-vitals.fcp",a.startTime,{type:"web-vital",description:"First Contentful Paint"})});try{e.observe({entryTypes:["paint"]})}catch(e){console.warn("Performance Observer n\xe3o suportado:",e)}let a=new PerformanceObserver(e=>{let a=e.getEntries(),r=a[a.length-1];this.recordMetric("web-vitals.lcp",r.startTime,{type:"web-vital",description:"Largest Contentful Paint"})});try{a.observe({entryTypes:["largest-contentful-paint"]})}catch(e){console.warn("LCP Observer n\xe3o suportado:",e)}let r=0,t=new PerformanceObserver(e=>{for(let a of e.getEntries())a.hadRecentInput||(r+=a.value);this.recordMetric("web-vitals.cls",r,{type:"web-vital",description:"Cumulative Layout Shift"})});try{t.observe({entryTypes:["layout-shift"]})}catch(e){console.warn("CLS Observer n\xe3o suportado:",e)}}monitorMemory(){if(!("memory"in performance))return;let e=performance.memory;this.recordMetric("memory.used",e.usedJSHeapSize,{type:"memory",total:e.totalJSHeapSize,limit:e.jsHeapSizeLimit})}cleanup(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:864e5,a=Date.now()-e,r=this.metrics.length;this.metrics=this.metrics.filter(e=>e.timestamp>a);let t=r-this.metrics.length;t>0&&console.log("\uD83E\uDDF9 Performance: Removidas ".concat(t," m\xe9tricas antigas"))}constructor(){this.metrics=[],this.maxMetrics=1e3}}let l=new o;l.collectWebVitals(),setInterval(()=>{l.monitorMemory()},3e4),setInterval(()=>{l.cleanup()},36e5);var n=r(9509),c=function(e){return e[e.DEBUG=0]="DEBUG",e[e.INFO=1]="INFO",e[e.WARN=2]="WARN",e[e.ERROR=3]="ERROR",e[e.FATAL=4]="FATAL",e}({});class d{shouldLog(e){return e>=this.minLevel}formatLogEntry(e){let{timestamp:a,levelName:r,message:t,context:s,error:i,performance:o}=e;if(!this.isDevelopment)return JSON.stringify(e);{let e="[".concat(a,"] ").concat(r,": ").concat(t);return s&&Object.keys(s).length>0&&(e+="\n  Context: ".concat(JSON.stringify(s,null,2))),i&&(e+="\n  Error: ".concat(i.name,": ").concat(i.message),i.stack&&(e+="\n  Stack: ".concat(i.stack))),o&&(e+="\n  Performance: ".concat(o.duration,"ms"),o.memory&&(e+=" (".concat(o.memory,"MB)"))),e}}createLogEntry(e,a,r,t,s){return{timestamp:new Date().toISOString(),level:e,levelName:c[e],message:a,context:r,error:t?{name:t.name,message:t.message,stack:this.isDevelopment?t.stack:void 0}:void 0,performance:s}}writeLog(e){if(!this.shouldLog(e.level))return;let a=this.formatLogEntry(e);if(this.isDevelopment)switch(e.level){case 0:console.debug(a);break;case 1:console.info(a);break;case 2:console.warn(a);break;case 3:case 4:console.error(a)}else console.log(a)}debug(e,a){let r=this.createLogEntry(0,e,a);this.writeLog(r)}info(e,a){let r=this.createLogEntry(1,e,a);this.writeLog(r)}warn(e,a){let r=this.createLogEntry(2,e,a);this.writeLog(r)}error(e,a,r){let t=this.createLogEntry(3,e,r,a);this.writeLog(t)}fatal(e,a,r){let t=this.createLogEntry(4,e,r,a);this.writeLog(t)}performance(e,a,r){let t=this.createLogEntry(1,e,r,void 0,{duration:a,memory:this.getMemoryUsage()});this.writeLog(t)}getMemoryUsage(){return void 0!==n&&n.memoryUsage?Math.round(n.memoryUsage().heapUsed/1024/1024):0}timer(e,a){let r=Date.now();return{end:()=>{let t=Date.now()-r;return this.performance("Timer: ".concat(e),t,a),t}}}apiRequest(e,a,r){this.info("API Request: ".concat(e," ").concat(a),{...r,component:"api",action:"request",method:e,url:a})}apiResponse(e,a,r,t,s){let i=r>=400?2:1,o=this.createLogEntry(i,"API Response: ".concat(e," ").concat(a," - ").concat(r),{...s,component:"api",action:"response",method:e,url:a,status:r},void 0,{duration:t});this.writeLog(o)}validation(e,a,r,t){this.warn("Validation Error: ".concat(e," - ").concat(a),{...t,component:"validation",field:e,error:a,value:this.isProduction?"[REDACTED]":r})}auth(e,a){let r=!(arguments.length>2)||void 0===arguments[2]||arguments[2],t=arguments.length>3?arguments[3]:void 0,s=this.createLogEntry(r?1:2,"Auth: ".concat(e," - ").concat(r?"Success":"Failed"),{...t,component:"auth",action:e,userId:a,success:r});this.writeLog(s)}constructor(){this.isDevelopment=!1,this.isProduction=!0,this.minLevel=2*!!this.isProduction}}let m=new d;function u(e){return e.replace(/\D/g,"")}function h(e){let a=u(e);return 11===a.length&&/^\d{11}$/.test(a)}function x(e){let a=u(e);return/^(\d)\1{10}$/.test(a)}function p(e,a){let r=0,t=a+1;for(let s=0;s<a;s++)r+=parseInt(e[s])*(t-s);let s=r%11;return s<2?0:11-s}let g={allowTestCPFs:!1,testCPFs:[]};function f(e){let a=u(e),r=function(e){let a=e.replace(/\D/g,"");return a.length<=3?a:a.length<=6?"".concat(a.slice(0,3),".").concat(a.slice(3)):a.length<=9?"".concat(a.slice(0,3),".").concat(a.slice(3,6),".").concat(a.slice(6)):"".concat(a.slice(0,3),".").concat(a.slice(3,6),".").concat(a.slice(6,9),"-").concat(a.slice(9,11))}(e),t=[],s=!0;h(a)||(t.push("CPF deve ter 11 d\xedgitos num\xe9ricos"),s=!1),x(a)&&(t.push("CPF n\xe3o pode ser uma sequ\xeancia de n\xfameros iguais"),s=!1),s&&!function(e){let a=u(e);if(!h(a)||x(a))return!1;let r=p(a,9);if(parseInt(a[9])!==r)return!1;let t=p(a,10);return parseInt(a[10])===t}(e)&&(t.push("CPF inv\xe1lido - d\xedgitos verificadores incorretos"),s=!1);let i=g.testCPFs.includes(a);return{isValid:s||g.allowTestCPFs&&i,formatted:r,unformatted:a,errors:t,isTestCPF:i}}function v(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Telefone";if(!(null==e?void 0:e.trim()))return{isValid:!0};let r=e.replace(/\D/g,"");return r.length<10||r.length>11?{isValid:!1,error:"".concat(a," deve ter 10 ou 11 d\xedgitos")}:{isValid:!0}}function b(e){let[a,r]=e.split(":").map(Number);return 60*a+r}var j=r(9946);let y=(0,j.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);function N(e){let{steps:a,currentStepIndex:r,completedSteps:s}=e,i=new Set(s.map(e=>e.id));return(0,t.jsxs)("div",{className:"w-full",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Progresso do Cadastro"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:[r+1," de ",a.length]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat((r+1)/a.length*100,"%")}})})]}),(0,t.jsx)("div",{className:"flex items-center justify-between",children:a.map((e,s)=>{let o=i.has(e.id)||s<r,l=s===r;return(0,t.jsxs)("div",{className:"flex flex-col items-center flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center w-full",children:[s>0&&(0,t.jsx)("div",{className:"flex-1 h-1 ".concat(o?"bg-blue-600":"bg-gray-200")}),(0,t.jsx)("div",{className:"\n                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200\n                    ".concat(o?"bg-blue-600 border-blue-600 text-white":l?"bg-white border-blue-600 text-blue-600":"bg-white border-gray-300 text-gray-500","\n                  "),children:o?(0,t.jsx)(y,{className:"h-5 w-5"}):(0,t.jsx)("span",{className:"text-sm font-medium",children:s+1})}),s<a.length-1&&(0,t.jsx)("div",{className:"flex-1 h-1 ".concat(s<r?"bg-blue-600":"bg-gray-200")})]}),(0,t.jsxs)("div",{className:"mt-3 text-center max-w-[120px]",children:[(0,t.jsx)("div",{className:"text-sm font-medium ".concat(l?"text-blue-600":o?"text-gray-900":"text-gray-500"),children:e.title}),(0,t.jsx)("div",{className:"text-xs mt-1 ".concat(l?"text-blue-500":o?"text-gray-600":"text-gray-500"),children:e.description})]})]},e.id)})})]})}var w=r(3769),C=r(1007),E=r(5816);function S(e){let{data:a,onDataChange:r,onValidationChange:i}=e,{toUpperCaseText:o,toLowerCaseEmail:l,formatCPF:n,formatPhone:c,formatCEP:d}=(0,E.w3)(),[m,u]=(0,s.useState)({nomeCompleto:a.nomeCompleto||"",cpf:a.cpf||"",rg:a.rg||"",email:a.email||"",telefone:a.telefone||"",celular:a.celular||"",cep:a.cep||"",logradouro:a.logradouro||"",numero:a.numero||"",complemento:a.complemento||"",bairro:a.bairro||"",cidade:a.cidade||"",uf:a.uf||""}),[h,x]=(0,s.useState)({}),[p,g]=(0,s.useState)(!1),v=(e,a)=>{let t=a;switch(e){case"email":t=l(a);break;case"cpf":case"telefone":case"celular":case"cep":t=a.replace(/\D/g,"");break;default:t=o(a)}let s={...m,[e]:t};u(s),r(s),"cep"===e&&8===t.length&&b(t)},b=async e=>{if(8===e.length)try{g(!0);let a=await fetch("https://viacep.com.br/ws/".concat(e,"/json/")),t=await a.json();if(t.erro)x(e=>({...e,cep:"CEP n\xe3o encontrado"}));else{let a={...m,cep:e,logradouro:t.logradouro||"",bairro:t.bairro||"",cidade:t.localidade||"",uf:t.uf||""};u(a),r(a),h.cep&&x(e=>({...e,cep:""}))}}catch(e){console.error("Erro ao buscar CEP:",e),x(e=>({...e,cep:"Erro ao buscar CEP"}))}finally{g(!1)}};return(0,s.useEffect)(()=>{(()=>{let e={};if(m.nomeCompleto.trim()||(e.nomeCompleto="Nome completo \xe9 obrigat\xf3rio"),m.cpf.trim()){let a=f(m.cpf);a.isValid||(e.cpf=a.errors[0]||"CPF inv\xe1lido")}else e.cpf="CPF \xe9 obrigat\xf3rio";return m.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(m.email)&&(e.email="Email inv\xe1lido"),x(e),i(0===Object.keys(e).length)})()},[m,i]),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,t.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,t.jsx)(C.A,{className:"h-8 w-8 text-blue-600"})})}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Dados Pessoais"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Informe os dados pessoais do funcion\xe1rio"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nome Completo *"}),(0,t.jsx)(w.pd,{placeholder:"Digite o nome completo",value:m.nomeCompleto,onChange:e=>v("nomeCompleto",e.target.value),error:h.nomeCompleto})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CPF *"}),(0,t.jsx)(w.pd,{placeholder:"000.000.000-00",value:n(m.cpf),onChange:e=>v("cpf",e.target.value.replace(/\D/g,"")),error:h.cpf})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"RG"}),(0,t.jsx)(w.pd,{placeholder:"Digite o RG",value:m.rg,onChange:e=>v("rg",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,t.jsx)(w.pd,{type:"email",placeholder:"<EMAIL>",value:m.email,onChange:e=>v("email",e.target.value),error:h.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Telefone"}),(0,t.jsx)(w.pd,{placeholder:"(11) 1234-5678",value:c(m.telefone),onChange:e=>v("telefone",e.target.value.replace(/\D/g,""))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Celular"}),(0,t.jsx)(w.pd,{placeholder:"(11) 99999-9999",value:c(m.celular),onChange:e=>v("celular",e.target.value.replace(/\D/g,""))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CEP"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(w.pd,{placeholder:"00000-000",value:d(m.cep),onChange:e=>v("cep",e.target.value.replace(/\D/g,""))}),(0,t.jsx)(w.$n,{type:"button",variant:"outline",onClick:()=>b(m.cep),disabled:8!==m.cep.replace(/\D/g,"").length||p,children:p?"Buscando...":"Buscar"})]})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Logradouro"}),(0,t.jsx)(w.pd,{placeholder:"Rua, Avenida, etc.",value:m.logradouro,onChange:e=>v("logradouro",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"N\xfamero"}),(0,t.jsx)(w.pd,{placeholder:"123",value:m.numero,onChange:e=>v("numero",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Complemento"}),(0,t.jsx)(w.pd,{placeholder:"Apto, Bloco, etc.",value:m.complemento,onChange:e=>v("complemento",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Bairro"}),(0,t.jsx)(w.pd,{placeholder:"Nome do bairro",value:m.bairro,onChange:e=>v("bairro",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cidade"}),(0,t.jsx)(w.pd,{placeholder:"Nome da cidade",value:m.cidade,onChange:e=>v("cidade",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"UF"}),(0,t.jsxs)("select",{value:m.uf,onChange:e=>v("uf",e.target.value),"data-format":"preserve",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 font-semibold",children:[(0,t.jsx)("option",{value:"",children:"Selecione o Estado"}),(0,t.jsx)("option",{value:"AC",children:"Acre"}),(0,t.jsx)("option",{value:"AL",children:"Alagoas"}),(0,t.jsx)("option",{value:"AP",children:"Amap\xe1"}),(0,t.jsx)("option",{value:"AM",children:"Amazonas"}),(0,t.jsx)("option",{value:"BA",children:"Bahia"}),(0,t.jsx)("option",{value:"CE",children:"Cear\xe1"}),(0,t.jsx)("option",{value:"DF",children:"Distrito Federal"}),(0,t.jsx)("option",{value:"ES",children:"Esp\xedrito Santo"}),(0,t.jsx)("option",{value:"GO",children:"Goi\xe1s"}),(0,t.jsx)("option",{value:"MA",children:"Maranh\xe3o"}),(0,t.jsx)("option",{value:"MT",children:"Mato Grosso"}),(0,t.jsx)("option",{value:"MS",children:"Mato Grosso do Sul"}),(0,t.jsx)("option",{value:"MG",children:"Minas Gerais"}),(0,t.jsx)("option",{value:"PA",children:"Par\xe1"}),(0,t.jsx)("option",{value:"PB",children:"Para\xedba"}),(0,t.jsx)("option",{value:"PR",children:"Paran\xe1"}),(0,t.jsx)("option",{value:"PE",children:"Pernambuco"}),(0,t.jsx)("option",{value:"PI",children:"Piau\xed"}),(0,t.jsx)("option",{value:"RJ",children:"Rio de Janeiro"}),(0,t.jsx)("option",{value:"RN",children:"Rio Grande do Norte"}),(0,t.jsx)("option",{value:"RS",children:"Rio Grande do Sul"}),(0,t.jsx)("option",{value:"RO",children:"Rond\xf4nia"}),(0,t.jsx)("option",{value:"RR",children:"Roraima"}),(0,t.jsx)("option",{value:"SC",children:"Santa Catarina"}),(0,t.jsx)("option",{value:"SP",children:"S\xe3o Paulo"}),(0,t.jsx)("option",{value:"SE",children:"Sergipe"}),(0,t.jsx)("option",{value:"TO",children:"Tocantins"})]})]})]})]})}var A=r(7576);let k=(0,j.A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var P=r(3904);function D(e){let{data:a,onDataChange:r,onValidationChange:i}=e,{matricula:o,isLoading:l,error:n,isGenerated:c,gerarMatricula:d}=function(e){let[a,r]=(0,s.useState)(e||""),[t,i]=(0,s.useState)(!1),[o,l]=(0,s.useState)(null),[n,c]=(0,s.useState)(!1),d=(0,s.useCallback)(async()=>{try{i(!0),l(null);let e=await fetch("/api/matricula",{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok){let a=await e.json();throw Error(a.error||"Erro ao gerar matr\xedcula")}let a=await e.json();if(a.success)r(a.matricula),c(!0),console.log("Matr\xedcula gerada (n\xe3o reservada):",a.matricula);else throw Error(a.error||"Erro ao gerar matr\xedcula")}catch(e){l(e instanceof Error?e.message:"Erro desconhecido"),console.error("Erro ao gerar matr\xedcula:",e)}finally{i(!1)}},[]);return{matricula:a,isLoading:t,error:o,isGenerated:n,gerarMatricula:d,verificarDisponibilidade:(0,s.useCallback)(async e=>{try{let a=await fetch("/api/matricula",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({matricula:e})});if(!a.ok)throw Error("Erro ao verificar disponibilidade");return(await a.json()).disponivel}catch(e){return console.error("Erro ao verificar disponibilidade:",e),!1}},[]),resetar:(0,s.useCallback)(()=>{r(""),i(!1),l(null),c(!1)},[])}}(a.matricula),[m,u]=(0,s.useState)({matricula:a.matricula||"",cargo:a.cargo||"",setor:a.setor||"",dataAdmissao:a.dataAdmissao||"",salario:a.salario||"",cargaHoraria:a.cargaHoraria||"40",horarioEntrada:a.horarioEntrada||"",horarioSaida:a.horarioSaida||"",intervaloInicio:a.intervaloInicio||"",intervaloFim:a.intervaloFim||"",observacoes:a.observacoes||""}),[h,x]=(0,s.useState)({});(0,s.useEffect)(()=>{m.matricula||o||d()},[]),(0,s.useEffect)(()=>{o&&o!==m.matricula&&p("matricula",o)},[o]),(0,s.useEffect)(()=>{if(o&&o!==m.matricula){let e={...m,matricula:o};u(e),r(e)}},[o]),(0,s.useEffect)(()=>{n?x(e=>({...e,matricula:n})):x(e=>{let{matricula:a,...r}=e;return r})},[n]);let p=(e,a)=>{let t={...m,[e]:a};u(t),r(t)};return(0,s.useEffect)(()=>{(()=>{let e={};return m.matricula.trim()||(e.matricula="Matr\xedcula \xe9 obrigat\xf3ria"),m.cargo.trim()||(e.cargo="Cargo \xe9 obrigat\xf3rio"),m.setor.trim()||(e.setor="Setor \xe9 obrigat\xf3rio"),m.dataAdmissao||(e.dataAdmissao="Data de admiss\xe3o \xe9 obrigat\xf3ria"),m.horarioEntrada||(e.horarioEntrada="Hor\xe1rio de entrada \xe9 obrigat\xf3rio"),m.horarioSaida||(e.horarioSaida="Hor\xe1rio de sa\xedda \xe9 obrigat\xf3rio"),m.cargaHoraria<=0&&(e.cargaHoraria="Carga hor\xe1ria deve ser maior que zero"),x(e),i(0===Object.keys(e).length)})()},[m,i]),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,t.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,t.jsx)(A.A,{className:"h-8 w-8 text-green-600"})})}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Dados Profissionais"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Informe os dados profissionais do funcion\xe1rio"}),(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4",children:(0,t.jsx)("div",{className:"flex items-center text-yellow-800",children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("strong",{children:"\uD83D\uDCDD Nota:"})," Os dados ser\xe3o salvos apenas na etapa final de confirma\xe7\xe3o."]})})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(k,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Matr\xedcula *"}),c&&(0,t.jsx)("span",{className:"text-xs text-green-600 bg-green-100 px-2 py-1 rounded",children:"Gerada automaticamente"})]})}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(w.pd,{placeholder:l?"Gerando matr\xedcula...":"Matr\xedcula ser\xe1 gerada automaticamente",value:m.matricula||"Aguardando gera\xe7\xe3o...",readOnly:!0,disabled:l,className:"".concat(c?"bg-green-50 border-green-200":"bg-gray-50"," cursor-not-allowed")}),l&&(0,t.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,t.jsx)(P.A,{className:"h-4 w-4 text-blue-500 animate-spin"})}),!l&&!m.matricula&&(0,t.jsx)("button",{type:"button",onClick:d,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700",title:"Gerar nova matr\xedcula",children:(0,t.jsx)(P.A,{className:"h-4 w-4"})})]}),c&&(0,t.jsxs)("p",{className:"text-xs text-green-600 mt-1",children:["✓ Matr\xedcula gerada automaticamente no formato sequencial (0001, 0002, etc.)",(0,t.jsx)("br",{}),"✓ Sistema garante que n\xe3o haver\xe1 duplicatas ou reutiliza\xe7\xe3o"]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"\uD83D\uDD12 A matr\xedcula \xe9 gerada automaticamente e n\xe3o pode ser editada. \xc9 \xfanica e nunca ser\xe1 reutilizada."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Data de Admiss\xe3o *"}),(0,t.jsx)("input",{type:"date",value:m.dataAdmissao,onChange:e=>p("dataAdmissao",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(h.dataAdmissao?"border-red-500":"border-gray-300")}),h.dataAdmissao&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.dataAdmissao})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cargo *"}),(0,t.jsx)(w.pd,{placeholder:"Digite o cargo",value:m.cargo,onChange:e=>p("cargo",e.target.value),error:h.cargo})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Setor *"}),(0,t.jsxs)("select",{value:m.setor,onChange:e=>p("setor",e.target.value),"data-format":"preserve",className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(h.setor?"border-red-500":"border-gray-300"),children:[(0,t.jsx)("option",{value:"",children:"Selecione o setor"}),(0,t.jsx)("option",{value:"ADMINISTRA\xc7\xc3O",children:"ADMINISTRA\xc7\xc3O"}),(0,t.jsx)("option",{value:"PRODU\xc7\xc3O",children:"PRODU\xc7\xc3O"}),(0,t.jsx)("option",{value:"VENDAS",children:"VENDAS"}),(0,t.jsx)("option",{value:"RECURSOS HUMANOS",children:"RECURSOS HUMANOS"}),(0,t.jsx)("option",{value:"TECNOLOGIA",children:"TECNOLOGIA"}),(0,t.jsx)("option",{value:"FINANCEIRO",children:"FINANCEIRO"}),(0,t.jsx)("option",{value:"MARKETING",children:"MARKETING"}),(0,t.jsx)("option",{value:"OPERA\xc7\xd5ES",children:"OPERA\xc7\xd5ES"})]}),h.setor&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.setor})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sal\xe1rio"}),(0,t.jsx)(w.pd,{placeholder:"R$ 0,00",value:m.salario.toString(),onChange:e=>p("salario",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Carga Hor\xe1ria Semanal *"}),(0,t.jsx)("input",{type:"number",placeholder:"40",value:m.cargaHoraria.toString(),onChange:e=>p("cargaHoraria",parseInt(e.target.value)||0),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(h.cargaHoraria?"border-red-500":"border-gray-300"),min:"1",max:"60"}),h.cargaHoraria&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.cargaHoraria})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Hor\xe1rio de Entrada *"}),(0,t.jsx)("input",{type:"time",value:m.horarioEntrada,onChange:e=>p("horarioEntrada",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(h.horarioEntrada?"border-red-500":"border-gray-300")}),h.horarioEntrada&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.horarioEntrada})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Hor\xe1rio de Sa\xedda *"}),(0,t.jsx)("input",{type:"time",value:m.horarioSaida,onChange:e=>p("horarioSaida",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(h.horarioSaida?"border-red-500":"border-gray-300")}),h.horarioSaida&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.horarioSaida})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"In\xedcio do Intervalo"}),(0,t.jsx)("input",{type:"time",value:m.intervaloInicio,onChange:e=>p("intervaloInicio",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Fim do Intervalo"}),(0,t.jsx)("input",{type:"time",value:m.intervaloFim,onChange:e=>p("intervaloFim",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Observa\xe7\xf5es"}),(0,t.jsx)("textarea",{placeholder:"Observa\xe7\xf5es adicionais sobre o funcion\xe1rio...",value:m.observacoes,onChange:e=>p("observacoes",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),m.horarioEntrada&&m.horarioSaida&&(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Resumo dos Hor\xe1rios"}),(0,t.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Expediente:"})," ",m.horarioEntrada," \xe0s ",m.horarioSaida]}),m.intervaloInicio&&m.intervaloFim&&(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Intervalo:"})," ",m.intervaloInicio," \xe0s ",m.intervaloFim]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Carga Hor\xe1ria:"})," ",m.cargaHoraria,"h semanais"]})]})]})]})}var F=r(6011),V=r(4355),R=r(1284),I=r(646),O=r(4861);let M=(0,j.A)("skip-forward",[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]]);var T=r(5525),L=r(1243);function z(e){let{funcionarioId:a,onValidationChange:i,onBiometriaChange:o}=e,[l,n]=(0,s.useState)("none"),[c,d]=(0,s.useState)(""),[m]=(0,s.useState)(!0);(0,s.useEffect)(()=>{i(!0)},[i]),(0,s.useEffect)(()=>{null==o||o("success"===l)},[l,o]);let u=async e=>{if(!a){d("ID do funcion\xe1rio n\xe3o dispon\xedvel. Complete o cadastro primeiro."),n("error");return}console.log("\uD83D\uDD25 Iniciando cadastro de biometria para funcion\xe1rio:",a);try{n("capturing"),d("Processando biometria facial...");let r=await x(e);if(!r)throw Error("Falha ao extrair caracter\xedsticas faciais");let t=await fetch("/api/biometria/facial",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({funcionarioId:a,landmarks:r,imageData:e,quality:.9})}),s=await t.json();if(s.success)n("success"),d("Biometria facial cadastrada com sucesso!");else throw Error(s.error||"Erro ao cadastrar biometria facial")}catch(e){console.error("Erro no cadastro facial:",e),n("error"),d(e instanceof Error?e.message:"Erro no cadastro facial")}},h=()=>{n("skipped"),d("Biometria facial n\xe3o cadastrada. Voc\xea pode cadastrar depois na p\xe1gina do funcion\xe1rio.")},x=async e=>{try{let a=new Image,t=document.createElement("canvas"),s=t.getContext("2d");return new Promise((i,o)=>{a.onload=async()=>{try{t.width=a.width,t.height=a.height,null==s||s.drawImage(a,0,0);let{FaceMesh:e}=await r.e(7103).then(r.t.bind(r,7103,23)),l=new e({locateFile:e=>"https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/".concat(e)});l.onResults(e=>{if(e.multiFaceLandmarks&&e.multiFaceLandmarks[0]){let a=e.multiFaceLandmarks[0].map(e=>({x:e.x,y:e.y,z:e.z||0}));i(a)}else o(Error("Nenhuma face detectada na imagem"))}),await l.send({image:t})}catch(e){console.error("Erro ao processar landmarks:",e),o(Error("Erro ao processar landmarks faciais"))}},a.onerror=()=>{o(Error("Erro ao carregar imagem"))},a.src=e})}catch(e){throw console.error("Erro na extra\xe7\xe3o de landmarks:",e),Error("Falha na extra\xe7\xe3o de landmarks faciais")}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,t.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,t.jsx)(V.A,{className:"h-8 w-8 text-green-600"})})}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Cadastro de Biometria Facial"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Configure o reconhecimento facial para facilitar o registro de ponto"})]}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(R.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-900 mb-1",children:m?"Cadastro Opcional":"Cadastro Obrigat\xf3rio"}),(0,t.jsx)("div",{className:"text-blue-700 space-y-1",children:m?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("p",{children:"• O cadastro de biometria facial \xe9 opcional durante o registro"}),(0,t.jsx)("p",{children:"• Pode ser feito agora ou posteriormente na p\xe1gina do funcion\xe1rio"}),(0,t.jsx)("p",{children:"• Facilita o registro de ponto sem necessidade de cart\xe3o ou senha"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("p",{children:"• O cadastro de biometria facial \xe9 obrigat\xf3rio"}),(0,t.jsx)("p",{children:"• Necess\xe1rio para utilizar o sistema de controle de ponto"}),(0,t.jsx)("p",{children:"• Garante seguran\xe7a e precis\xe3o nos registros"})]})})]})]})}),c&&(0,t.jsx)("div",{className:"p-4 rounded-lg border ".concat("success"===l?"bg-green-50 border-green-200 text-green-800":"error"===l?"bg-red-50 border-red-200 text-red-800":"skipped"===l?"bg-yellow-50 border-yellow-200 text-yellow-800":"bg-blue-50 border-blue-200 text-blue-800"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:["success"===l&&(0,t.jsx)(I.A,{className:"h-5 w-5"}),"error"===l&&(0,t.jsx)(O.A,{className:"h-5 w-5"}),"skipped"===l&&(0,t.jsx)(M,{className:"h-5 w-5"}),"capturing"===l&&(0,t.jsx)(V.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:c})]})}),"none"===l&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Capturar Biometria Facial"}),(0,t.jsx)(F.q,{mode:"register",onCapture:u,onError:e=>{n("error"),d(e)}})]}),m&&(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsxs)(w.$n,{onClick:h,variant:"outline",className:"flex items-center space-x-2",children:[(0,t.jsx)(M,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Pular Esta Etapa"})]})})]}),"success"===l&&(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(I.A,{className:"h-16 w-16 text-green-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Biometria Cadastrada!"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"O funcion\xe1rio j\xe1 pode utilizar o reconhecimento facial para registrar ponto."}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-green-700",children:[(0,t.jsx)(T.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"font-medium",children:"Dados protegidos com criptografia"})]})})]}),"error"===l&&(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(O.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Erro no Cadastro"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"N\xe3o foi poss\xedvel cadastrar a biometria facial. Tente novamente."}),(0,t.jsxs)("div",{className:"flex justify-center space-x-3",children:[(0,t.jsxs)(w.$n,{onClick:()=>{n("none"),d("")},variant:"primary",className:"flex items-center space-x-2",children:[(0,t.jsx)(V.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Tentar Novamente"})]}),m&&(0,t.jsxs)(w.$n,{onClick:h,variant:"outline",className:"flex items-center space-x-2",children:[(0,t.jsx)(M,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Pular Esta Etapa"})]})]})]}),"skipped"===l&&(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(L.A,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Etapa Pulada"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"A biometria facial n\xe3o foi cadastrada. Voc\xea pode fazer isso depois."}),(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Como cadastrar depois:"}),(0,t.jsx)("p",{children:"Funcion\xe1rios → [Nome] → Biometria → Cadastrar Biometria Facial"})]})})]}),(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(T.A,{className:"h-5 w-5 text-gray-600 mt-0.5"}),(0,t.jsxs)("div",{className:"text-sm text-gray-700",children:[(0,t.jsx)("h4",{className:"font-medium mb-1",children:"Seguran\xe7a e Privacidade"}),(0,t.jsxs)("ul",{className:"space-y-1",children:[(0,t.jsx)("li",{children:"• Apenas caracter\xedsticas matem\xe1ticas s\xe3o armazenadas"}),(0,t.jsx)("li",{children:"• Dados criptografados com padr\xe3o militar (AES-256)"}),(0,t.jsx)("li",{children:"• Conformidade total com LGPD"}),(0,t.jsx)("li",{children:"• Possibilidade de remo\xe7\xe3o a qualquer momento"})]})]})]})})]})}var H=r(4516),G=r(4186);function B(e){var a,r;let{data:i,onValidationChange:o}=e;(0,s.useEffect)(()=>{o(!!(i.nomeCompleto&&i.cpf&&i.matricula&&i.cargo&&i.setor&&i.dataAdmissao&&i.horarioEntrada&&i.horarioSaida&&i.cargaHoraria))},[i,o]);let l=e=>{if(!e)return"";let a=e.replace(/\D/g,"");return a.length<=10?a.replace(/(\d{2})(\d{4})(\d{4})/,"($1) $2-$3"):a.replace(/(\d{2})(\d{5})(\d{4})/,"($1) $2-$3")};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,t.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,t.jsx)(I.A,{className:"h-8 w-8 text-green-600"})})}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Confirma\xe7\xe3o dos Dados"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Revise todas as informa\xe7\xf5es antes de finalizar o cadastro"}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(I.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,t.jsxs)("div",{className:"text-blue-800",children:[(0,t.jsx)("p",{className:"font-medium",children:"⚠️ Importante:"}),(0,t.jsxs)("p",{className:"text-sm mt-1",children:["Os dados do funcion\xe1rio ser\xe3o salvos permanentemente apenas quando voc\xea clicar em",(0,t.jsx)("strong",{children:' "Salvar e Finalizar Cadastro"'})," abaixo."]})]})]})})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(C.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Dados Pessoais"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Nome Completo"}),(0,t.jsx)("p",{className:"text-gray-900",children:i.nomeCompleto||"-"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"CPF"}),(0,t.jsx)("p",{className:"text-gray-900",children:i.cpf?i.cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/,"$1.$2.$3-$4"):"-"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"RG"}),(0,t.jsx)("p",{className:"text-gray-900",children:i.rg||"-"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,t.jsx)("p",{className:"text-gray-900",children:i.email||"-"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Telefone"}),(0,t.jsx)("p",{className:"text-gray-900",children:i.telefone?l(i.telefone):"-"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Celular"}),(0,t.jsx)("p",{className:"text-gray-900",children:i.celular?l(i.celular):"-"})]})]}),(i.logradouro||i.cidade)&&(0,t.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(H.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Endere\xe7o"})]}),(0,t.jsx)("p",{className:"text-gray-900",children:[i.logradouro,i.numero,i.complemento,i.bairro,i.cidade,i.uf].filter(Boolean).join(", ")||"-"}),i.cep&&(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["CEP: ",i.cep]})]})]}),(0,t.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(A.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Dados Profissionais"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Matr\xedcula"}),(0,t.jsx)("p",{className:"text-gray-900",children:i.matricula||"-"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Data de Admiss\xe3o"}),(0,t.jsx)("p",{className:"text-gray-900",children:i.dataAdmissao?(a=i.dataAdmissao)?new Date(a).toLocaleDateString("pt-BR"):"":"-"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Cargo"}),(0,t.jsx)("p",{className:"text-gray-900",children:i.cargo||"-"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Setor"}),(0,t.jsx)("p",{className:"text-gray-900 capitalize",children:i.setor||"-"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Sal\xe1rio"}),(0,t.jsx)("p",{className:"text-gray-900",children:i.salario?(r=i.salario)?("string"==typeof r?parseFloat(r):r).toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"":"-"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Carga Hor\xe1ria"}),(0,t.jsx)("p",{className:"text-gray-900",children:i.cargaHoraria?"".concat(i.cargaHoraria,"h semanais"):"-"})]})]}),(i.horarioEntrada||i.horarioSaida)&&(0,t.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(G.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Hor\xe1rios de Trabalho"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Expediente"}),(0,t.jsx)("p",{className:"text-gray-900",children:i.horarioEntrada&&i.horarioSaida?"".concat(i.horarioEntrada," \xe0s ").concat(i.horarioSaida):"-"})]}),i.intervaloInicio&&i.intervaloFim&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Intervalo"}),(0,t.jsx)("p",{className:"text-gray-900",children:"".concat(i.intervaloInicio," \xe0s ").concat(i.intervaloFim)})]})]})]}),i.observacoes&&(0,t.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Observa\xe7\xf5es"}),(0,t.jsx)("p",{className:"text-gray-900 mt-1",children:i.observacoes})]})]})]}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(I.A,{className:"h-5 w-5 text-green-400"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"✅ Funcion\xe1rio cadastrado com sucesso!"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-green-700",children:(0,t.jsx)("p",{children:'O funcion\xe1rio foi salvo no sistema e j\xe1 pode come\xe7ar a utilizar o controle de ponto. Clique em "Finalizar Cadastro" para retornar \xe0 lista de funcion\xe1rios.'})})]})]})})]})}var $=r(2355),W=r(3052),q=r(1154);let U=[{id:"pessoal",title:"Dados Pessoais",description:"Informa\xe7\xf5es b\xe1sicas do funcion\xe1rio"},{id:"profissional",title:"Dados Profissionais",description:"Cargo, setor e hor\xe1rios"},{id:"biometria",title:"Biometria Facial",description:"Cadastrar reconhecimento facial (opcional)"},{id:"confirmacao",title:"Confirma\xe7\xe3o",description:"Revisar e confirmar dados"}];function J(){let e=(0,i.useRouter)(),{measureAsync:a}={recordMetric:l.recordMetric.bind(l),measureAsync:l.measureAsync.bind(l),measure:l.measure.bind(l),getMetrics:l.getMetrics.bind(l),getStats:l.getStats.bind(l)},[r,o]=(0,s.useState)(!1),[n,c]=(0,s.useState)(null),[d,u]=(0,s.useState)({}),h=function(e){let{steps:a,initialStep:r=0}=e,[t,i]=(0,s.useState)(r),[o,l]=(0,s.useState)({}),[n,c]=(0,s.useState)(new Set),d=a[t],m=0===t,u=t===a.length-1,h=(0,s.useCallback)(()=>{u||i(e=>e+1)},[u]),x=(0,s.useCallback)(()=>{m||i(e=>e-1)},[m]),p=(0,s.useCallback)(e=>{e>=0&&e<a.length&&i(e)},[a.length]),g=(0,s.useCallback)((e,a)=>{l(r=>({...r,[e]:a}))},[]),f=(0,s.useCallback)(e=>{c(a=>new Set([...a,e]))},[]),v=(0,s.useCallback)(e=>{var a;return null!=(a=o[e])&&a},[o]),b=(0,s.useCallback)(e=>n.has(e),[n]),j=v(null==d?void 0:d.id),y=!m,N=(t+1)/a.length*100;return{currentStep:d,currentStepIndex:t,steps:a,isFirstStep:m,isLastStep:u,canGoNext:j,canGoPrevious:y,progress:N,goToNext:h,goToPrevious:x,goToStep:p,setStepValid:g,markStepCompleted:f,isStepValid:v,isStepCompleted:b}}({steps:U,initialStep:0});(0,s.useEffect)(()=>()=>{o(!1),c(null)},[]);let x=e=>{u(a=>({...a,...e}))};(0,s.useEffect)(()=>{let e=()=>d.nomeCompleto||d.cpf||d.cargo,a=a=>{if(e()&&!r)return a.preventDefault(),a.returnValue="Voc\xea tem dados n\xe3o salvos. Tem certeza que deseja sair?",a.returnValue},t=a=>{if(e()&&!r){if(!window.confirm("Voc\xea tem dados n\xe3o salvos. Tem certeza que deseja voltar? Os dados ser\xe3o perdidos."))return void window.history.pushState(null,"",window.location.href);u({})}};return window.addEventListener("beforeunload",a),window.addEventListener("popstate",t),window.history.pushState(null,"",window.location.href),()=>{window.removeEventListener("beforeunload",a),window.removeEventListener("popstate",t)}},[d,r]);let p=()=>{let e=function(e){var a,r,t;let s=function(e){let a=function(e){var a,r,t,s,i,o;let l=[],n=[],c=(null==(a=e.nomeCompleto||"")?void 0:a.trim())?a.trim().length<2?{isValid:!1,error:"Nome deve ter pelo menos 2 caracteres"}:a.trim().length>150?{isValid:!1,error:"Nome n\xe3o pode ter mais de 150 caracteres"}:a.trim().split(" ").filter(e=>e.length>0).length<2?{isValid:!1,warning:"Recomendado informar nome e sobrenome completos"}:{isValid:!0}:{isValid:!1,error:"Nome completo \xe9 obrigat\xf3rio"};c.isValid?c.warning&&n.push(c.warning):l.push(c.error);let d=function(e){if(!(null==e?void 0:e.trim()))return{isValid:!1,error:"CPF \xe9 obrigat\xf3rio"};let a=f(e);return a.isValid?a.isTestCPF?{isValid:!0,warning:"CPF de teste - apenas para desenvolvimento"}:{isValid:!0}:{isValid:!1,error:a.errors[0]||"CPF inv\xe1lido"}}(e.cpf||"");if(d.isValid?d.warning&&n.push(d.warning):l.push(d.error),e.email){let a=(null==(r=e.email)?void 0:r.trim())?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)?{isValid:!0}:{isValid:!1,error:"Email inv\xe1lido"}:{isValid:!0};a.isValid||l.push(a.error)}if(e.telefone){let a=v(e.telefone,"Telefone");a.isValid||l.push(a.error)}if(e.celular){let a=v(e.celular,"Celular");a.isValid||l.push(a.error)}if(e.endereco){let a=(null==(o=e.endereco.cep||"")?void 0:o.trim())?8!==o.replace(/\D/g,"").length?{isValid:!1,error:"CEP deve ter 8 d\xedgitos"}:{isValid:!0}:{isValid:!1,error:"CEP \xe9 obrigat\xf3rio"};a.isValid||l.push(a.error),(null==(t=e.endereco.logradouro)?void 0:t.trim())||l.push("Logradouro \xe9 obrigat\xf3rio"),(null==(s=e.endereco.cidade)?void 0:s.trim())||l.push("Cidade \xe9 obrigat\xf3ria"),(null==(i=e.endereco.uf)?void 0:i.trim())||l.push("UF \xe9 obrigat\xf3ria")}return{isValid:0===l.length,errors:l,warnings:n}}(e),r=function(e){var a,r,t;let s=[],i=[];if(!e.dadosProfissionais)return s.push("Dados profissionais s\xe3o obrigat\xf3rios"),{isValid:!1,errors:s,warnings:i};let o=e.dadosProfissionais,l=(null==(a=o.matricula||"")?void 0:a.trim())?a.length<3||a.length>10?{isValid:!1,error:"Matr\xedcula deve ter entre 3 e 10 caracteres"}:{isValid:!0}:{isValid:!1,error:"Matr\xedcula \xe9 obrigat\xf3ria"};l.isValid||s.push(l.error);let n=(null==(r=o.cargo||"")?void 0:r.trim())?r.trim().length<2?{isValid:!1,error:"Cargo deve ter pelo menos 2 caracteres"}:{isValid:!0}:{isValid:!1,error:"Cargo \xe9 obrigat\xf3rio"};n.isValid||s.push(n.error);let c=(null==(t=o.setor||"")?void 0:t.trim())?{isValid:!0}:{isValid:!1,error:"Setor \xe9 obrigat\xf3rio"};c.isValid||s.push(c.error);let d=function(e){if(!(null==e?void 0:e.trim()))return{isValid:!1,error:"Data de admiss\xe3o \xe9 obrigat\xf3ria"};let a=new Date(e);if(isNaN(a.getTime()))return{isValid:!1,error:"Data de admiss\xe3o inv\xe1lida"};let r=new Date,t=new Date;return(t.setFullYear(r.getFullYear()-1),a>r)?{isValid:!1,error:"Data de admiss\xe3o n\xe3o pode ser futura"}:a<t?{isValid:!0,warning:"Data de admiss\xe3o \xe9 anterior a 1 ano"}:{isValid:!0}}(o.dataAdmissao||"");if(d.isValid?d.warning&&i.push(d.warning):s.push(d.error),o.horarioTrabalho){let e=function(e,a){if(!(null==e?void 0:e.trim())||!(null==a?void 0:a.trim()))return{isValid:!1,error:"Hor\xe1rios de entrada e sa\xedda s\xe3o obrigat\xf3rios"};let r=b(e),t=b(a);if(r>=t)return{isValid:!1,error:"Hor\xe1rio de sa\xedda deve ser posterior ao de entrada"};let s=(t-r)/60;return s>12?{isValid:!1,error:"Jornada n\xe3o pode exceder 12 horas"}:s<4?{isValid:!0,warning:"Jornada inferior a 4 horas"}:{isValid:!0}}(o.horarioTrabalho.entrada||"",o.horarioTrabalho.saida||"");e.isValid?e.warning&&i.push(e.warning):s.push(e.error)}return{isValid:0===s.length,errors:s,warnings:i}}(e);return{isValid:a.isValid&&r.isValid,errors:[...a.errors,...r.errors],warnings:[...a.warnings,...r.warnings]}}(e),i=[];return(null==(a=e.telefone)?void 0:a.trim())||(null==(r=e.celular)?void 0:r.trim())||(null==(t=e.email)?void 0:t.trim())||i.push("Pelo menos um meio de contato \xe9 obrigat\xf3rio (telefone, celular ou email)"),e.endereco&&e.endereco.logradouro&&e.endereco.cidade&&e.endereco.uf||i.push("Endere\xe7o completo \xe9 obrigat\xf3rio para submiss\xe3o"),{isValid:s.isValid&&0===i.length,errors:[...s.errors,...i],warnings:s.warnings}}(d);return{isValid:e.isValid,errors:e.errors}},g=async()=>{if(m.debug("Iniciando submiss\xe3o de funcion\xe1rio",{component:"FuncionarioWizard",action:"handleSubmit",isSubmitting:r,hasData:!!d}),r)return void m.warn("Tentativa de submiss\xe3o m\xfaltipla ignorada",{component:"FuncionarioWizard",action:"handleSubmit"});let a=p();if(!a.isValid){c("Dados incompletos:\n".concat(a.errors.join("\n"))),m.error("Valida\xe7\xe3o falhou antes do salvamento: "+a.errors.join(", "));return}try{var t,s,i,l;o(!0),c(null),m.debug("Iniciando processo de submiss\xe3o",{component:"FuncionarioWizard",action:"submit"});let a=m.timer("Valida\xe7\xe3o de dados"),r=p().errors;if(a.end(),r.length>0)throw m.validation("funcionario","Dados inv\xe1lidos para submiss\xe3o",r,{component:"FuncionarioWizard"}),Error("Dados inv\xe1lidos:\n\n• ".concat(r.join("\n• ")));let n=m.timer("Transforma\xe7\xe3o de dados"),u=(e=>{var a,r,t,s,i,o,l,n,c,d,m,u,h,x,p,g,f,v,b,j,y;let N={nomeCompleto:null==(a=e.nomeCompleto)?void 0:a.trim(),cpf:null==(r=e.cpf)?void 0:r.replace(/\D/g,""),rg:null==(t=e.rg)?void 0:t.trim(),email:null==(s=e.email)?void 0:s.trim(),telefone:null==(i=e.telefone)?void 0:i.replace(/\D/g,""),celular:null==(o=e.celular)?void 0:o.replace(/\D/g,""),endereco:{cep:null==(l=e.cep)?void 0:l.replace(/\D/g,""),logradouro:null==(n=e.logradouro)?void 0:n.trim(),numero:null==(c=e.numero)?void 0:c.trim(),complemento:null==(d=e.complemento)?void 0:d.trim(),bairro:null==(m=e.bairro)?void 0:m.trim(),cidade:null==(u=e.cidade)?void 0:u.trim(),uf:null==(h=e.uf)?void 0:h.trim()},dadosProfissionais:{matricula:null==(x=e.matricula)?void 0:x.trim(),cargo:null==(p=e.cargo)?void 0:p.trim(),setor:null==(g=e.setor)?void 0:g.trim(),dataAdmissao:e.dataAdmissao,salario:"string"==typeof e.salario?parseFloat(e.salario)||0:e.salario||0,cargaHoraria:"string"==typeof e.cargaHoraria?parseInt(e.cargaHoraria)||40:e.cargaHoraria||40,horarioTrabalho:{entrada:null==(f=e.horarioEntrada)?void 0:f.trim(),saida:null==(v=e.horarioSaida)?void 0:v.trim(),intervaloInicio:null==(b=e.intervaloInicio)?void 0:b.trim(),intervaloFim:null==(j=e.intervaloFim)?void 0:j.trim()}},observacoes:null==(y=e.observacoes)?void 0:y.trim()};return Object.values(N.endereco).some(e=>e&&e.length>0)||delete N.endereco,N})(d);n.end(),m.apiRequest("POST","/api/funcionarios",{component:"FuncionarioWizard"});let h=await fetch("/api/funcionarios",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(u)});if(m.apiResponse("POST","/api/funcionarios",h.status,Date.now()-performance.now(),{component:"FuncionarioWizard"}),!h.ok){let e=await h.json();m.error("Erro na API de funcion\xe1rios",void 0,{component:"FuncionarioWizard",status:h.status,errorData:e});let a=e.error||"Erro ao cadastrar funcion\xe1rio";if(e.details&&Array.isArray(e.details)){let r=e.details.map(e=>"object"==typeof e&&e.message&&e.path?"".concat(e.path.join("."),": ").concat(e.message):e.toString()).join("; ");a+="\n\nDetalhes: ".concat(r)}throw Error(a)}let x=await h.json();m.info("Funcion\xe1rio cadastrado com sucesso",{component:"FuncionarioWizard",action:"submit",funcionarioId:null==(t=x.funcionario)?void 0:t.id,matricula:null==(i=x.funcionario)||null==(s=i.dadosProfissionais)?void 0:s.matricula}),m.info("Funcion\xe1rio salvo com sucesso, redirecionando",{component:"FuncionarioWizard",action:"redirectAfterSave",funcionarioId:null==(l=x.funcionario)?void 0:l.id}),setTimeout(()=>{try{e.replace("/funcionarios?success=funcionario-cadastrado")}catch(e){m.error("Erro no router.replace, usando window.location",{error:e instanceof Error?e.message:String(e),component:"FuncionarioWizard"}),window.location.href="/funcionarios?success=funcionario-cadastrado"}},150)}catch(e){console.error("\uD83D\uDD25 ERRO CAPTURADO:",e),c(e instanceof Error?e.message:"Erro desconhecido")}finally{m.debug("Finalizando processo de submiss\xe3o",{component:"FuncionarioWizard",action:"submit"}),o(!1)}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(N,{steps:h.steps,currentStepIndex:h.currentStepIndex,completedSteps:h.steps.slice(0,h.currentStepIndex)}),n&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"text-red-800",children:[(0,t.jsx)("strong",{children:"Erro ao cadastrar funcion\xe1rio:"}),(0,t.jsx)("div",{className:"mt-2 whitespace-pre-line",children:n})]})}),(0,t.jsx)("div",{className:"min-h-[400px]",children:(()=>{switch(h.currentStep.id){case"pessoal":return(0,t.jsx)(S,{data:d,onDataChange:x,onValidationChange:e=>h.setStepValid("pessoal",e)});case"profissional":return(0,t.jsx)(D,{data:d,onDataChange:x,onValidationChange:e=>h.setStepValid("profissional",e)});case"biometria":return(0,t.jsx)(z,{funcionarioId:d.matricula||"temp-id",onValidationChange:e=>h.setStepValid("biometria",e),onBiometriaChange:e=>{m.info("Status da biometria alterado",{component:"FuncionarioWizard",action:"biometriaChange",hasBiometria:e}),u(a=>({...a,biometriaCadastrada:e}))}});case"confirmacao":return m.debug("Renderizando step de confirma\xe7\xe3o",{component:"FuncionarioWizard",step:"confirmacao",hasData:!!d}),(0,t.jsx)(B,{data:d,onValidationChange:e=>{m.debug("Valida\xe7\xe3o de confirma\xe7\xe3o alterada",{component:"FuncionarioWizard",step:"confirmacao",isValid:e}),h.setStepValid("confirmacao",e)}});default:return null}})()}),(0,t.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[(0,t.jsx)("div",{children:!h.isFirstStep&&(0,t.jsxs)(w.$n,{variant:"outline",onClick:h.goToPrevious,disabled:r,children:[(0,t.jsx)($.A,{className:"h-4 w-4 mr-2"}),"Anterior"]})}),(0,t.jsx)("div",{className:"flex space-x-3",children:0===h.currentStepIndex?(0,t.jsxs)(w.$n,{onClick:h.goToNext,disabled:!h.canGoNext||r,variant:"primary",children:["Pr\xf3ximo",(0,t.jsx)(W.A,{className:"h-4 w-4 ml-2"})]}):1===h.currentStepIndex?(0,t.jsxs)(w.$n,{onClick:()=>{m.debug("Avan\xe7ando para biometria",{component:"FuncionarioWizard",action:"nextToBiometria",canGoNext:h.canGoNext}),h.goToNext()},disabled:!h.canGoNext||r,variant:"primary",children:["Pr\xf3ximo",(0,t.jsx)(W.A,{className:"h-4 w-4 ml-2"})]}):2===h.currentStepIndex?(0,t.jsxs)(w.$n,{onClick:h.goToNext,disabled:!h.canGoNext||r,variant:"primary",children:["Pr\xf3ximo",(0,t.jsx)(W.A,{className:"h-4 w-4 ml-2"})]}):3===h.currentStepIndex?(0,t.jsx)(w.$n,{onClick:()=>{m.info("Iniciando salvamento final do funcion\xe1rio",{component:"FuncionarioWizard",action:"salvarFinal"}),g()},variant:"primary",disabled:r||!h.canGoNext,children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(q.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Salvando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y,{className:"h-4 w-4 mr-2"}),"Salvar e Finalizar Cadastro"]})}):null})]})]})}},7576:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])}},e=>{e.O(0,[6874,4285,3769,6011,8441,5964,7358],()=>e(e.s=6533)),_N_E=e.O()}]);