(()=>{var a={};a.id=3381,a.ids=[3381],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3217:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>R,patchFetch:()=>Q,routeModule:()=>M,serverHooks:()=>P,workAsyncStorage:()=>N,workUnitAsyncStorage:()=>O});var d={};c.r(d),c.d(d,{DELETE:()=>L,GET:()=>K,POST:()=>I,PUT:()=>J});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(50639),w=c(14250);let x={FACE_OVAL:[10,338,297,332,284,251,389,356,454,323,361,288,397,365,379,378,400,377,152,148,176,149,150,136,172,58,132,93,234,127,162,21,54,103,67,109],LEFT_EYE:[33,7,163,144,145,153,154,155,133,173,157,158,159,160,161,246],RIGHT_EYE:[362,382,381,380,374,373,390,249,263,466,388,387,386,385,384,398],LEFT_EYEBROW:[46,53,52,51,48,115,131,134,102,49,220,305],RIGHT_EYEBROW:[276,283,282,295,285,336,296,334,293,300,276,283],NOSE:[1,2,5,4,6,168,8,9,10,151,195,197,196,3,51,48,115,131,134,102,49,220,305,281,275,274,278,279,360,363],MOUTH:[61,84,17,314,405,320,307,375,321,308,324,318,402,317,14,87,178,88,95,78,191,80,81,82,13,312,311,310,415,308]};class y{static{this.FEATURE_VECTOR_SIZE=128}static{this.QUALITY_THRESHOLD=.7}static generateTemplate(a,b,c=1){if(a.length<468)throw Error("Landmarks insuficientes para gerar template");if(c<this.QUALITY_THRESHOLD)throw Error(`Qualidade insuficiente: ${c}. M\xednimo: ${this.QUALITY_THRESHOLD}`);let d=this.normalizeLandmarks(a),e=this.extractFeatures(d);return{id:this.generateTemplateId(e),landmarks:d,features:e,metadata:{quality:c,timestamp:new Date().toISOString(),imageData:b}}}static normalizeLandmarks(a){let b=a.map(a=>a.x),c=a.map(a=>a.y),d=Math.min(...b),e=Math.min(...c),f=Math.max(Math.max(...b)-d,Math.max(...c)-e);return a.map(a=>({x:(a.x-d)/f,y:(a.y-e)/f,z:a.z||0}))}static extractFeatures(a){let b=[];for(b.push(...this.extractDistanceFeatures(a)),b.push(...this.extractAngleFeatures(a)),b.push(...this.extractProportionFeatures(a)),b.push(...this.extractGeometricFeatures(a));b.length<this.FEATURE_VECTOR_SIZE;)b.push(0);return b.slice(0,this.FEATURE_VECTOR_SIZE)}static extractDistanceFeatures(a){let b=[],c=this.getCenterPoint(a,x.LEFT_EYE),d=this.getCenterPoint(a,x.RIGHT_EYE);b.push(this.euclideanDistance(c,d));let e=this.getCenterPoint(a,x.NOSE);b.push(this.euclideanDistance(c,e)),b.push(this.euclideanDistance(d,e));let f=this.getCenterPoint(a,x.MOUTH);b.push(this.euclideanDistance(e,f)),b.push(this.getFeatureWidth(a,x.LEFT_EYE)),b.push(this.getFeatureWidth(a,x.RIGHT_EYE)),b.push(this.getFeatureWidth(a,x.MOUTH));let g=a[10],h=a[152];b.push(this.euclideanDistance(g,h));let i=a[234],j=a[454];for(b.push(this.euclideanDistance(i,j));b.length<40;)b.push(0);return b.slice(0,40)}static extractAngleFeatures(a){let b=[];for(b.push(this.getEyeAngle(a,x.LEFT_EYE),this.getEyeAngle(a,x.RIGHT_EYE)),b.push(this.getMouthAngle(a)),b.push(this.getNoseAngle(a)),b.push(this.getBrowAngle(a,x.LEFT_EYEBROW),this.getBrowAngle(a,x.RIGHT_EYEBROW));b.length<30;)b.push(0);return b.slice(0,30)}static extractProportionFeatures(a){let b=[],c=this.euclideanDistance(a[234],a[454]);for(b.push(c/this.euclideanDistance(a[10],a[152])),b.push(this.euclideanDistance(this.getCenterPoint(a,x.LEFT_EYE),this.getCenterPoint(a,x.RIGHT_EYE))/c),b.push(this.getFeatureWidth(a,x.NOSE)/c),b.push(this.getFeatureWidth(a,x.MOUTH)/c);b.length<25;)b.push(0);return b.slice(0,25)}static extractGeometricFeatures(a){let b=[];for(b.push(this.calculateFacialSymmetry(a)),b.push(this.calculateFaceCurvature(a));b.length<33;)b.push(0);return b.slice(0,33)}static getCenterPoint(a,b){let c=b.filter(b=>b<a.length),d=c.reduce((b,c)=>b+a[c].x,0),e=c.reduce((b,c)=>b+a[c].y,0);return{x:d/c.length,y:e/c.length,z:0}}static euclideanDistance(a,b){return Math.sqrt(Math.pow(a.x-b.x,2)+Math.pow(a.y-b.y,2))}static getFeatureWidth(a,b){let c=b.filter(b=>b<a.length).map(b=>a[b].x);return Math.max(...c)-Math.min(...c)}static getEyeAngle(a,b){if(b.length<2)return 0;let c=a[b[0]],d=a[b[b.length-1]];return Math.atan2(d.y-c.y,d.x-c.x)}static getMouthAngle(a){let b=a[61],c=a[291];return Math.atan2(c.y-b.y,c.x-b.x)}static getNoseAngle(a){let b=a[1],c=a[2];return Math.atan2(b.y-c.y,b.x-c.x)}static getBrowAngle(a,b){if(b.length<2)return 0;let c=a[b[0]],d=a[b[b.length-1]];return Math.atan2(d.y-c.y,d.x-c.x)}static calculateFacialSymmetry(a){let b=a.reduce((a,b)=>a+b.x,0)/a.length,c=0,d=0;for(let[e,f]of[[33,362],[61,291],[234,454]])if(e<a.length&&f<a.length){let g=a[e],h=a[f];c+=1-Math.abs(Math.abs(g.x-b)-Math.abs(h.x-b)),d++}return d>0?c/d:0}static calculateFaceCurvature(a){let b=x.FACE_OVAL.filter(b=>b<a.length).map(b=>a[b]);if(b.length<3)return 0;let c=0;for(let a=1;a<b.length-1;a++){let d=b[a-1],e=b[a],f=b[a+1],g=Math.atan2(e.y-d.y,e.x-d.x),h=Math.atan2(f.y-e.y,f.x-e.x)-g;h>Math.PI&&(h-=2*Math.PI),h<-Math.PI&&(h+=2*Math.PI),c+=Math.abs(h)}return c/(b.length-2)}static generateTemplateId(a){let b=a.reduce((a,b,c)=>a+b*(c+1),0);return`face_${Math.abs(Math.round(b))}_${Date.now()}`}static compareTemplates(a,b,c=.8){if(a.features.length!==b.features.length)throw Error("Templates t\xeam dimens\xf5es diferentes");let d=this.calculateSimilarity(a.features,b.features),e=Math.min(a.metadata.quality,b.metadata.quality);return{similarity:d,confidence:e,match:d>=c&&e>=this.QUALITY_THRESHOLD,threshold:c}}static calculateSimilarity(a,b){return Math.max(0,1-Math.sqrt(a.reduce((a,c,d)=>{let e=c-b[d];return a+e*e},0))/Math.sqrt(a.length))}static validateTemplate(a){return a.features.length===this.FEATURE_VECTOR_SIZE&&a.metadata.quality>=this.QUALITY_THRESHOLD&&a.landmarks.length>=468}}class z{static{this.DEFAULT_THRESHOLD=.75}static{this.HIGH_CONFIDENCE_THRESHOLD=.85}static{this.MAX_TEMPLATES_PER_PERSON=5}static{this.QUALITY_THRESHOLD=.7}addTemplate(a,b){try{if(!y.validateTemplate(b))throw Error("Template inv\xe1lido");let c=this.faceDatabase.get(a);if(c||(c={funcionarioId:a,templates:[],metadata:{createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),totalSamples:0}}),c.templates.some(a=>y.compareTemplates(b,a,.95).match))return console.warn(`Template duplicado detectado para funcion\xe1rio ${a}`),!1;return c.templates.push(b),c.metadata.updatedAt=new Date().toISOString(),c.metadata.totalSamples++,c.templates.length>z.MAX_TEMPLATES_PER_PERSON&&(c.templates=this.selectBestTemplates(c.templates)),this.faceDatabase.set(a,c),!0}catch(a){return console.error("Erro ao adicionar template:",a),!1}}removeTemplates(a){return this.faceDatabase.delete(a)}async findMatch(a,b={}){let c=performance.now(),d={threshold:b.threshold||z.DEFAULT_THRESHOLD,maxCandidates:b.maxCandidates||10,useMultipleTemplates:b.useMultipleTemplates??!0,qualityFilter:b.qualityFilter||z.QUALITY_THRESHOLD};try{if(!y.validateTemplate(a))throw Error("Template de consulta inv\xe1lido");let b=null,e=[];for(let[b,c]of this.faceDatabase){let f=await this.compareWithEmployee(a,b,c,d);f&&f.similarity>=d.threshold&&e.push(f)}return e.sort((a,b)=>b.similarity-a.similarity),e.length>0&&((b=e[0]).matchTime=performance.now()-c),b}catch(a){return console.error("Erro na busca de correspond\xeancia:",a),null}}async compareWithEmployee(a,b,c,d){let e=c.templates.filter(a=>a.metadata.quality>=d.qualityFilter);if(0===e.length)return null;let f=0,g=0,h=null;if(d.useMultipleTemplates){let b=e.map(b=>y.compareTemplates(a,b,d.threshold));f=b.reduce((a,b,c)=>{let d=e[c].metadata.quality;return a+b.similarity*d},0)/e.reduce((a,b)=>a+b.metadata.quality,0),g=Math.max(...b.map(a=>a.confidence)),h=e[0]}else for(let b of e){let c=y.compareTemplates(a,b,d.threshold);c.similarity>f&&(f=c.similarity,g=c.confidence,h=b)}return!h||f<d.threshold?null:{funcionarioId:b,similarity:f,confidence:g,match:f>=d.threshold,template:h,matchTime:0}}selectBestTemplates(a){let b=[...a].sort((a,b)=>b.metadata.quality-a.metadata.quality),c=[],d=z.MAX_TEMPLATES_PER_PERSON;b.length>0&&c.push(b[0]);for(let a=1;a<b.length&&c.length<d;a++){let d=b[a];c.every(a=>!y.compareTemplates(d,a,.9).match)&&c.push(d)}return c}getDatabaseStats(){let a=this.faceDatabase.size,b=0,c=0,d=0,e=0;for(let a of this.faceDatabase.values())for(let f of(b+=a.templates.length,a.templates))f.metadata.quality>=.9?c++:f.metadata.quality>=.7?d++:e++;return{totalEmployees:a,totalTemplates:b,averageTemplatesPerEmployee:a>0?b/a:0,qualityDistribution:{high:c,medium:d,low:e}}}hasTemplates(a){let b=this.faceDatabase.get(a);return!!b&&b.templates.length>0}getTemplates(a){let b=this.faceDatabase.get(a);return b?[...b.templates]:[]}updateTemplateQuality(a,b,c){let d=this.faceDatabase.get(a);if(!d)return!1;let e=d.templates.find(a=>a.id===b);return!!e&&(e.metadata.quality=Math.max(0,Math.min(1,c)),d.metadata.updatedAt=new Date().toISOString(),!0)}clearDatabase(){this.faceDatabase.clear()}exportDatabase(){return JSON.stringify(Array.from(this.faceDatabase.entries()).map(([a,b])=>({funcionarioId:a,...b})),null,2)}importDatabase(a){try{let b=JSON.parse(a);for(let a of(this.faceDatabase.clear(),b)){let{funcionarioId:b,...c}=a;this.faceDatabase.set(b,c)}return!0}catch(a){return console.error("Erro ao importar banco de dados:",a),!1}}async benchmark(a=100){if(0===this.faceDatabase.size)throw Error("Banco de dados vazio para benchmark");let b=[],c=0,d=this.faceDatabase.values().next().value.templates[0];for(let e=0;e<a;e++){let a=performance.now(),e=await this.findMatch(d),f=performance.now();b.push(f-a),e&&e.match&&c++}return{averageMatchTime:b.reduce((a,b)=>a+b,0)/b.length,minMatchTime:Math.min(...b),maxMatchTime:Math.max(...b),successRate:c/a}}constructor(){this.faceDatabase=new Map}}class A{static{this.STORAGE_FILE="biometric-data.json"}static{this.BACKUP_PREFIX="biometric-backup-"}constructor(){this.cache=new Map,this.lastSave=0,this.SAVE_INTERVAL=5e3,this.loadFromStorage()}async save(a,b){try{let c=this.cache.get(a);if(c||(c={funcionarioId:a,templates:[],metadata:{createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),totalSamples:0}},this.cache.set(a,c)),c.templates.some(a=>a.id===b.id))return console.warn(`Template duplicado para funcion\xe1rio ${a}`),!1;return c.templates.push(b),c.metadata.updatedAt=new Date().toISOString(),c.metadata.totalSamples++,c.templates.length>5&&(c.templates=c.templates.sort((a,b)=>b.metadata.quality-a.metadata.quality).slice(0,5)),await this.debouncedSave(),!0}catch(a){return console.error("Erro ao salvar template biom\xe9trico:",a),!1}}async load(a){try{let b=this.cache.get(a);return b?b.templates:[]}catch(a){return console.error("Erro ao carregar templates:",a),[]}}async loadAll(){return new Map(this.cache)}async delete(a){try{let b=this.cache.delete(a);return b&&await this.debouncedSave(),b}catch(a){return console.error("Erro ao deletar dados biom\xe9tricos:",a),!1}}async backup(){try{let a={version:"1.0",timestamp:new Date().toISOString(),data:Object.fromEntries(this.cache)},b=JSON.stringify(a,null,2),c=`${A.BACKUP_PREFIX}${Date.now()}.json`;return console.log(`Backup criado: ${c}`),b}catch(a){throw console.error("Erro ao criar backup:",a),a}}async restore(a){try{let b=JSON.parse(a);if(!b.version||!b.data)throw Error("Formato de backup inv\xe1lido");for(let[a,c]of Object.entries(b.data))if(!this.validateEntry(c))throw Error(`Dados inv\xe1lidos para funcion\xe1rio ${a}`);return this.cache=new Map(Object.entries(b.data)),await this.saveToStorage(),console.log(`Backup restaurado: ${Object.keys(b.data).length} funcion\xe1rios`),!0}catch(a){return console.error("Erro ao restaurar backup:",a),!1}}async loadFromStorage(){}async saveToStorage(){try{Object.fromEntries(this.cache),this.lastSave=Date.now()}catch(a){console.error("Erro ao salvar dados biom\xe9tricos:",a)}}async debouncedSave(){Date.now()-this.lastSave>this.SAVE_INTERVAL?await this.saveToStorage():setTimeout(()=>this.saveToStorage(),this.SAVE_INTERVAL)}validateEntry(a){return a&&"string"==typeof a.funcionarioId&&Array.isArray(a.templates)&&a.metadata&&"string"==typeof a.metadata.createdAt&&"string"==typeof a.metadata.updatedAt&&"number"==typeof a.metadata.totalSamples}getStats(){let a=this.cache.size,b=0,c=0,d=0;for(let a of this.cache.values())for(let c of(b+=a.templates.length,a.templates))d+=c.metadata.quality;return b>0&&(c=d/b),{totalFuncionarios:a,totalTemplates:b,avgQuality:Math.round(100*c)/100,cacheSize:JSON.stringify(Object.fromEntries(this.cache)).length}}}let B=new A,C=new z,D=!1;async function E(){if(!D)try{let a=await B.loadAll();for(let[b,c]of a)for(let a of c.templates)C.addTemplate(b,a);D=!0,console.log(`Matcher inicializado com ${a.size} funcion\xe1rios`)}catch(a){console.error("Erro ao inicializar matcher:",a),D=!0}}let F=v.Ik({funcionarioId:v.Yj().min(1,"ID do funcion\xe1rio \xe9 obrigat\xf3rio"),landmarks:v.YO(v.Ik({x:v.ai(),y:v.ai(),z:v.ai().optional()})).min(468,"Landmarks insuficientes"),imageData:v.Yj().optional(),quality:v.ai().min(0).max(1)}),G=v.Ik({landmarks:v.YO(v.Ik({x:v.ai(),y:v.ai(),z:v.ai().optional()})).min(468,"Landmarks insuficientes"),imageData:v.Yj().optional(),quality:v.ai().min(0).max(1),threshold:v.ai().min(0).max(1).optional()}),H=v.Ik({funcionarioId:v.Yj().min(1)});async function I(a){try{await E();let b=await a.json(),{funcionarioId:c,landmarks:d,imageData:e,quality:f}=F.parse(b),g=y.generateTemplate(d,e,f);if(!C.addTemplate(c,g))return u.NextResponse.json({success:!1,error:"Falha ao cadastrar template facial",details:"Template pode ser duplicado ou inv\xe1lido"},{status:400});await B.save(c,g)||console.warn("Falha ao persistir template, mas mantido em mem\xf3ria");let h=C.getDatabaseStats();return u.NextResponse.json({success:!0,message:"Template facial cadastrado com sucesso",data:{templateId:g.id,funcionarioId:c,quality:g.metadata.quality,timestamp:g.metadata.timestamp,databaseStats:h}})}catch(a){if(console.error("Erro ao cadastrar biometria facial:",a),a instanceof w.G)return u.NextResponse.json({success:!1,error:"Dados inv\xe1lidos",details:a.errors.map(a=>`${a.path.join(".")}: ${a.message}`)},{status:400});return u.NextResponse.json({success:!1,error:"Erro interno do servidor",details:a instanceof Error?a.message:"Erro desconhecido"},{status:500})}}async function J(a){try{var b;await E();let c=await a.json(),{landmarks:d,imageData:e,quality:f,threshold:g}=G.parse(c),h=y.generateTemplate(d,e,f),i=await C.findMatch(h,{threshold:g||.75,useMultipleTemplates:!0,qualityFilter:.6});if(!i||!i.match)return u.NextResponse.json({success:!1,message:"Funcion\xe1rio n\xe3o reconhecido",data:{similarity:i?.similarity||0,confidence:i?.confidence||0,threshold:g||.75,matchTime:i?.matchTime||0}});let j={EMP001:{id:"EMP001",nome:"Jo\xe3o Silva Santos",matricula:"EMP001",cargo:"Analista de Sistemas",setor:"TI",ativo:!0},EMP002:{id:"EMP002",nome:"Maria Oliveira Costa",matricula:"EMP002",cargo:"Gerente de Projetos",setor:"Gest\xe3o",ativo:!0},EMP003:{id:"EMP003",nome:"Carlos Eduardo Lima",matricula:"EMP003",cargo:"Desenvolvedor Frontend",setor:"TI",ativo:!0}}[b=i.funcionarioId]||{id:b,nome:"Funcion\xe1rio Teste",matricula:b,cargo:"Cargo Teste",setor:"Setor Teste",ativo:!0};return u.NextResponse.json({success:!0,message:"Funcion\xe1rio reconhecido com sucesso",data:{funcionario:j,biometria:{similarity:i.similarity,confidence:i.confidence,threshold:g||.75,matchTime:i.matchTime,templateId:i.template.id}}})}catch(a){if(console.error("Erro no reconhecimento facial:",a),a instanceof w.G)return u.NextResponse.json({success:!1,error:"Dados inv\xe1lidos",details:a.errors.map(a=>`${a.path.join(".")}: ${a.message}`)},{status:400});return u.NextResponse.json({success:!1,error:"Erro interno do servidor",details:a instanceof Error?a.message:"Erro desconhecido"},{status:500})}}async function K(a){try{let{searchParams:b}=new URL(a.url),c=b.get("funcionarioId");if(!c){let a=C.getDatabaseStats();return u.NextResponse.json({success:!0,data:{databaseStats:a,message:"Estat\xedsticas do banco de biometria facial"}})}let{funcionarioId:d}=H.parse({funcionarioId:c});if(!C.hasTemplates(d))return u.NextResponse.json({success:!1,message:"Funcion\xe1rio n\xe3o possui templates faciais cadastrados",data:{funcionarioId:d,templates:[]}});let e=C.getTemplates(d),f=e.map(a=>({id:a.id,quality:a.metadata.quality,timestamp:a.metadata.timestamp,featuresCount:a.features.length,landmarksCount:a.landmarks.length}));return u.NextResponse.json({success:!0,data:{funcionarioId:d,templates:f,totalTemplates:e.length}})}catch(a){if(console.error("Erro ao listar templates:",a),a instanceof w.G)return u.NextResponse.json({success:!1,error:"Par\xe2metros inv\xe1lidos",details:a.errors.map(a=>`${a.path.join(".")}: ${a.message}`)},{status:400});return u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}async function L(a){try{let{searchParams:b}=new URL(a.url),c=b.get("funcionarioId");if(!c)return u.NextResponse.json({success:!1,error:"ID do funcion\xe1rio \xe9 obrigat\xf3rio"},{status:400});let{funcionarioId:d}=H.parse({funcionarioId:c});if(!C.hasTemplates(d))return u.NextResponse.json({success:!1,message:"Funcion\xe1rio n\xe3o possui templates para remover",data:{funcionarioId:d}});if(!C.removeTemplates(d))return u.NextResponse.json({success:!1,error:"Falha ao remover templates"},{status:500});return u.NextResponse.json({success:!0,message:"Templates faciais removidos com sucesso",data:{funcionarioId:d,removedAt:new Date().toISOString()}})}catch(a){if(console.error("Erro ao remover templates:",a),a instanceof w.G)return u.NextResponse.json({success:!1,error:"Par\xe2metros inv\xe1lidos",details:a.errors.map(a=>`${a.path.join(".")}: ${a.message}`)},{status:400});return u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}let M=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/biometria/facial/route",pathname:"/api/biometria/facial",filename:"route",bundlePath:"app/api/biometria/facial/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\biometria\\facial\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:N,workUnitAsyncStorage:O,serverHooks:P}=M;function Q(){return(0,g.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:O})}async function R(a,b,c){var d;let e="/api/biometria/facial/route";"/index"===e&&(e="/");let g=await M.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=g,D=(0,j.normalizeAppPath)(e),E=!!(y.dynamicRoutes[D]||y.routes[C]);if(E&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[D];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let F=null;!E||M.isDev||x||(F="/index"===(F=C)?"/":F);let G=!0===M.isDev||!E,H=E&&!G,I=a.method||"GET",J=(0,i.getTracer)(),K=J.getActiveScopeSpan(),L={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:G,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:H,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>M.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>M.handle(P,L).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=J.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${I} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${I} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&B&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=L.renderOpts.fetchMetrics;let i=L.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=L.renderOpts.collectedTags;if(!E)return await (0,o.I)(N,O,e,L.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==L.renderOpts.collectedRevalidate&&!(L.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&L.renderOpts.collectedRevalidate,d=void 0===L.renderOpts.collectedExpire||L.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:L.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await M.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:H,isOnDemandRevalidate:A})},z),b}},l=await M.handleResponse({req:a,nextConfig:w,cacheKey:F,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!E)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&E||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};K?await g(K):await J.withPropagatedContext(a.headers,()=>J.trace(m.BaseServerSpan.handleRequest,{spanName:`${I} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":I,"http.target":a.url}},g))}catch(b){if(K||b instanceof s.NoFallbackError||await M.onRequestError(a,b,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:H,isOnDemandRevalidate:A})}),E)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,6055,639],()=>b(b.s=3217));module.exports=c})();