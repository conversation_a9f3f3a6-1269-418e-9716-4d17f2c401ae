"use strict";exports.id=4831,exports.ids=[4831],exports.modules={41862:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},51361:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},51465:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},68263:(a,b,c)=>{c.d(b,{q:()=>m});var d=c(60687),e=c(43210),f=c(42613),g=c(41862),h=c(58869),i=c(51361);let j=(0,c(62688).A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);var k=c(5336),l=c(43649);function m({onCapture:a,onError:b,mode:m="recognize",className:n="",autoStart:o=!0}){let p=(0,e.useRef)(null),q=(0,e.useRef)(null),r=(0,e.useRef)(null),[s,t]=(0,e.useState)("idle"),[u,v]=(0,e.useState)(!1),[w,x]=(0,e.useState)(0),[y,z]=(0,e.useState)(""),[A,B]=(0,e.useState)(!1),[C,D]=(0,e.useState)(!1),E=(0,e.useRef)(null),F=(0,e.useRef)(null);(0,e.useCallback)(async()=>{try{t("initializing");try{let{FaceDetection:a}=await Promise.resolve().then(c.t.bind(c,24040,23)),{FaceMesh:b}=await Promise.resolve().then(c.t.bind(c,56268,23)),d=new a({locateFile:a=>`https://cdn.jsdelivr.net/npm/@mediapipe/face_detection/${a}`});d.setOptions({model:"short",minDetectionConfidence:.5}),d.onResults(a=>{H(a)});let e=new b({locateFile:a=>`https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/${a}`});e.setOptions({maxNumFaces:1,refineLandmarks:!0,minDetectionConfidence:.5,minTrackingConfidence:.5}),e.onResults(a=>{I(a)}),E.current=d,F.current=e,console.log("✅ MediaPipe inicializado com sucesso")}catch(a){console.warn("⚠️ MediaPipe n\xe3o dispon\xedvel, usando modo simplificado:",a),E.current=null,F.current=null,v(!0),x(.8)}t("ready")}catch(a){console.error("Erro ao inicializar sistema de captura:",a),z("Erro ao carregar sistema de captura facial"),t("error"),b?.("Erro ao inicializar sistema de captura")}},[b]);let G=(0,e.useCallback)(async()=>{try{if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)throw Error("C\xe2mera n\xe3o suportada neste navegador");let a=await navigator.permissions.query({name:"camera"});console.log("Permiss\xe3o da c\xe2mera:",a.state);let b=await navigator.mediaDevices.getUserMedia({video:{width:{ideal:640},height:{ideal:480},facingMode:"user"}});return p.current&&(p.current.srcObject=b,r.current=b),!0}catch(c){console.error("Erro ao acessar c\xe2mera:",c);let a="Erro ao acessar c\xe2mera";return"NotAllowedError"===c.name?a="Permiss\xe3o de c\xe2mera negada. Clique no \xedcone da c\xe2mera na barra de endere\xe7os e permita o acesso.":"NotFoundError"===c.name?a="Nenhuma c\xe2mera encontrada no dispositivo.":"NotSupportedError"===c.name?a="C\xe2mera n\xe3o suportada neste navegador.":"NotReadableError"===c.name?a="C\xe2mera est\xe1 sendo usada por outro aplicativo.":c.message&&(a=c.message),z(a),t("error"),b?.(a),!1}},[b]),H=(0,e.useCallback)(a=>{if(!q.current||!p.current)return;let b=q.current,c=b.getContext("2d");if(c)if(c.clearRect(0,0,b.width,b.height),c.drawImage(p.current,0,0,b.width,b.height),a.detections&&a.detections.length>0){let d=a.detections[0];v(!0);let e=J(d);x(e),K(c,d,b.width,b.height),e>.8&&"capturing"===s&&!C&&(D(!0),setTimeout(()=>{"capturing"!==s||C||L(d)},500))}else v(!1),x(0)},[s]),I=(0,e.useCallback)(a=>{a.multiFaceLandmarks&&a.multiFaceLandmarks.length>0&&a.multiFaceLandmarks[0]},[]),J=a=>{let b=a.boundingBox,c=a.score,d=b.width*b.height;return(d<.1||d>.6)&&(c*=.7),Math.sqrt(Math.pow(b.xCenter-.5,2)+Math.pow(b.yCenter-.5,2))>.2&&(c*=.8),Math.min(c,1)},K=(a,b,c,d)=>{let e=b.boundingBox,f=(e.xCenter-e.width/2)*c,g=(e.yCenter-e.height/2)*d,h=e.width*c,i=e.height*d,j=w>.8?"#00ff00":w>.5?"#ffff00":"#ff0000";a.strokeStyle=j,a.lineWidth=3,a.strokeRect(f,g,h,i),a.fillStyle=j,a.font="16px Arial",a.fillText(`${Math.round(100*w)}%`,f,g-10)},L=async c=>{try{t("processing");let b=await M(c);if(b)t("success"),a?.(b);else throw Error("Falha ao extrair caracter\xedsticas faciais")}catch(a){console.error("Erro ao processar captura:",a),t("error"),z("Erro ao processar face capturada"),b?.("Erro ao processar captura facial")}finally{D(!1)}},M=async a=>{if(!q.current||!p.current)return null;let b=q.current;if(!b.getContext("2d"))return null;let c=document.createElement("canvas");c.width=b.width,c.height=b.height;let d=c.getContext("2d");if(!d)return null;d.drawImage(p.current,0,0,b.width,b.height);let e=a.boundingBox,f=Math.max(0,(e.xCenter-e.width/2)*b.width),g=Math.max(0,(e.yCenter-e.height/2)*b.height),h=Math.min(b.width-f,e.width*b.width),i=Math.min(b.height-g,e.height*b.height),j=d.getImageData(f,g,h,i),k=document.createElement("canvas");k.width=h,k.height=i;let l=k.getContext("2d");return l?(l.putImageData(j,0,0),k.toDataURL("image/jpeg",.8)):null},N=(0,e.useCallback)(async()=>{"ready"===s&&(t("capturing"),z(""),E.current||setTimeout(()=>{O()},1e3))},[s]),O=(0,e.useCallback)(async()=>{if(p.current&&q.current)try{t("processing");let b=q.current,c=b.getContext("2d");if(!c)throw Error("Erro ao acessar canvas");c.drawImage(p.current,0,0,b.width,b.height);let d=b.toDataURL("image/jpeg",.8);t("success"),a?.(d)}catch(a){console.error("Erro na captura simples:",a),t("error"),z("Erro ao capturar imagem"),b?.("Erro ao capturar imagem")}},[a,b]),P=(0,e.useCallback)(()=>{t("ready"),z(""),D(!1)},[]);return(0,e.useCallback)(async()=>{if(p.current&&E.current&&A)try{await E.current.send({image:p.current}),F.current&&await F.current.send({image:p.current})}catch(a){console.error("Erro ao processar frame:",a)}},[A]),(0,d.jsxs)("div",{className:`space-y-4 ${n}`,children:[(0,d.jsxs)("div",{className:"relative bg-gray-900 rounded-lg overflow-hidden",children:[(0,d.jsx)("video",{ref:p,autoPlay:!0,playsInline:!0,muted:!0,className:"w-full h-64 object-cover",onLoadedData:()=>{B(!0)}}),(0,d.jsx)("canvas",{ref:q,width:640,height:480,className:"absolute inset-0 w-full h-full object-cover"}),(0,d.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:["initializing"===s&&(0,d.jsxs)("div",{className:"bg-black bg-opacity-50 text-white p-4 rounded-lg flex items-center space-x-2",children:[(0,d.jsx)(g.A,{className:"h-5 w-5 animate-spin"}),(0,d.jsx)("span",{children:"Carregando..."})]}),"capturing"===s&&(0,d.jsx)("div",{className:"bg-red-500 bg-opacity-20 border-2 border-red-500 rounded-lg p-2",children:(0,d.jsx)("div",{className:"w-4 h-4 bg-red-500 rounded-full animate-pulse"})})]}),u&&(0,d.jsx)("div",{className:"absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(h.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{className:"text-sm",children:[Math.round(100*w),"%"]})]})})]}),(0,d.jsx)("div",{className:`text-center ${(()=>{switch(s){case"success":return"text-green-600";case"error":return"text-red-600";case"ready":return w>.8?"text-green-600":"text-yellow-600";default:return"text-blue-600"}})()}`,children:(0,d.jsx)("p",{className:"font-medium",children:(()=>{switch(s){case"initializing":return"Inicializando sistema de reconhecimento...";case"ready":return u?`Face detectada - Qualidade: ${Math.round(100*w)}%`:"Posicione seu rosto na c\xe2mera";case"capturing":return"Capturando face...";case"processing":return"Processando caracter\xedsticas faciais...";case"success":return"Face capturada com sucesso!";case"error":return y||"Erro no sistema";default:return"Clique para iniciar"}})()})}),(0,d.jsxs)("div",{className:"flex justify-center space-x-3",children:["ready"===s&&!o&&!r.current&&(0,d.jsxs)(f.$n,{onClick:G,variant:"primary",className:"flex items-center space-x-2",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Iniciar C\xe2mera"})]}),"ready"===s&&r.current&&(0,d.jsxs)(f.$n,{onClick:N,disabled:!u||w<.5,variant:"primary",className:"flex items-center space-x-2",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"register"===m?"Cadastrar Face":"Reconhecer"})]}),"capturing"===s&&(0,d.jsxs)(f.$n,{onClick:P,variant:"secondary",className:"flex items-center space-x-2",children:[(0,d.jsx)(j,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Cancelar"})]}),"success"===s&&(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-green-600",children:[(0,d.jsx)(k.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"Sucesso!"})]}),"error"===s&&(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsxs)(f.$n,{onClick:async()=>{t("ready"),z(""),await G()},variant:"primary",className:"flex items-center space-x-2",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Tentar Novamente"})]}),(0,d.jsxs)(f.$n,{onClick:()=>{t("ready"),z("")},variant:"secondary",className:"flex items-center space-x-2",children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Tentar Novamente"})]})]})]}),(0,d.jsxs)("div",{className:"text-sm text-gray-600 text-center space-y-1",children:[(0,d.jsx)("p",{children:"• Mantenha o rosto bem iluminado"}),(0,d.jsx)("p",{children:"• Olhe diretamente para a c\xe2mera"}),(0,d.jsx)("p",{children:"• Mantenha-se a uma dist\xe2ncia confort\xe1vel"}),"register"===m&&(0,d.jsx)("p",{className:"text-blue-600 font-medium",children:"• Modo cadastro: sua face ser\xe1 salva no sistema"})]})]})}}};