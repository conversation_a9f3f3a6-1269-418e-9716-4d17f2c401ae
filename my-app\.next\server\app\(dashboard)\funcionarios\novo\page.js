(()=>{var a={};a.id=1332,a.ids=[1332],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7854:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l,metadata:()=>k});var d=c(37413),e=c(12279),f=c(51465);let g=(0,c(26373).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var h=c(75243),i=c(4536),j=c.n(i);let k={title:"Novo Funcion\xe1rio - RLPONTO",description:"Cadastro de novo funcion\xe1rio no sistema"};function l(){return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(j(),{href:"/funcionarios",children:(0,d.jsxs)(h.$n,{variant:"outline",size:"sm",children:[(0,d.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Voltar"]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-600 rounded-lg",children:(0,d.jsx)(g,{className:"h-8 w-8 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Novo Funcion\xe1rio"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Cadastre um novo funcion\xe1rio no sistema"})]})]})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsx)(e.FuncionarioWizard,{})})]})})})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12279:(a,b,c)=>{"use strict";c.d(b,{FuncionarioWizard:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call FuncionarioWizard() from the server but FuncionarioWizard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\novo\\funcionario-wizard.tsx","FuncionarioWizard")},13336:(a,b,c)=>{"use strict";c.d(b,{FuncionarioWizard:()=>T});var d=c(60687),e=c(43210),f=c(16189);class g{recordMetric(a,b,c){let d={name:a,value:b,timestamp:Date.now(),metadata:c};this.metrics.push(d),this.metrics.length>this.maxMetrics&&(this.metrics=this.metrics.slice(-this.maxMetrics))}async measureAsync(a,b,c){let d=performance.now();try{let e=await b(),f=performance.now()-d;return this.recordMetric(a,f,{...c,success:!0}),e}catch(e){let b=performance.now()-d;throw this.recordMetric(a,b,{...c,success:!1,error:e.message}),e}}measure(a,b,c){let d=performance.now();try{let e=b(),f=performance.now()-d;return this.recordMetric(a,f,{...c,success:!0}),e}catch(e){let b=performance.now()-d;throw this.recordMetric(a,b,{...c,success:!1,error:e.message}),e}}getMetrics(a){let b=[...this.metrics];return a?.name&&(b=b.filter(b=>b.name.includes(a.name))),a?.since&&(b=b.filter(b=>b.timestamp>=a.since)),a?.limit&&(b=b.slice(-a.limit)),b.sort((a,b)=>b.timestamp-a.timestamp)}getStats(a){let b=this.metrics;if(a&&(b=b.filter(b=>b.name===a)),0===b.length)return{count:0,average:0,min:0,max:0,p95:0,p99:0};let c=b.map(a=>a.value).sort((a,b)=>a-b),d=c.length,e=c.reduce((a,b)=>a+b,0),f=c[0],g=c[d-1],h=Math.floor(.95*d),i=Math.floor(.99*d),j=c[h]||g,k=c[i]||g;return{count:d,average:e/d,min:f,max:g,p95:j,p99:k}}collectWebVitals(){}monitorMemory(){}cleanup(a=864e5){let b=Date.now()-a,c=this.metrics.length;this.metrics=this.metrics.filter(a=>a.timestamp>b);let d=c-this.metrics.length;d>0&&console.log(`🧹 Performance: Removidas ${d} m\xe9tricas antigas`)}constructor(){this.metrics=[],this.maxMetrics=1e3}}let h=new g;var i=function(a){return a[a.DEBUG=0]="DEBUG",a[a.INFO=1]="INFO",a[a.WARN=2]="WARN",a[a.ERROR=3]="ERROR",a[a.FATAL=4]="FATAL",a}({});class j{constructor(){this.isDevelopment=!1,this.isProduction=!0,this.minLevel=2*!!this.isProduction}shouldLog(a){return a>=this.minLevel}formatLogEntry(a){let{timestamp:b,levelName:c,message:d,context:e,error:f,performance:g}=a;if(!this.isDevelopment)return JSON.stringify(a);{let a=`[${b}] ${c}: ${d}`;return e&&Object.keys(e).length>0&&(a+=`
  Context: ${JSON.stringify(e,null,2)}`),f&&(a+=`
  Error: ${f.name}: ${f.message}`,f.stack&&(a+=`
  Stack: ${f.stack}`)),g&&(a+=`
  Performance: ${g.duration}ms`,g.memory&&(a+=` (${g.memory}MB)`)),a}}createLogEntry(a,b,c,d,e){return{timestamp:new Date().toISOString(),level:a,levelName:i[a],message:b,context:c,error:d?{name:d.name,message:d.message,stack:this.isDevelopment?d.stack:void 0}:void 0,performance:e}}writeLog(a){if(!this.shouldLog(a.level))return;let b=this.formatLogEntry(a);if(this.isDevelopment)switch(a.level){case 0:console.debug(b);break;case 1:console.info(b);break;case 2:console.warn(b);break;case 3:case 4:console.error(b)}else console.log(b)}debug(a,b){let c=this.createLogEntry(0,a,b);this.writeLog(c)}info(a,b){let c=this.createLogEntry(1,a,b);this.writeLog(c)}warn(a,b){let c=this.createLogEntry(2,a,b);this.writeLog(c)}error(a,b,c){let d=this.createLogEntry(3,a,c,b);this.writeLog(d)}fatal(a,b,c){let d=this.createLogEntry(4,a,c,b);this.writeLog(d)}performance(a,b,c){let d=this.createLogEntry(1,a,c,void 0,{duration:b,memory:this.getMemoryUsage()});this.writeLog(d)}getMemoryUsage(){return"undefined"!=typeof process&&process.memoryUsage?Math.round(process.memoryUsage().heapUsed/1024/1024):0}timer(a,b){let c=Date.now();return{end:()=>{let d=Date.now()-c;return this.performance(`Timer: ${a}`,d,b),d}}}apiRequest(a,b,c){this.info(`API Request: ${a} ${b}`,{...c,component:"api",action:"request",method:a,url:b})}apiResponse(a,b,c,d,e){let f=this.createLogEntry(c>=400?2:1,`API Response: ${a} ${b} - ${c}`,{...e,component:"api",action:"response",method:a,url:b,status:c},void 0,{duration:d});this.writeLog(f)}validation(a,b,c,d){this.warn(`Validation Error: ${a} - ${b}`,{...d,component:"validation",field:a,error:b,value:this.isProduction?"[REDACTED]":c})}auth(a,b,c=!0,d){let e=this.createLogEntry(c?1:2,`Auth: ${a} - ${c?"Success":"Failed"}`,{...d,component:"auth",action:a,userId:b,success:c});this.writeLog(e)}}let k=new j;function l(a){return a.replace(/\D/g,"")}function m(a){let b=l(a);return 11===b.length&&/^\d{11}$/.test(b)}function n(a){let b=l(a);return/^(\d)\1{10}$/.test(b)}function o(a,b){let c=0,d=b+1;for(let e=0;e<b;e++)c+=parseInt(a[e])*(d-e);let e=c%11;return e<2?0:11-e}let p={allowTestCPFs:!1,testCPFs:[]};function q(a,b="Telefone"){if(!a?.trim())return{isValid:!0};let c=a.replace(/\D/g,"");return c.length<10||c.length>11?{isValid:!1,error:`${b} deve ter 10 ou 11 d\xedgitos`}:{isValid:!0}}function r(a){let[b,c]=a.split(":").map(Number);return 60*b+c}var s=c(62688);let t=(0,s.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);function u({steps:a,currentStepIndex:b,completedSteps:c}){let e=new Set(c.map(a=>a.id));return(0,d.jsxs)("div",{className:"w-full",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Progresso do Cadastro"}),(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:[b+1," de ",a.length]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${(b+1)/a.length*100}%`}})})]}),(0,d.jsx)("div",{className:"flex items-center justify-between",children:a.map((c,f)=>{let g=e.has(c.id)||f<b,h=f===b;return(0,d.jsxs)("div",{className:"flex flex-col items-center flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center w-full",children:[f>0&&(0,d.jsx)("div",{className:`flex-1 h-1 ${g?"bg-blue-600":"bg-gray-200"}`}),(0,d.jsx)("div",{className:`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200
                    ${g?"bg-blue-600 border-blue-600 text-white":h?"bg-white border-blue-600 text-blue-600":"bg-white border-gray-300 text-gray-500"}
                  `,children:g?(0,d.jsx)(t,{className:"h-5 w-5"}):(0,d.jsx)("span",{className:"text-sm font-medium",children:f+1})}),f<a.length-1&&(0,d.jsx)("div",{className:`flex-1 h-1 ${f<b?"bg-blue-600":"bg-gray-200"}`})]}),(0,d.jsxs)("div",{className:"mt-3 text-center max-w-[120px]",children:[(0,d.jsx)("div",{className:`text-sm font-medium ${h?"text-blue-600":g?"text-gray-900":"text-gray-500"}`,children:c.title}),(0,d.jsx)("div",{className:`text-xs mt-1 ${h?"text-blue-500":g?"text-gray-600":"text-gray-500"}`,children:c.description})]})]},c.id)})})]})}var v=c(42613),w=c(58869),x=c(22502);function y({data:a,onDataChange:b,onValidationChange:c}){let{toUpperCaseText:f,toLowerCaseEmail:g,formatCPF:h,formatPhone:i,formatCEP:j}=(0,x.w3)(),[k,l]=(0,e.useState)({nomeCompleto:a.nomeCompleto||"",cpf:a.cpf||"",rg:a.rg||"",email:a.email||"",telefone:a.telefone||"",celular:a.celular||"",cep:a.cep||"",logradouro:a.logradouro||"",numero:a.numero||"",complemento:a.complemento||"",bairro:a.bairro||"",cidade:a.cidade||"",uf:a.uf||""}),[m,n]=(0,e.useState)({}),[o,p]=(0,e.useState)(!1),q=(a,c)=>{let d=c;switch(a){case"email":d=g(c);break;case"cpf":case"telefone":case"celular":case"cep":d=c.replace(/\D/g,"");break;default:d=f(c)}let e={...k,[a]:d};l(e),b(e),"cep"===a&&8===d.length&&r(d)},r=async a=>{if(8===a.length)try{p(!0);let c=await fetch(`https://viacep.com.br/ws/${a}/json/`),d=await c.json();if(d.erro)n(a=>({...a,cep:"CEP n\xe3o encontrado"}));else{let c={...k,cep:a,logradouro:d.logradouro||"",bairro:d.bairro||"",cidade:d.localidade||"",uf:d.uf||""};l(c),b(c),m.cep&&n(a=>({...a,cep:""}))}}catch(a){console.error("Erro ao buscar CEP:",a),n(a=>({...a,cep:"Erro ao buscar CEP"}))}finally{p(!1)}};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,d.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,d.jsx)(w.A,{className:"h-8 w-8 text-blue-600"})})}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Dados Pessoais"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Informe os dados pessoais do funcion\xe1rio"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nome Completo *"}),(0,d.jsx)(v.pd,{placeholder:"Digite o nome completo",value:k.nomeCompleto,onChange:a=>q("nomeCompleto",a.target.value),error:m.nomeCompleto})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CPF *"}),(0,d.jsx)(v.pd,{placeholder:"000.000.000-00",value:h(k.cpf),onChange:a=>q("cpf",a.target.value.replace(/\D/g,"")),error:m.cpf})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"RG"}),(0,d.jsx)(v.pd,{placeholder:"Digite o RG",value:k.rg,onChange:a=>q("rg",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,d.jsx)(v.pd,{type:"email",placeholder:"<EMAIL>",value:k.email,onChange:a=>q("email",a.target.value),error:m.email})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Telefone"}),(0,d.jsx)(v.pd,{placeholder:"(11) 1234-5678",value:i(k.telefone),onChange:a=>q("telefone",a.target.value.replace(/\D/g,""))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Celular"}),(0,d.jsx)(v.pd,{placeholder:"(11) 99999-9999",value:i(k.celular),onChange:a=>q("celular",a.target.value.replace(/\D/g,""))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CEP"}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(v.pd,{placeholder:"00000-000",value:j(k.cep),onChange:a=>q("cep",a.target.value.replace(/\D/g,""))}),(0,d.jsx)(v.$n,{type:"button",variant:"outline",onClick:()=>r(k.cep),disabled:8!==k.cep.replace(/\D/g,"").length||o,children:o?"Buscando...":"Buscar"})]})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Logradouro"}),(0,d.jsx)(v.pd,{placeholder:"Rua, Avenida, etc.",value:k.logradouro,onChange:a=>q("logradouro",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"N\xfamero"}),(0,d.jsx)(v.pd,{placeholder:"123",value:k.numero,onChange:a=>q("numero",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Complemento"}),(0,d.jsx)(v.pd,{placeholder:"Apto, Bloco, etc.",value:k.complemento,onChange:a=>q("complemento",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Bairro"}),(0,d.jsx)(v.pd,{placeholder:"Nome do bairro",value:k.bairro,onChange:a=>q("bairro",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cidade"}),(0,d.jsx)(v.pd,{placeholder:"Nome da cidade",value:k.cidade,onChange:a=>q("cidade",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"UF"}),(0,d.jsxs)("select",{value:k.uf,onChange:a=>q("uf",a.target.value),"data-format":"preserve",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 font-semibold",children:[(0,d.jsx)("option",{value:"",children:"Selecione o Estado"}),(0,d.jsx)("option",{value:"AC",children:"Acre"}),(0,d.jsx)("option",{value:"AL",children:"Alagoas"}),(0,d.jsx)("option",{value:"AP",children:"Amap\xe1"}),(0,d.jsx)("option",{value:"AM",children:"Amazonas"}),(0,d.jsx)("option",{value:"BA",children:"Bahia"}),(0,d.jsx)("option",{value:"CE",children:"Cear\xe1"}),(0,d.jsx)("option",{value:"DF",children:"Distrito Federal"}),(0,d.jsx)("option",{value:"ES",children:"Esp\xedrito Santo"}),(0,d.jsx)("option",{value:"GO",children:"Goi\xe1s"}),(0,d.jsx)("option",{value:"MA",children:"Maranh\xe3o"}),(0,d.jsx)("option",{value:"MT",children:"Mato Grosso"}),(0,d.jsx)("option",{value:"MS",children:"Mato Grosso do Sul"}),(0,d.jsx)("option",{value:"MG",children:"Minas Gerais"}),(0,d.jsx)("option",{value:"PA",children:"Par\xe1"}),(0,d.jsx)("option",{value:"PB",children:"Para\xedba"}),(0,d.jsx)("option",{value:"PR",children:"Paran\xe1"}),(0,d.jsx)("option",{value:"PE",children:"Pernambuco"}),(0,d.jsx)("option",{value:"PI",children:"Piau\xed"}),(0,d.jsx)("option",{value:"RJ",children:"Rio de Janeiro"}),(0,d.jsx)("option",{value:"RN",children:"Rio Grande do Norte"}),(0,d.jsx)("option",{value:"RS",children:"Rio Grande do Sul"}),(0,d.jsx)("option",{value:"RO",children:"Rond\xf4nia"}),(0,d.jsx)("option",{value:"RR",children:"Roraima"}),(0,d.jsx)("option",{value:"SC",children:"Santa Catarina"}),(0,d.jsx)("option",{value:"SP",children:"S\xe3o Paulo"}),(0,d.jsx)("option",{value:"SE",children:"Sergipe"}),(0,d.jsx)("option",{value:"TO",children:"Tocantins"})]})]})]})]})}var z=c(57800);let A=(0,s.A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var B=c(78122);function C({data:a,onDataChange:b,onValidationChange:c}){let{matricula:f,isLoading:g,error:h,isGenerated:i,gerarMatricula:j}=function(a){let[b,c]=(0,e.useState)(a||""),[d,f]=(0,e.useState)(!1),[g,h]=(0,e.useState)(null),[i,j]=(0,e.useState)(!1),k=(0,e.useCallback)(async()=>{try{f(!0),h(null);let a=await fetch("/api/matricula",{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok){let b=await a.json();throw Error(b.error||"Erro ao gerar matr\xedcula")}let b=await a.json();if(b.success)c(b.matricula),j(!0),console.log("Matr\xedcula gerada (n\xe3o reservada):",b.matricula);else throw Error(b.error||"Erro ao gerar matr\xedcula")}catch(a){h(a instanceof Error?a.message:"Erro desconhecido"),console.error("Erro ao gerar matr\xedcula:",a)}finally{f(!1)}},[]);return{matricula:b,isLoading:d,error:g,isGenerated:i,gerarMatricula:k,verificarDisponibilidade:(0,e.useCallback)(async a=>{try{let b=await fetch("/api/matricula",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({matricula:a})});if(!b.ok)throw Error("Erro ao verificar disponibilidade");return(await b.json()).disponivel}catch(a){return console.error("Erro ao verificar disponibilidade:",a),!1}},[]),resetar:(0,e.useCallback)(()=>{c(""),f(!1),h(null),j(!1)},[])}}(a.matricula),[k,l]=(0,e.useState)({matricula:a.matricula||"",cargo:a.cargo||"",setor:a.setor||"",dataAdmissao:a.dataAdmissao||"",salario:a.salario||"",cargaHoraria:a.cargaHoraria||"40",horarioEntrada:a.horarioEntrada||"",horarioSaida:a.horarioSaida||"",intervaloInicio:a.intervaloInicio||"",intervaloFim:a.intervaloFim||"",observacoes:a.observacoes||""}),[m,n]=(0,e.useState)({}),o=(a,c)=>{let d={...k,[a]:c};l(d),b(d)};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,d.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,d.jsx)(z.A,{className:"h-8 w-8 text-green-600"})})}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Dados Profissionais"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Informe os dados profissionais do funcion\xe1rio"}),(0,d.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4",children:(0,d.jsx)("div",{className:"flex items-center text-yellow-800",children:(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsx)("strong",{children:"\uD83D\uDCDD Nota:"})," Os dados ser\xe3o salvos apenas na etapa final de confirma\xe7\xe3o."]})})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Matr\xedcula *"}),i&&(0,d.jsx)("span",{className:"text-xs text-green-600 bg-green-100 px-2 py-1 rounded",children:"Gerada automaticamente"})]})}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(v.pd,{placeholder:g?"Gerando matr\xedcula...":"Matr\xedcula ser\xe1 gerada automaticamente",value:k.matricula||"Aguardando gera\xe7\xe3o...",readOnly:!0,disabled:g,className:`${i?"bg-green-50 border-green-200":"bg-gray-50"} cursor-not-allowed`}),g&&(0,d.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,d.jsx)(B.A,{className:"h-4 w-4 text-blue-500 animate-spin"})}),!g&&!k.matricula&&(0,d.jsx)("button",{type:"button",onClick:j,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700",title:"Gerar nova matr\xedcula",children:(0,d.jsx)(B.A,{className:"h-4 w-4"})})]}),i&&(0,d.jsxs)("p",{className:"text-xs text-green-600 mt-1",children:["✓ Matr\xedcula gerada automaticamente no formato sequencial (0001, 0002, etc.)",(0,d.jsx)("br",{}),"✓ Sistema garante que n\xe3o haver\xe1 duplicatas ou reutiliza\xe7\xe3o"]}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"\uD83D\uDD12 A matr\xedcula \xe9 gerada automaticamente e n\xe3o pode ser editada. \xc9 \xfanica e nunca ser\xe1 reutilizada."})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Data de Admiss\xe3o *"}),(0,d.jsx)("input",{type:"date",value:k.dataAdmissao,onChange:a=>o("dataAdmissao",a.target.value),className:`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${m.dataAdmissao?"border-red-500":"border-gray-300"}`}),m.dataAdmissao&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:m.dataAdmissao})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cargo *"}),(0,d.jsx)(v.pd,{placeholder:"Digite o cargo",value:k.cargo,onChange:a=>o("cargo",a.target.value),error:m.cargo})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Setor *"}),(0,d.jsxs)("select",{value:k.setor,onChange:a=>o("setor",a.target.value),"data-format":"preserve",className:`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${m.setor?"border-red-500":"border-gray-300"}`,children:[(0,d.jsx)("option",{value:"",children:"Selecione o setor"}),(0,d.jsx)("option",{value:"ADMINISTRA\xc7\xc3O",children:"ADMINISTRA\xc7\xc3O"}),(0,d.jsx)("option",{value:"PRODU\xc7\xc3O",children:"PRODU\xc7\xc3O"}),(0,d.jsx)("option",{value:"VENDAS",children:"VENDAS"}),(0,d.jsx)("option",{value:"RECURSOS HUMANOS",children:"RECURSOS HUMANOS"}),(0,d.jsx)("option",{value:"TECNOLOGIA",children:"TECNOLOGIA"}),(0,d.jsx)("option",{value:"FINANCEIRO",children:"FINANCEIRO"}),(0,d.jsx)("option",{value:"MARKETING",children:"MARKETING"}),(0,d.jsx)("option",{value:"OPERA\xc7\xd5ES",children:"OPERA\xc7\xd5ES"})]}),m.setor&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:m.setor})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sal\xe1rio"}),(0,d.jsx)(v.pd,{placeholder:"R$ 0,00",value:k.salario.toString(),onChange:a=>o("salario",a.target.value)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Carga Hor\xe1ria Semanal *"}),(0,d.jsx)("input",{type:"number",placeholder:"40",value:k.cargaHoraria.toString(),onChange:a=>o("cargaHoraria",parseInt(a.target.value)||0),className:`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${m.cargaHoraria?"border-red-500":"border-gray-300"}`,min:"1",max:"60"}),m.cargaHoraria&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:m.cargaHoraria})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Hor\xe1rio de Entrada *"}),(0,d.jsx)("input",{type:"time",value:k.horarioEntrada,onChange:a=>o("horarioEntrada",a.target.value),className:`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${m.horarioEntrada?"border-red-500":"border-gray-300"}`}),m.horarioEntrada&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:m.horarioEntrada})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Hor\xe1rio de Sa\xedda *"}),(0,d.jsx)("input",{type:"time",value:k.horarioSaida,onChange:a=>o("horarioSaida",a.target.value),className:`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${m.horarioSaida?"border-red-500":"border-gray-300"}`}),m.horarioSaida&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:m.horarioSaida})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"In\xedcio do Intervalo"}),(0,d.jsx)("input",{type:"time",value:k.intervaloInicio,onChange:a=>o("intervaloInicio",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Fim do Intervalo"}),(0,d.jsx)("input",{type:"time",value:k.intervaloFim,onChange:a=>o("intervaloFim",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Observa\xe7\xf5es"}),(0,d.jsx)("textarea",{placeholder:"Observa\xe7\xf5es adicionais sobre o funcion\xe1rio...",value:k.observacoes,onChange:a=>o("observacoes",a.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),k.horarioEntrada&&k.horarioSaida&&(0,d.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Resumo dos Hor\xe1rios"}),(0,d.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Expediente:"})," ",k.horarioEntrada," \xe0s ",k.horarioSaida]}),k.intervaloInicio&&k.intervaloFim&&(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Intervalo:"})," ",k.intervaloInicio," \xe0s ",k.intervaloFim]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Carga Hor\xe1ria:"})," ",k.cargaHoraria,"h semanais"]})]})]})]})}var D=c(68263),E=c(51361),F=c(96882),G=c(5336),H=c(35071);let I=(0,s.A)("skip-forward",[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]]);var J=c(99891),K=c(43649);function L({funcionarioId:a,onValidationChange:b,onBiometriaChange:f}){let[g,h]=(0,e.useState)("none"),[i,j]=(0,e.useState)(""),[k]=(0,e.useState)(!0),l=async b=>{if(!a){j("ID do funcion\xe1rio n\xe3o dispon\xedvel. Complete o cadastro primeiro."),h("error");return}console.log("\uD83D\uDD25 Iniciando cadastro de biometria para funcion\xe1rio:",a);try{h("capturing"),j("Processando biometria facial...");let c=await n(b);if(!c)throw Error("Falha ao extrair caracter\xedsticas faciais");let d=await fetch("/api/biometria/facial",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({funcionarioId:a,landmarks:c,imageData:b,quality:.9})}),e=await d.json();if(e.success)h("success"),j("Biometria facial cadastrada com sucesso!");else throw Error(e.error||"Erro ao cadastrar biometria facial")}catch(a){console.error("Erro no cadastro facial:",a),h("error"),j(a instanceof Error?a.message:"Erro no cadastro facial")}},m=()=>{h("skipped"),j("Biometria facial n\xe3o cadastrada. Voc\xea pode cadastrar depois na p\xe1gina do funcion\xe1rio.")},n=async a=>{try{let b=new Image,d=document.createElement("canvas"),e=d.getContext("2d");return new Promise((f,g)=>{b.onload=async()=>{try{d.width=b.width,d.height=b.height,e?.drawImage(b,0,0);let{FaceMesh:a}=await Promise.resolve().then(c.t.bind(c,56268,23)),h=new a({locateFile:a=>`https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/${a}`});h.onResults(a=>{if(a.multiFaceLandmarks&&a.multiFaceLandmarks[0]){let b=a.multiFaceLandmarks[0].map(a=>({x:a.x,y:a.y,z:a.z||0}));f(b)}else g(Error("Nenhuma face detectada na imagem"))}),await h.send({image:d})}catch(a){console.error("Erro ao processar landmarks:",a),g(Error("Erro ao processar landmarks faciais"))}},b.onerror=()=>{g(Error("Erro ao carregar imagem"))},b.src=a})}catch(a){throw console.error("Erro na extra\xe7\xe3o de landmarks:",a),Error("Falha na extra\xe7\xe3o de landmarks faciais")}};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,d.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,d.jsx)(E.A,{className:"h-8 w-8 text-green-600"})})}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Cadastro de Biometria Facial"}),(0,d.jsx)("p",{className:"text-gray-600 mt-2",children:"Configure o reconhecimento facial para facilitar o registro de ponto"})]}),(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(F.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsx)("h3",{className:"font-medium text-blue-900 mb-1",children:k?"Cadastro Opcional":"Cadastro Obrigat\xf3rio"}),(0,d.jsx)("div",{className:"text-blue-700 space-y-1",children:k?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("p",{children:"• O cadastro de biometria facial \xe9 opcional durante o registro"}),(0,d.jsx)("p",{children:"• Pode ser feito agora ou posteriormente na p\xe1gina do funcion\xe1rio"}),(0,d.jsx)("p",{children:"• Facilita o registro de ponto sem necessidade de cart\xe3o ou senha"})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("p",{children:"• O cadastro de biometria facial \xe9 obrigat\xf3rio"}),(0,d.jsx)("p",{children:"• Necess\xe1rio para utilizar o sistema de controle de ponto"}),(0,d.jsx)("p",{children:"• Garante seguran\xe7a e precis\xe3o nos registros"})]})})]})]})}),i&&(0,d.jsx)("div",{className:`p-4 rounded-lg border ${"success"===g?"bg-green-50 border-green-200 text-green-800":"error"===g?"bg-red-50 border-red-200 text-red-800":"skipped"===g?"bg-yellow-50 border-yellow-200 text-yellow-800":"bg-blue-50 border-blue-200 text-blue-800"}`,children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:["success"===g&&(0,d.jsx)(G.A,{className:"h-5 w-5"}),"error"===g&&(0,d.jsx)(H.A,{className:"h-5 w-5"}),"skipped"===g&&(0,d.jsx)(I,{className:"h-5 w-5"}),"capturing"===g&&(0,d.jsx)(E.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:i})]})}),"none"===g&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Capturar Biometria Facial"}),(0,d.jsx)(D.q,{mode:"register",onCapture:l,onError:a=>{h("error"),j(a)}})]}),k&&(0,d.jsx)("div",{className:"flex justify-center",children:(0,d.jsxs)(v.$n,{onClick:m,variant:"outline",className:"flex items-center space-x-2",children:[(0,d.jsx)(I,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Pular Esta Etapa"})]})})]}),"success"===g&&(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(G.A,{className:"h-16 w-16 text-green-500 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Biometria Cadastrada!"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"O funcion\xe1rio j\xe1 pode utilizar o reconhecimento facial para registrar ponto."}),(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-green-700",children:[(0,d.jsx)(J.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"Dados protegidos com criptografia"})]})})]}),"error"===g&&(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(H.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Erro no Cadastro"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"N\xe3o foi poss\xedvel cadastrar a biometria facial. Tente novamente."}),(0,d.jsxs)("div",{className:"flex justify-center space-x-3",children:[(0,d.jsxs)(v.$n,{onClick:()=>{h("none"),j("")},variant:"primary",className:"flex items-center space-x-2",children:[(0,d.jsx)(E.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Tentar Novamente"})]}),k&&(0,d.jsxs)(v.$n,{onClick:m,variant:"outline",className:"flex items-center space-x-2",children:[(0,d.jsx)(I,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Pular Esta Etapa"})]})]})]}),"skipped"===g&&(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(K.A,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Etapa Pulada"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"A biometria facial n\xe3o foi cadastrada. Voc\xea pode fazer isso depois."}),(0,d.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,d.jsx)("p",{className:"font-medium mb-1",children:"Como cadastrar depois:"}),(0,d.jsx)("p",{children:"Funcion\xe1rios → [Nome] → Biometria → Cadastrar Biometria Facial"})]})})]}),(0,d.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(J.A,{className:"h-5 w-5 text-gray-600 mt-0.5"}),(0,d.jsxs)("div",{className:"text-sm text-gray-700",children:[(0,d.jsx)("h4",{className:"font-medium mb-1",children:"Seguran\xe7a e Privacidade"}),(0,d.jsxs)("ul",{className:"space-y-1",children:[(0,d.jsx)("li",{children:"• Apenas caracter\xedsticas matem\xe1ticas s\xe3o armazenadas"}),(0,d.jsx)("li",{children:"• Dados criptografados com padr\xe3o militar (AES-256)"}),(0,d.jsx)("li",{children:"• Conformidade total com LGPD"}),(0,d.jsx)("li",{children:"• Possibilidade de remo\xe7\xe3o a qualquer momento"})]})]})]})})]})}var M=c(97992),N=c(48730);function O({data:a,onValidationChange:b}){var c,e;let f=a=>{if(!a)return"";let b=a.replace(/\D/g,"");return b.length<=10?b.replace(/(\d{2})(\d{4})(\d{4})/,"($1) $2-$3"):b.replace(/(\d{2})(\d{5})(\d{4})/,"($1) $2-$3")};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,d.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,d.jsx)(G.A,{className:"h-8 w-8 text-green-600"})})}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Confirma\xe7\xe3o dos Dados"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Revise todas as informa\xe7\xf5es antes de finalizar o cadastro"}),(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(G.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,d.jsxs)("div",{className:"text-blue-800",children:[(0,d.jsx)("p",{className:"font-medium",children:"⚠️ Importante:"}),(0,d.jsxs)("p",{className:"text-sm mt-1",children:["Os dados do funcion\xe1rio ser\xe3o salvos permanentemente apenas quando voc\xea clicar em",(0,d.jsx)("strong",{children:' "Salvar e Finalizar Cadastro"'})," abaixo."]})]})]})})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)(w.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Dados Pessoais"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Nome Completo"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.nomeCompleto||"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"CPF"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.cpf?a.cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/,"$1.$2.$3-$4"):"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"RG"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.rg||"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.email||"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Telefone"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.telefone?f(a.telefone):"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Celular"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.celular?f(a.celular):"-"})]})]}),(a.logradouro||a.cidade)&&(0,d.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center mb-2",children:[(0,d.jsx)(M.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Endere\xe7o"})]}),(0,d.jsx)("p",{className:"text-gray-900",children:[a.logradouro,a.numero,a.complemento,a.bairro,a.cidade,a.uf].filter(Boolean).join(", ")||"-"}),a.cep&&(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["CEP: ",a.cep]})]})]}),(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)(z.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Dados Profissionais"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Matr\xedcula"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.matricula||"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Data de Admiss\xe3o"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.dataAdmissao?(c=a.dataAdmissao)?new Date(c).toLocaleDateString("pt-BR"):"":"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Cargo"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.cargo||"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Setor"}),(0,d.jsx)("p",{className:"text-gray-900 capitalize",children:a.setor||"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Sal\xe1rio"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.salario?(e=a.salario)?("string"==typeof e?parseFloat(e):e).toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"":"-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Carga Hor\xe1ria"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.cargaHoraria?`${a.cargaHoraria}h semanais`:"-"})]})]}),(a.horarioEntrada||a.horarioSaida)&&(0,d.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center mb-2",children:[(0,d.jsx)(N.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Hor\xe1rios de Trabalho"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Expediente"}),(0,d.jsx)("p",{className:"text-gray-900",children:a.horarioEntrada&&a.horarioSaida?`${a.horarioEntrada} \xe0s ${a.horarioSaida}`:"-"})]}),a.intervaloInicio&&a.intervaloFim&&(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Intervalo"}),(0,d.jsx)("p",{className:"text-gray-900",children:`${a.intervaloInicio} \xe0s ${a.intervaloFim}`})]})]})]}),a.observacoes&&(0,d.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Observa\xe7\xf5es"}),(0,d.jsx)("p",{className:"text-gray-900 mt-1",children:a.observacoes})]})]})]}),(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(G.A,{className:"h-5 w-5 text-green-400"})}),(0,d.jsxs)("div",{className:"ml-3",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"✅ Funcion\xe1rio cadastrado com sucesso!"}),(0,d.jsx)("div",{className:"mt-2 text-sm text-green-700",children:(0,d.jsx)("p",{children:'O funcion\xe1rio foi salvo no sistema e j\xe1 pode come\xe7ar a utilizar o controle de ponto. Clique em "Finalizar Cadastro" para retornar \xe0 lista de funcion\xe1rios.'})})]})]})})]})}var P=c(47033),Q=c(14952),R=c(41862);let S=[{id:"pessoal",title:"Dados Pessoais",description:"Informa\xe7\xf5es b\xe1sicas do funcion\xe1rio"},{id:"profissional",title:"Dados Profissionais",description:"Cargo, setor e hor\xe1rios"},{id:"biometria",title:"Biometria Facial",description:"Cadastrar reconhecimento facial (opcional)"},{id:"confirmacao",title:"Confirma\xe7\xe3o",description:"Revisar e confirmar dados"}];function T(){let a=(0,f.useRouter)(),{measureAsync:b}={recordMetric:h.recordMetric.bind(h),measureAsync:h.measureAsync.bind(h),measure:h.measure.bind(h),getMetrics:h.getMetrics.bind(h),getStats:h.getStats.bind(h)},[c,g]=(0,e.useState)(!1),[i,j]=(0,e.useState)(null),[s,w]=(0,e.useState)({}),x=function({steps:a,initialStep:b=0}){let[c,d]=(0,e.useState)(b),[f,g]=(0,e.useState)({}),[h,i]=(0,e.useState)(new Set),j=a[c],k=0===c,l=c===a.length-1,m=(0,e.useCallback)(()=>{l||d(a=>a+1)},[l]),n=(0,e.useCallback)(()=>{k||d(a=>a-1)},[k]),o=(0,e.useCallback)(b=>{b>=0&&b<a.length&&d(b)},[a.length]),p=(0,e.useCallback)((a,b)=>{g(c=>({...c,[a]:b}))},[]),q=(0,e.useCallback)(a=>{i(b=>new Set([...b,a]))},[]),r=(0,e.useCallback)(a=>f[a]??!1,[f]),s=(0,e.useCallback)(a=>h.has(a),[h]),t=r(j?.id),u=!k,v=(c+1)/a.length*100;return{currentStep:j,currentStepIndex:c,steps:a,isFirstStep:k,isLastStep:l,canGoNext:t,canGoPrevious:u,progress:v,goToNext:m,goToPrevious:n,goToStep:o,setStepValid:p,markStepCompleted:q,isStepValid:r,isStepCompleted:s}}({steps:S,initialStep:0}),z=a=>{w(b=>({...b,...a}))},A=()=>{let a=function(a){let b=function(a){let b=function(a){var b,c,d;let e=[],f=[],g=(b=a.nomeCompleto||"",b?.trim()?b.trim().length<2?{isValid:!1,error:"Nome deve ter pelo menos 2 caracteres"}:b.trim().length>150?{isValid:!1,error:"Nome n\xe3o pode ter mais de 150 caracteres"}:b.trim().split(" ").filter(a=>a.length>0).length<2?{isValid:!1,warning:"Recomendado informar nome e sobrenome completos"}:{isValid:!0}:{isValid:!1,error:"Nome completo \xe9 obrigat\xf3rio"});g.isValid?g.warning&&f.push(g.warning):e.push(g.error);let h=function(a){if(!a?.trim())return{isValid:!1,error:"CPF \xe9 obrigat\xf3rio"};let b=function(a){let b=l(a),c=function(a){let b=a.replace(/\D/g,"");return b.length<=3?b:b.length<=6?`${b.slice(0,3)}.${b.slice(3)}`:b.length<=9?`${b.slice(0,3)}.${b.slice(3,6)}.${b.slice(6)}`:`${b.slice(0,3)}.${b.slice(3,6)}.${b.slice(6,9)}-${b.slice(9,11)}`}(a),d=[],e=!0;m(b)||(d.push("CPF deve ter 11 d\xedgitos num\xe9ricos"),e=!1),n(b)&&(d.push("CPF n\xe3o pode ser uma sequ\xeancia de n\xfameros iguais"),e=!1),e&&!function(a){let b=l(a);if(!m(b)||n(b))return!1;let c=o(b,9);if(parseInt(b[9])!==c)return!1;let d=o(b,10);return parseInt(b[10])===d}(a)&&(d.push("CPF inv\xe1lido - d\xedgitos verificadores incorretos"),e=!1);let f=p.testCPFs.includes(b);return{isValid:e||p.allowTestCPFs&&f,formatted:c,unformatted:b,errors:d,isTestCPF:f}}(a);return b.isValid?b.isTestCPF?{isValid:!0,warning:"CPF de teste - apenas para desenvolvimento"}:{isValid:!0}:{isValid:!1,error:b.errors[0]||"CPF inv\xe1lido"}}(a.cpf||"");if(h.isValid?h.warning&&f.push(h.warning):e.push(h.error),a.email){let b=(c=a.email,c?.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(c)?{isValid:!0}:{isValid:!1,error:"Email inv\xe1lido"}:{isValid:!0});b.isValid||e.push(b.error)}if(a.telefone){let b=q(a.telefone,"Telefone");b.isValid||e.push(b.error)}if(a.celular){let b=q(a.celular,"Celular");b.isValid||e.push(b.error)}if(a.endereco){let b=(d=a.endereco.cep||"",d?.trim()?8!==d.replace(/\D/g,"").length?{isValid:!1,error:"CEP deve ter 8 d\xedgitos"}:{isValid:!0}:{isValid:!1,error:"CEP \xe9 obrigat\xf3rio"});b.isValid||e.push(b.error),a.endereco.logradouro?.trim()||e.push("Logradouro \xe9 obrigat\xf3rio"),a.endereco.cidade?.trim()||e.push("Cidade \xe9 obrigat\xf3ria"),a.endereco.uf?.trim()||e.push("UF \xe9 obrigat\xf3ria")}return{isValid:0===e.length,errors:e,warnings:f}}(a),c=function(a){var b,c,d;let e=[],f=[];if(!a.dadosProfissionais)return e.push("Dados profissionais s\xe3o obrigat\xf3rios"),{isValid:!1,errors:e,warnings:f};let g=a.dadosProfissionais,h=(b=g.matricula||"",b?.trim()?b.length<3||b.length>10?{isValid:!1,error:"Matr\xedcula deve ter entre 3 e 10 caracteres"}:{isValid:!0}:{isValid:!1,error:"Matr\xedcula \xe9 obrigat\xf3ria"});h.isValid||e.push(h.error);let i=(c=g.cargo||"",c?.trim()?c.trim().length<2?{isValid:!1,error:"Cargo deve ter pelo menos 2 caracteres"}:{isValid:!0}:{isValid:!1,error:"Cargo \xe9 obrigat\xf3rio"});i.isValid||e.push(i.error);let j=(d=g.setor||"",d?.trim()?{isValid:!0}:{isValid:!1,error:"Setor \xe9 obrigat\xf3rio"});j.isValid||e.push(j.error);let k=function(a){if(!a?.trim())return{isValid:!1,error:"Data de admiss\xe3o \xe9 obrigat\xf3ria"};let b=new Date(a);if(isNaN(b.getTime()))return{isValid:!1,error:"Data de admiss\xe3o inv\xe1lida"};let c=new Date,d=new Date;return(d.setFullYear(c.getFullYear()-1),b>c)?{isValid:!1,error:"Data de admiss\xe3o n\xe3o pode ser futura"}:b<d?{isValid:!0,warning:"Data de admiss\xe3o \xe9 anterior a 1 ano"}:{isValid:!0}}(g.dataAdmissao||"");if(k.isValid?k.warning&&f.push(k.warning):e.push(k.error),g.horarioTrabalho){let a=function(a,b){if(!a?.trim()||!b?.trim())return{isValid:!1,error:"Hor\xe1rios de entrada e sa\xedda s\xe3o obrigat\xf3rios"};let c=r(a),d=r(b);if(c>=d)return{isValid:!1,error:"Hor\xe1rio de sa\xedda deve ser posterior ao de entrada"};let e=(d-c)/60;return e>12?{isValid:!1,error:"Jornada n\xe3o pode exceder 12 horas"}:e<4?{isValid:!0,warning:"Jornada inferior a 4 horas"}:{isValid:!0}}(g.horarioTrabalho.entrada||"",g.horarioTrabalho.saida||"");a.isValid?a.warning&&f.push(a.warning):e.push(a.error)}return{isValid:0===e.length,errors:e,warnings:f}}(a);return{isValid:b.isValid&&c.isValid,errors:[...b.errors,...c.errors],warnings:[...b.warnings,...c.warnings]}}(a),c=[];return a.telefone?.trim()||a.celular?.trim()||a.email?.trim()||c.push("Pelo menos um meio de contato \xe9 obrigat\xf3rio (telefone, celular ou email)"),a.endereco&&a.endereco.logradouro&&a.endereco.cidade&&a.endereco.uf||c.push("Endere\xe7o completo \xe9 obrigat\xf3rio para submiss\xe3o"),{isValid:b.isValid&&0===c.length,errors:[...b.errors,...c],warnings:b.warnings}}(s);return{isValid:a.isValid,errors:a.errors}},B=async()=>{if(k.debug("Iniciando submiss\xe3o de funcion\xe1rio",{component:"FuncionarioWizard",action:"handleSubmit",isSubmitting:c,hasData:!!s}),c)return void k.warn("Tentativa de submiss\xe3o m\xfaltipla ignorada",{component:"FuncionarioWizard",action:"handleSubmit"});let b=A();if(!b.isValid){j(`Dados incompletos:
${b.errors.join("\n")}`),k.error("Valida\xe7\xe3o falhou antes do salvamento: "+b.errors.join(", "));return}try{g(!0),j(null),k.debug("Iniciando processo de submiss\xe3o",{component:"FuncionarioWizard",action:"submit"});let b=k.timer("Valida\xe7\xe3o de dados"),c=A().errors;if(b.end(),c.length>0)throw k.validation("funcionario","Dados inv\xe1lidos para submiss\xe3o",c,{component:"FuncionarioWizard"}),Error(`Dados inv\xe1lidos:

• ${c.join("\n• ")}`);let d=k.timer("Transforma\xe7\xe3o de dados"),e=(a=>{let b={nomeCompleto:a.nomeCompleto?.trim(),cpf:a.cpf?.replace(/\D/g,""),rg:a.rg?.trim(),email:a.email?.trim(),telefone:a.telefone?.replace(/\D/g,""),celular:a.celular?.replace(/\D/g,""),endereco:{cep:a.cep?.replace(/\D/g,""),logradouro:a.logradouro?.trim(),numero:a.numero?.trim(),complemento:a.complemento?.trim(),bairro:a.bairro?.trim(),cidade:a.cidade?.trim(),uf:a.uf?.trim()},dadosProfissionais:{matricula:a.matricula?.trim(),cargo:a.cargo?.trim(),setor:a.setor?.trim(),dataAdmissao:a.dataAdmissao,salario:"string"==typeof a.salario?parseFloat(a.salario)||0:a.salario||0,cargaHoraria:"string"==typeof a.cargaHoraria?parseInt(a.cargaHoraria)||40:a.cargaHoraria||40,horarioTrabalho:{entrada:a.horarioEntrada?.trim(),saida:a.horarioSaida?.trim(),intervaloInicio:a.intervaloInicio?.trim(),intervaloFim:a.intervaloFim?.trim()}},observacoes:a.observacoes?.trim()};return Object.values(b.endereco).some(a=>a&&a.length>0)||delete b.endereco,b})(s);d.end(),k.apiRequest("POST","/api/funcionarios",{component:"FuncionarioWizard"});let f=await fetch("/api/funcionarios",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(k.apiResponse("POST","/api/funcionarios",f.status,Date.now()-performance.now(),{component:"FuncionarioWizard"}),!f.ok){let a=await f.json();k.error("Erro na API de funcion\xe1rios",void 0,{component:"FuncionarioWizard",status:f.status,errorData:a});let b=a.error||"Erro ao cadastrar funcion\xe1rio";if(a.details&&Array.isArray(a.details)){let c=a.details.map(a=>"object"==typeof a&&a.message&&a.path?`${a.path.join(".")}: ${a.message}`:a.toString()).join("; ");b+=`

Detalhes: ${c}`}throw Error(b)}let h=await f.json();k.info("Funcion\xe1rio cadastrado com sucesso",{component:"FuncionarioWizard",action:"submit",funcionarioId:h.funcionario?.id,matricula:h.funcionario?.dadosProfissionais?.matricula}),k.info("Funcion\xe1rio salvo com sucesso, redirecionando",{component:"FuncionarioWizard",action:"redirectAfterSave",funcionarioId:h.funcionario?.id}),setTimeout(()=>{try{a.replace("/funcionarios?success=funcionario-cadastrado")}catch(a){k.error("Erro no router.replace, usando window.location",{error:a instanceof Error?a.message:String(a),component:"FuncionarioWizard"}),window.location.href="/funcionarios?success=funcionario-cadastrado"}},150)}catch(a){console.error("\uD83D\uDD25 ERRO CAPTURADO:",a),j(a instanceof Error?a.message:"Erro desconhecido")}finally{k.debug("Finalizando processo de submiss\xe3o",{component:"FuncionarioWizard",action:"submit"}),g(!1)}};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(u,{steps:x.steps,currentStepIndex:x.currentStepIndex,completedSteps:x.steps.slice(0,x.currentStepIndex)}),i&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"text-red-800",children:[(0,d.jsx)("strong",{children:"Erro ao cadastrar funcion\xe1rio:"}),(0,d.jsx)("div",{className:"mt-2 whitespace-pre-line",children:i})]})}),(0,d.jsx)("div",{className:"min-h-[400px]",children:(()=>{switch(x.currentStep.id){case"pessoal":return(0,d.jsx)(y,{data:s,onDataChange:z,onValidationChange:a=>x.setStepValid("pessoal",a)});case"profissional":return(0,d.jsx)(C,{data:s,onDataChange:z,onValidationChange:a=>x.setStepValid("profissional",a)});case"biometria":return(0,d.jsx)(L,{funcionarioId:s.matricula||"temp-id",onValidationChange:a=>x.setStepValid("biometria",a),onBiometriaChange:a=>{k.info("Status da biometria alterado",{component:"FuncionarioWizard",action:"biometriaChange",hasBiometria:a}),w(b=>({...b,biometriaCadastrada:a}))}});case"confirmacao":return k.debug("Renderizando step de confirma\xe7\xe3o",{component:"FuncionarioWizard",step:"confirmacao",hasData:!!s}),(0,d.jsx)(O,{data:s,onValidationChange:a=>{k.debug("Valida\xe7\xe3o de confirma\xe7\xe3o alterada",{component:"FuncionarioWizard",step:"confirmacao",isValid:a}),x.setStepValid("confirmacao",a)}});default:return null}})()}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[(0,d.jsx)("div",{children:!x.isFirstStep&&(0,d.jsxs)(v.$n,{variant:"outline",onClick:x.goToPrevious,disabled:c,children:[(0,d.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Anterior"]})}),(0,d.jsx)("div",{className:"flex space-x-3",children:0===x.currentStepIndex?(0,d.jsxs)(v.$n,{onClick:x.goToNext,disabled:!x.canGoNext||c,variant:"primary",children:["Pr\xf3ximo",(0,d.jsx)(Q.A,{className:"h-4 w-4 ml-2"})]}):1===x.currentStepIndex?(0,d.jsxs)(v.$n,{onClick:()=>{k.debug("Avan\xe7ando para biometria",{component:"FuncionarioWizard",action:"nextToBiometria",canGoNext:x.canGoNext}),x.goToNext()},disabled:!x.canGoNext||c,variant:"primary",children:["Pr\xf3ximo",(0,d.jsx)(Q.A,{className:"h-4 w-4 ml-2"})]}):2===x.currentStepIndex?(0,d.jsxs)(v.$n,{onClick:x.goToNext,disabled:!x.canGoNext||c,variant:"primary",children:["Pr\xf3ximo",(0,d.jsx)(Q.A,{className:"h-4 w-4 ml-2"})]}):3===x.currentStepIndex?(0,d.jsx)(v.$n,{onClick:()=>{k.info("Iniciando salvamento final do funcion\xe1rio",{component:"FuncionarioWizard",action:"salvarFinal"}),B()},variant:"primary",disabled:c||!x.canGoNext,children:c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(R.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Salvando..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(t,{className:"h-4 w-4 mr-2"}),"Salvar e Finalizar Cadastro"]})}):null})]})]})}},14952:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24040:a=>{"use strict";a.exports=require("@mediapipe/face_detection")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},38620:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["funcionarios",{children:["novo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,7854)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\funcionarios\\novo\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,57675)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,85389)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\funcionarios\\novo\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/funcionarios/novo/page",pathname:"/funcionarios/novo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/funcionarios/novo/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47033:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},56268:a=>{"use strict";a.exports=require("@mediapipe/face_mesh")},57800:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82851:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,12279)),Promise.resolve().then(c.bind(c,58570))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96923:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,13336)),Promise.resolve().then(c.bind(c,98316))},97992:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99891:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,3327,4556,1684,2121,4831],()=>b(b.s=38620));module.exports=c})();