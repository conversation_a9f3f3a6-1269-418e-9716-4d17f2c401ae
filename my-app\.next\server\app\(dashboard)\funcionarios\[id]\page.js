(()=>{var a={};a.id=9151,a.ids=[9151],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11995:(a,b,c)=>{"use strict";c.d(b,{FuncionarioDetails:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call FuncionarioDetails() from the server but FuncionarioDetails is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\funcionarios\\funcionario-details.tsx","FuncionarioDetails")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},40918:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},51361:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},51465:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},53148:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},57800:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65516:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},67593:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,76831)),Promise.resolve().then(c.bind(c,98316))},76831:(a,b,c)=>{"use strict";c.d(b,{FuncionarioDetails:()=>p});var d=c(60687),e=c(43210),f=c(35071),g=c(58869),h=c(5336);let i=(0,c(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var j=c(97992),k=c(57800),l=c(99891),m=c(51361),n=c(93613),o=c(94200);function p({funcionarioId:a}){let[b,c]=(0,e.useState)(null),[p,r]=(0,e.useState)(!0),[s,t]=(0,e.useState)(null),u=async()=>{try{r(!0);let b=await fetch(`/api/funcionarios/${a}`);if(!b.ok){if(404===b.status)throw Error("Funcion\xe1rio n\xe3o encontrado");throw Error("Erro ao carregar dados do funcion\xe1rio")}let d=await b.json();c(d)}catch(a){t(a instanceof Error?a.message:"Erro desconhecido")}finally{r(!1)}};return p?(0,d.jsx)(q,{}):s?(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-8 text-center",children:[(0,d.jsx)("div",{className:"text-red-500 mb-4",children:(0,d.jsx)(f.A,{className:"h-12 w-12 mx-auto"})}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Erro ao carregar funcion\xe1rio"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:s}),(0,d.jsx)("button",{onClick:u,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Tentar novamente"})]}):b?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,d.jsx)("div",{className:"w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center",children:(0,d.jsx)(g.A,{className:"h-12 w-12 text-blue-600"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:b.nomeCompleto}),(0,d.jsxs)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${"ativo"===b.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:["ativo"===b.status?(0,d.jsx)(h.A,{className:"h-4 w-4 mr-1"}):(0,d.jsx)(f.A,{className:"h-4 w-4 mr-1"}),b.status]})]}),(0,d.jsx)("p",{className:"text-lg text-gray-600 mb-1",children:b.dadosProfissionais.cargo}),(0,d.jsx)("p",{className:"text-gray-500",children:b.dadosProfissionais.setor}),(0,d.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:["Matr\xedcula: ",b.dadosProfissionais.matricula]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Cadastrado em"}),(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:new Date(b.createdAt).toLocaleDateString("pt-BR")})]})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,d.jsx)(g.A,{className:"h-5 w-5 text-blue-600"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Dados Pessoais"})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"CPF:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosPessoais.cpf})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"RG:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosPessoais.rg})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Data de Nascimento:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:new Date(b.dadosPessoais.dataNascimento).toLocaleDateString("pt-BR")})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Estado Civil:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosPessoais.estadoCivil})]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,d.jsx)(i,{className:"h-5 w-5 text-blue-600"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Contato"})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Email:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosPessoais.email})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Telefone:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosPessoais.telefone})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Celular:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosPessoais.celular||"N\xe3o informado"})]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,d.jsx)(j.A,{className:"h-5 w-5 text-blue-600"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Endere\xe7o"})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"CEP:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosPessoais.endereco.cep})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Logradouro:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosPessoais.endereco.logradouro})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"N\xfamero:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosPessoais.endereco.numero})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Cidade:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosPessoais.endereco.cidade})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Estado:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosPessoais.endereco.estado})]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,d.jsx)(k.A,{className:"h-5 w-5 text-blue-600"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Dados Profissionais"})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Data de Admiss\xe3o:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:new Date(b.dadosProfissionais.dataAdmissao).toLocaleDateString("pt-BR")})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Tipo de Contrato:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosProfissionais.tipoContrato})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Sal\xe1rio:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:b.dadosProfissionais.salario.toLocaleString("pt-BR",{style:"currency",currency:"BRL"})})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Jornada:"}),(0,d.jsxs)("span",{className:"font-medium text-gray-900",children:[b.dadosProfissionais.jornadaTrabalho,"h"]})]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(l.A,{className:"h-5 w-5 text-green-600"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Biometria e Seguran\xe7a"})]}),(0,d.jsxs)("a",{href:`/funcionarios/${b.id}/biometria`,className:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Gerenciar Biometria"]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 text-gray-500"}),(0,d.jsx)("span",{className:"text-gray-700",children:"Reconhecimento Facial:"})]}),(0,d.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,d.jsx)(n.A,{className:"h-3 w-3 mr-1"}),"N\xe3o Cadastrado"]})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 text-gray-500"}),(0,d.jsx)("span",{className:"text-gray-700",children:"Biometria Digital:"})]}),(0,d.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:"N\xe3o Dispon\xedvel"})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"\xdaltimo Acesso:"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:"Nunca"})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-700",children:"Status de Seguran\xe7a:"}),(0,d.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,d.jsx)(n.A,{className:"h-3 w-3 mr-1"}),"Configura\xe7\xe3o Pendente"]})]})]}),(0,d.jsx)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 text-blue-600 mt-0.5"}),(0,d.jsxs)("div",{className:"text-sm text-blue-700",children:[(0,d.jsx)("p",{className:"font-medium mb-1",children:"Configure a biometria para maior seguran\xe7a"}),(0,d.jsx)("p",{children:"O reconhecimento facial permite registro de ponto r\xe1pido e seguro, sem necessidade de cart\xf5es ou senhas."})]})]})})]})]})]}):(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-8 text-center",children:[(0,d.jsx)("div",{className:"text-gray-400 mb-4",children:(0,d.jsx)(g.A,{className:"h-12 w-12 mx-auto"})}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Funcion\xe1rio n\xe3o encontrado"}),(0,d.jsx)("p",{className:"text-gray-600",children:"O funcion\xe1rio solicitado n\xe3o existe ou foi removido."})]})}function q(){return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,d.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-full animate-pulse"}),(0,d.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,d.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse w-1/3"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/4"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/5"})]}),(0,d.jsx)("div",{className:"w-20 h-8 bg-gray-200 rounded animate-pulse"})]})}),(0,d.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("div",{className:"h-5 bg-gray-200 rounded animate-pulse w-1/3 mb-4"}),(0,d.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/3"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/2"})]},b))})]},b))})]})}},77321:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,11995)),Promise.resolve().then(c.bind(c,58570))},78074:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["funcionarios",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,93531)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\funcionarios\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,57675)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,85389)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\funcionarios\\[id]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/funcionarios/[id]/page",pathname:"/funcionarios/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/funcionarios/[id]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88971:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},93531:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>s,generateMetadata:()=>r});var d=c(37413),e=c(61120),f=c(4536),g=c.n(f),h=c(75243),i=c(11995),j=c(51465),k=c(88971),l=c(53148),m=c(40918),n=c(65516),o=c(26373);let p=(0,o.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),q=(0,o.A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);async function r({params:a}){let{id:b}=await a;return{title:`Funcion\xe1rio - RLPONTO`,description:"Detalhes do funcion\xe1rio"}}async function s({params:a}){let{id:b}=await a;return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(g(),{href:"/funcionarios",children:(0,d.jsxs)(h.$n,{variant:"outline",size:"sm",children:[(0,d.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Voltar"]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-600 rounded-lg",children:(0,d.jsx)(k.A,{className:"h-8 w-8 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Detalhes do Funcion\xe1rio"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Visualize e gerencie informa\xe7\xf5es do funcion\xe1rio"})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsxs)(h.$n,{variant:"outline",size:"sm",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Hist\xf3rico de Ponto"]}),(0,d.jsxs)(h.$n,{variant:"outline",size:"sm",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Relat\xf3rios"]}),(0,d.jsxs)(h.$n,{variant:"outline",size:"sm",children:[(0,d.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Exportar"]}),(0,d.jsx)(g(),{href:`/funcionarios/${b}/editar`,children:(0,d.jsxs)(h.$n,{variant:"primary",size:"sm",children:[(0,d.jsx)(p,{className:"h-4 w-4 mr-2"}),"Editar"]})}),(0,d.jsx)(h.$n,{variant:"outline",size:"sm",children:(0,d.jsx)(q,{className:"h-4 w-4"})})]})]}),(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(t,{}),children:(0,d.jsx)(i.FuncionarioDetails,{funcionarioId:b})})]})})})}function t(){return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,d.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-full animate-pulse"}),(0,d.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,d.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse w-1/3"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/4"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/5"})]}),(0,d.jsx)("div",{className:"w-20 h-8 bg-gray-200 rounded animate-pulse"})]})}),(0,d.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("div",{className:"h-5 bg-gray-200 rounded animate-pulse w-1/3 mb-4"}),(0,d.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/3"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-1/2"})]},b))})]},b))})]})}},94200:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("fingerprint",[["path",{d:"M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4",key:"1nerag"}],["path",{d:"M14 13.12c0 2.38 0 6.38-1 8.88",key:"o46ks0"}],["path",{d:"M17.29 21.02c.12-.6.43-2.3.5-3.02",key:"ptglia"}],["path",{d:"M2 12a10 10 0 0 1 18-6",key:"ydlgp0"}],["path",{d:"M2 16h.01",key:"1gqxmh"}],["path",{d:"M21.8 16c.2-2 .131-5.354 0-6",key:"drycrb"}],["path",{d:"M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2",key:"1tidbn"}],["path",{d:"M8.65 22c.21-.66.45-1.32.57-2",key:"13wd9y"}],["path",{d:"M9 6.8a6 6 0 0 1 9 5.2v2",key:"1fr1j5"}]])},97992:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99891:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,3327,4556,1684,2121],()=>b(b.s=78074));module.exports=c})();