import { NextRequest, NextResponse } from 'next/server';
import { Funcionario } from '@/types';
import { funcionarioStorageDB } from '@/lib/funcionario-storage-db';
import { logger } from '@/lib/logger';

// Usar storage com banco de dados
const funcionarioStorage = funcionarioStorageDB;

interface RouteParams {
  params: Promise<{ id: string }>;
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;

    // Buscar funcionário por ID
    const funcionario = await funcionarioStorage.getById(id);

    if (!funcionario) {
      return NextResponse.json(
        { error: 'Funcionário não encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json(funcionario);
  } catch (error) {
    console.error('Erro ao buscar funcionário:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;
    const body = await request.json();

    logger.info('Atualizando funcionário', { id, data: body });

    // Atualizar funcionário
    const success = await funcionarioStorage.update(id, body);

    if (!success) {
      return NextResponse.json(
        { error: 'Funcionário não encontrado' },
        { status: 404 }
      );
    }

    // Buscar funcionário atualizado
    const funcionarioAtualizado = await funcionarioStorage.getById(id);

    logger.info('Funcionário atualizado com sucesso', { id });
    return NextResponse.json(funcionarioAtualizado);
  } catch (error) {
    logger.error('Erro ao atualizar funcionário', { error: error instanceof Error ? error.message : String(error), id });
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;

    logger.info('Removendo funcionário', { id });

    // Remover funcionário
    const success = await funcionarioStorage.delete(id);

    if (!success) {
      return NextResponse.json(
        { error: 'Funcionário não encontrado' },
        { status: 404 }
      );
    }

    logger.info('Funcionário removido com sucesso', { id });
    return NextResponse.json({
      message: 'Funcionário removido com sucesso'
    });
  } catch (error) {
    logger.error('Erro ao remover funcionário', { error: error instanceof Error ? error.message : String(error), id });
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
