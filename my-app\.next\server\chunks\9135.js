exports.id=9135,exports.ids=[9135],exports.modules={4483:(a,b,c)=>{"use strict";c.d(b,{v:()=>r});var d=c(96330);let e=globalThis.prisma||new d.PrismaClient;var f=c(36463),g=c(55511),h=c.n(g);let i="aes-256-gcm";function j(){let a=process.env.ENCRYPTION_KEY;if(!a)throw Error("ENCRYPTION_KEY n\xe3o configurada em produ\xe7\xe3o");return h().scryptSync(a,"rlponto-salt",32)}function k(a){try{if(!a||"string"!=typeof a)throw Error("Texto para criptografia deve ser uma string n\xe3o vazia");let b=j(),c=h().randomBytes(16),d=h().createCipher(i,b);d.setAAD(Buffer.from("rlponto-aad"));let e=d.update(a,"utf8","hex");e+=d.final("hex");let g=d.getAuthTag(),k={encrypted:e,iv:c.toString("hex"),tag:g.toString("hex"),algorithm:i};return f.vF.debug("Dados criptografados com sucesso",{component:"Encryption",action:"encrypt",algorithm:i,dataLength:a.length}),k}catch(a){throw f.vF.error("Erro ao criptografar dados",{component:"Encryption",action:"encrypt"},a instanceof Error?a:Error(String(a))),Error("Falha na criptografia dos dados")}}function l(a){try{if(!a||"object"!=typeof a)throw Error("Dados criptografados inv\xe1lidos");let{encrypted:b,iv:c,tag:d,algorithm:e}=a;if(!b||!c||!d)throw Error("Dados criptografados incompletos");if(e!==i)throw Error(`Algoritmo n\xe3o suportado: ${e}`);let g=j(),k=h().createDecipher(e,g);k.setAAD(Buffer.from("rlponto-aad")),k.setAuthTag(Buffer.from(d,"hex"));let l=k.update(b,"hex","utf8");return l+=k.final("utf8"),f.vF.debug("Dados descriptografados com sucesso",{component:"Encryption",action:"decrypt",algorithm:e,dataLength:l.length}),l}catch(a){throw f.vF.error("Erro ao descriptografar dados",{component:"Encryption",action:"decrypt"},a instanceof Error?a:Error(String(a))),Error("Falha na descriptografia dos dados")}}function m(a){if(!a)return"";try{let b=k(a);return JSON.stringify(b)}catch(a){throw f.vF.error("Erro ao criptografar CPF",{component:"Encryption",action:"encryptCPF"},a instanceof Error?a:Error(String(a))),Error("Falha ao criptografar CPF")}}function n(a){if(!a)return"";try{let b=JSON.parse(a);return l(b)}catch(b){return f.vF.warn("CPF n\xe3o est\xe1 criptografado - migra\xe7\xe3o necess\xe1ria",{component:"Encryption",action:"decryptCPF"}),a}}function o(a){if(!a)return"";try{let b=k(a);return JSON.stringify(b)}catch(a){throw f.vF.error("Erro ao criptografar RG",{component:"Encryption",action:"encryptRG"},a instanceof Error?a:Error(String(a))),Error("Falha ao criptografar RG")}}let p={algorithm:i,keyLength:32,ivLength:16,tagLength:16,isProduction:!0,validateProductionConfig(){if(!this.isProduction)return!0;let a=!!process.env.ENCRYPTION_KEY,b=!!process.env.HASH_SALT;return!!a&&!!b||(f.vF.error("Configura\xe7\xe3o de criptografia inadequada para produ\xe7\xe3o",{component:"Encryption",action:"validateConfig",hasKey:a,hasSalt:b}),!1)}};if(p.isProduction&&!p.validateProductionConfig())throw Error("Configura\xe7\xe3o de criptografia inadequada para produ\xe7\xe3o");class q{static getInstance(){return q.instance||(q.instance=new q),q.instance}async save(a){try{f.vF.info("Salvando funcion\xe1rio no banco de dados",{component:"FuncionarioStorageDB",action:"save",funcionarioId:a.id});let b=m(a.cpf),c=a.rg?o(a.rg):null;return await e.funcionario.create({data:{id:a.id,nomeCompleto:a.nomeCompleto,cpf:b,rg:c,email:a.email,telefone:a.telefone,celular:a.celular,cep:a.endereco?.cep,logradouro:a.endereco?.logradouro,numero:a.endereco?.numero,complemento:a.endereco?.complemento,bairro:a.endereco?.bairro,cidade:a.endereco?.cidade,uf:a.endereco?.uf,matricula:a.dadosProfissionais.matricula,cargo:a.dadosProfissionais.cargo,setor:a.dadosProfissionais.setor,dataAdmissao:a.dadosProfissionais.dataAdmissao,salario:a.dadosProfissionais.salario,cargaHoraria:a.dadosProfissionais.cargaHoraria,horarioEntrada:a.dadosProfissionais.horarioTrabalho.entrada,horarioSaida:a.dadosProfissionais.horarioTrabalho.saida,intervaloInicio:a.dadosProfissionais.horarioTrabalho.intervaloInicio,intervaloFim:a.dadosProfissionais.horarioTrabalho.intervaloFim,biometriaCadastrada:a.biometria?.cadastrada||!1,biometriaTemplates:a.biometria?.templates||0,biometriaDataCadastro:a.biometria?.dataUltimoCadastro,foto:a.foto,status:a.status,observacoes:a.observacoes}}),f.vF.info("Funcion\xe1rio salvo com sucesso no banco",{component:"FuncionarioStorageDB",action:"save",funcionarioId:a.id,matricula:a.dadosProfissionais.matricula}),!0}catch(b){return f.vF.error("Erro ao salvar funcion\xe1rio no banco",{component:"FuncionarioStorageDB",action:"save",error:b instanceof Error?b.message:"Erro desconhecido",funcionarioId:a.id}),!1}}async findById(a){try{let b=await e.funcionario.findUnique({where:{id:a}});if(!b)return null;return this.convertFromDB(b)}catch(b){return f.vF.error("Erro ao buscar funcion\xe1rio por ID",{component:"FuncionarioStorageDB",action:"findById",error:b instanceof Error?b.message:"Erro desconhecido",funcionarioId:a}),null}}async findByCpf(a){try{for(let b of(await e.funcionario.findMany()))try{if(n(b.cpf)===a)return this.convertFromDB(b)}catch(c){if(b.cpf===a)return this.convertFromDB(b)}return null}catch(a){return f.vF.error("Erro ao buscar funcion\xe1rio por CPF",{component:"FuncionarioStorageDB",action:"findByCpf",error:a instanceof Error?a.message:"Erro desconhecido"}),null}}async findAll(){try{return(await e.funcionario.findMany({orderBy:{criadoEm:"desc"}})).map(a=>this.convertFromDB(a))}catch(a){return f.vF.error("Erro ao buscar todos os funcion\xe1rios",{component:"FuncionarioStorageDB",action:"findAll",error:a instanceof Error?a.message:"Erro desconhecido"}),[]}}async getNextMatricula(){try{let a=await e.funcionario.findFirst({orderBy:{matricula:"desc"}});if(!a)return"0001";return(parseInt(a.matricula)+1).toString().padStart(4,"0")}catch(a){return f.vF.error("Erro ao gerar pr\xf3xima matr\xedcula",{component:"FuncionarioStorageDB",action:"getNextMatricula",error:a instanceof Error?a.message:"Erro desconhecido"}),Date.now().toString().slice(-4)}}async update(a,b){try{let c={atualizadoEm:new Date};return b.nomeCompleto&&(c.nomeCompleto=b.nomeCompleto),b.email&&(c.email=b.email),b.telefone&&(c.telefone=b.telefone),b.celular&&(c.celular=b.celular),b.status&&(c.status=b.status),b.observacoes&&(c.observacoes=b.observacoes),b.cpf&&(c.cpf=m(b.cpf)),b.rg&&(c.rg=o(b.rg)),await e.funcionario.update({where:{id:a},data:c}),f.vF.info("Funcion\xe1rio atualizado com sucesso",{component:"FuncionarioStorageDB",action:"update",funcionarioId:a}),!0}catch(b){return f.vF.error("Erro ao atualizar funcion\xe1rio",{component:"FuncionarioStorageDB",action:"update",error:b instanceof Error?b.message:"Erro desconhecido",funcionarioId:a}),!1}}convertFromDB(a){let b=n(a.cpf),c=a.rg?function(a){if(!a)return"";try{let b=JSON.parse(a);return l(b)}catch(b){return f.vF.warn("RG n\xe3o est\xe1 criptografado - migra\xe7\xe3o necess\xe1ria",{component:"Encryption",action:"decryptRG"}),a}}(a.rg):null;return{id:a.id,nomeCompleto:a.nomeCompleto,cpf:b,rg:c,email:a.email,telefone:a.telefone,celular:a.celular,endereco:{cep:a.cep,logradouro:a.logradouro,numero:a.numero,complemento:a.complemento,bairro:a.bairro,cidade:a.cidade,uf:a.uf},dadosProfissionais:{matricula:a.matricula,cargo:a.cargo,setor:a.setor,dataAdmissao:a.dataAdmissao,salario:a.salario?Number(a.salario):void 0,cargaHoraria:a.cargaHoraria,horarioTrabalho:{entrada:a.horarioEntrada,saida:a.horarioSaida,intervaloInicio:a.intervaloInicio,intervaloFim:a.intervaloFim}},foto:a.foto,biometria:{cadastrada:a.biometriaCadastrada,dataUltimoCadastro:a.biometriaDataCadastro,templates:a.biometriaTemplates},status:a.status,observacoes:a.observacoes,createdAt:a.criadoEm,updatedAt:a.atualizadoEm}}}let r=q.getInstance()},36463:(a,b,c)=>{"use strict";c.d(b,{vF:()=>f});var d=function(a){return a[a.DEBUG=0]="DEBUG",a[a.INFO=1]="INFO",a[a.WARN=2]="WARN",a[a.ERROR=3]="ERROR",a[a.FATAL=4]="FATAL",a}({});class e{constructor(){this.isDevelopment=!1,this.isProduction=!0,this.minLevel=2*!!this.isProduction}shouldLog(a){return a>=this.minLevel}formatLogEntry(a){let{timestamp:b,levelName:c,message:d,context:e,error:f,performance:g}=a;if(!this.isDevelopment)return JSON.stringify(a);{let a=`[${b}] ${c}: ${d}`;return e&&Object.keys(e).length>0&&(a+=`
  Context: ${JSON.stringify(e,null,2)}`),f&&(a+=`
  Error: ${f.name}: ${f.message}`,f.stack&&(a+=`
  Stack: ${f.stack}`)),g&&(a+=`
  Performance: ${g.duration}ms`,g.memory&&(a+=` (${g.memory}MB)`)),a}}createLogEntry(a,b,c,e,f){return{timestamp:new Date().toISOString(),level:a,levelName:d[a],message:b,context:c,error:e?{name:e.name,message:e.message,stack:this.isDevelopment?e.stack:void 0}:void 0,performance:f}}writeLog(a){if(!this.shouldLog(a.level))return;let b=this.formatLogEntry(a);if(this.isDevelopment)switch(a.level){case 0:console.debug(b);break;case 1:console.info(b);break;case 2:console.warn(b);break;case 3:case 4:console.error(b)}else console.log(b)}debug(a,b){let c=this.createLogEntry(0,a,b);this.writeLog(c)}info(a,b){let c=this.createLogEntry(1,a,b);this.writeLog(c)}warn(a,b){let c=this.createLogEntry(2,a,b);this.writeLog(c)}error(a,b,c){let d=this.createLogEntry(3,a,c,b);this.writeLog(d)}fatal(a,b,c){let d=this.createLogEntry(4,a,c,b);this.writeLog(d)}performance(a,b,c){let d=this.createLogEntry(1,a,c,void 0,{duration:b,memory:this.getMemoryUsage()});this.writeLog(d)}getMemoryUsage(){return"undefined"!=typeof process&&process.memoryUsage?Math.round(process.memoryUsage().heapUsed/1024/1024):0}timer(a,b){let c=Date.now();return{end:()=>{let d=Date.now()-c;return this.performance(`Timer: ${a}`,d,b),d}}}apiRequest(a,b,c){this.info(`API Request: ${a} ${b}`,{...c,component:"api",action:"request",method:a,url:b})}apiResponse(a,b,c,d,e){let f=this.createLogEntry(c>=400?2:1,`API Response: ${a} ${b} - ${c}`,{...e,component:"api",action:"response",method:a,url:b,status:c},void 0,{duration:d});this.writeLog(f)}validation(a,b,c,d){this.warn(`Validation Error: ${a} - ${b}`,{...d,component:"validation",field:a,error:b,value:this.isProduction?"[REDACTED]":c})}auth(a,b,c=!0,d){let e=this.createLogEntry(c?1:2,`Auth: ${a} - ${c?"Success":"Failed"}`,{...d,component:"auth",action:a,userId:b,success:c});this.writeLog(e)}}let f=new e},78335:()=>{},96487:()=>{}};