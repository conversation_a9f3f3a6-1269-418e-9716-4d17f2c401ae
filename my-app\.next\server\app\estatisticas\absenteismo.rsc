1:"$Sreact.fragment"
2:I[6062,["7177","static/chunks/app/layout-7e63b6183d7635de.js"],"SessionProvider"]
3:I[6403,["7177","static/chunks/app/layout-7e63b6183d7635de.js"],"TextFormattingProvider"]
4:I[7555,[],""]
5:I[1901,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","8039","static/chunks/app/error-da7a176665770fd1.js"],"default"]
6:I[1295,[],""]
7:I[2558,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","4345","static/chunks/app/not-found-0d232e224bc38f5f.js"],"default"]
8:I[277,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","9305","static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js"],"DashboardHeader"]
9:I[2623,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","9305","static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js"],"DashboardNav"]
a:I[6874,["6874","static/chunks/6874-d27b54d0b28e3259.js","3963","static/chunks/app/(dashboard)/estatisticas/absenteismo/page-ba16de8c46966459.js"],""]
16:I[8393,[],""]
:HL["/_next/static/css/b293d1867f231925.css","style"]
0:{"P":null,"b":"57groY_ofPMaGR4OOS9UN","p":"","c":["","estatisticas","absenteismo"],"i":false,"f":[[["",{"children":["(dashboard)",{"children":["estatisticas",{"children":["absenteismo",{"children":["__PAGE__",{}]}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/b293d1867f231925.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$5","errorStyles":[],"errorScripts":[],"template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","$L7",null,{}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]}]]}],{"children":["(dashboard)",["$","$1","c",{"children":[null,["$","div",null,{"className":"min-h-screen bg-gray-50","children":[["$","$L8",null,{}],["$","div",null,{"className":"flex","children":[["$","$L9",null,{}],["$","main",null,{"className":"flex-1 overflow-auto","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]]}]]}],{"children":["estatisticas",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["absenteismo",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-gray-50","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8","children":["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"className":"flex items-center justify-between","children":[["$","div",null,{"className":"flex items-center space-x-3","children":[["$","$La",null,{"href":"/estatisticas","children":["$","button",null,{"className":"p-2 hover:bg-gray-100 rounded-lg transition-colors","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-left h-5 w-5 text-gray-600","aria-hidden":"true","children":[["$","path","1l729n",{"d":"m12 19-7-7 7-7"}],["$","path","x3x0zl",{"d":"M19 12H5"}],"$undefined"]}]}]}],["$","div",null,{"className":"p-2 bg-red-600 rounded-lg","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-triangle-alert h-8 w-8 text-white","aria-hidden":"true","children":[["$","path","wmoenq",{"d":"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"}],["$","path","juzpu7",{"d":"M12 9v4"}],["$","path","p32p05",{"d":"M12 17h.01"}],"$undefined"]}]}],["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold text-gray-900","children":"Análise de Absenteísmo"}],["$","p",null,{"className":"text-gray-600","children":"Monitoramento de faltas e ausências"}]]}]]}],["$","div",null,{"className":"flex space-x-3","children":[["$","select",null,{"className":"px-3 py-2 border border-gray-300 rounded-md text-sm","children":[["$","option",null,{"children":"Últimos 30 dias"}],["$","option",null,{"children":"Últimos 90 dias"}],["$","option",null,{"children":"Último ano"}]]}],["$","button",null,{"className":"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-download h-4 w-4 mr-2","aria-hidden":"true","children":[["$","path","m9g1x1",{"d":"M12 15V3"}],["$","path","ih7n3h",{"d":"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}],["$","path","brsn70",{"d":"m7 10 5 5 5-5"}],"$undefined"]}],"Exportar"]}]]}]]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-4 gap-6","children":[["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":["$","div",null,{"className":"flex items-center","children":["$Lb","$Lc"]}]}],"$Ld","$Le","$Lf"]}],"$L10","$L11","$L12","$L13"]}]}]}],null,"$L14"]}],{},null,false]},null,false]},null,false]},null,false]},null,false],"$L15",false]],"m":"$undefined","G":["$16",[]],"s":false,"S":true}
1b:I[9665,[],"OutletBoundary"]
1d:I[4911,[],"AsyncMetadataOutlet"]
1f:I[9665,[],"ViewportBoundary"]
21:I[9665,[],"MetadataBoundary"]
22:"$Sreact.suspense"
b:["$","div",null,{"className":"p-2 bg-red-100 rounded-lg","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-triangle-alert h-6 w-6 text-red-600","aria-hidden":"true","children":[["$","path","wmoenq",{"d":"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"}],["$","path","juzpu7",{"d":"M12 9v4"}],["$","path","p32p05",{"d":"M12 17h.01"}],"$undefined"]}]}]
c:["$","div",null,{"className":"ml-4","children":[["$","p",null,{"className":"text-sm font-medium text-gray-500","children":"Taxa de Absenteísmo"}],["$","p",null,{"className":"text-2xl font-semibold text-gray-900","children":[3.8,"%"]}]]}]
d:["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"p-2 bg-orange-100 rounded-lg","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-user-x h-6 w-6 text-orange-600","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],["$","line","3nzzx3",{"x1":"17","x2":"22","y1":"8","y2":"13"}],["$","line","1swrse",{"x1":"22","x2":"17","y1":"8","y2":"13"}],"$undefined"]}]}],["$","div",null,{"className":"ml-4","children":[["$","p",null,{"className":"text-sm font-medium text-gray-500","children":"Total de Faltas"}],["$","p",null,{"className":"text-2xl font-semibold text-gray-900","children":47}]]}]]}]}]
e:["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"p-2 bg-yellow-100 rounded-lg","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar h-6 w-6 text-yellow-600","aria-hidden":"true","children":[["$","path","1cmpym",{"d":"M8 2v4"}],["$","path","4m81vk",{"d":"M16 2v4"}],["$","rect","1hopcy",{"width":"18","height":"18","x":"3","y":"4","rx":"2"}],["$","path","8toen8",{"d":"M3 10h18"}],"$undefined"]}]}],["$","div",null,{"className":"ml-4","children":[["$","p",null,{"className":"text-sm font-medium text-gray-500","children":"Dias Perdidos"}],["$","p",null,{"className":"text-2xl font-semibold text-gray-900","children":94}]]}]]}]}]
f:["$","div",null,{"className":"bg-white rounded-lg shadow p-6","children":["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"p-2 bg-purple-100 rounded-lg","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-trending-down h-6 w-6 text-purple-600","aria-hidden":"true","children":[["$","path","t6n2it",{"d":"M16 17h6v-6"}],["$","path","x473p",{"d":"m22 17-8.5-8.5-5 5L2 7"}],"$undefined"]}]}],["$","div",null,{"className":"ml-4","children":[["$","p",null,{"className":"text-sm font-medium text-gray-500","children":"Custo Estimado"}],["$","p",null,{"className":"text-2xl font-semibold text-gray-900","children":["R$ ","15.420"]}]]}]]}]}]
10:["$","div",null,{"className":"bg-white rounded-lg shadow","children":[["$","div",null,{"className":"px-6 py-4 border-b border-gray-200","children":["$","h2",null,{"className":"text-lg font-medium text-gray-900","children":"Evolução do Absenteísmo"}]}],["$","div",null,{"className":"p-6","children":["$","div",null,{"className":"h-64 bg-gray-100 rounded-lg flex items-center justify-center","children":["$","div",null,{"className":"text-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-trending-down h-12 w-12 text-gray-400 mx-auto mb-2","aria-hidden":"true","children":[["$","path","t6n2it",{"d":"M16 17h6v-6"}],["$","path","x473p",{"d":"m22 17-8.5-8.5-5 5L2 7"}],"$undefined"]}],["$","p",null,{"className":"text-gray-500","children":"Gráfico de evolução do absenteísmo"}],["$","p",null,{"className":"text-sm text-gray-400","children":"Implementação com biblioteca de gráficos"}]]}]}]}]]}]
11:["$","div",null,{"className":"grid grid-cols-1 lg:grid-cols-2 gap-6","children":[["$","div",null,{"className":"bg-white rounded-lg shadow","children":[["$","div",null,{"className":"px-6 py-4 border-b border-gray-200","children":["$","h2",null,{"className":"text-lg font-medium text-gray-900","children":"Tipos de Falta"}]}],["$","div",null,{"className":"p-6","children":["$","div",null,{"className":"space-y-4","children":[["$","div","0",{"className":"flex items-center justify-between","children":[["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"w-3 h-3 bg-red-500 rounded-full mr-3"}],["$","span",null,{"className":"text-sm font-medium text-gray-900","children":"Doença"}]]}],["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-sm text-gray-500","children":18}],["$","span",null,{"className":"text-sm font-medium text-gray-900","children":[38.3,"%"]}]]}]]}],["$","div","1",{"className":"flex items-center justify-between","children":[["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"w-3 h-3 bg-red-500 rounded-full mr-3"}],["$","span",null,{"className":"text-sm font-medium text-gray-900","children":"Particular"}]]}],["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-sm text-gray-500","children":12}],["$","span",null,{"className":"text-sm font-medium text-gray-900","children":[25.5,"%"]}]]}]]}],["$","div","2",{"className":"flex items-center justify-between","children":[["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"w-3 h-3 bg-red-500 rounded-full mr-3"}],["$","span",null,{"className":"text-sm font-medium text-gray-900","children":"Médico"}]]}],["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-sm text-gray-500","children":8}],["$","span",null,{"className":"text-sm font-medium text-gray-900","children":[17,"%"]}]]}]]}],["$","div","3",{"className":"flex items-center justify-between","children":[["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"w-3 h-3 bg-red-500 rounded-full mr-3"}],["$","span",null,{"className":"text-sm font-medium text-gray-900","children":"Família"}]]}],["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-sm text-gray-500","children":6}],["$","span",null,{"className":"text-sm font-medium text-gray-900","children":[12.8,"%"]}]]}]]}],["$","div","4",{"className":"flex items-center justify-between","children":[["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"w-3 h-3 bg-red-500 rounded-full mr-3"}],["$","span",null,{"className":"text-sm font-medium text-gray-900","children":"Outros"}]]}],["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-sm text-gray-500","children":3}],["$","span",null,{"className":"text-sm font-medium text-gray-900","children":[6.4,"%"]}]]}]]}]]}]}]]}],["$","div",null,{"className":"bg-white rounded-lg shadow","children":[["$","div",null,{"className":"px-6 py-4 border-b border-gray-200","children":["$","h2",null,{"className":"text-lg font-medium text-gray-900","children":"Funcionários com Mais Faltas"}]}],["$","div",null,{"className":"p-6","children":["$","div",null,{"className":"space-y-4","children":[["$","div","0",{"className":"flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg","children":[["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-user-x w-4 h-4 text-red-600","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],["$","line","3nzzx3",{"x1":"17","x2":"22","y1":"8","y2":"13"}],["$","line","1swrse",{"x1":"22","x2":"17","y1":"8","y2":"13"}],"$undefined"]}]}],["$","div",null,{"className":"ml-3","children":[["$","p",null,{"className":"text-sm font-medium text-gray-900","children":"Roberto Silva"}],["$","p",null,{"className":"text-xs text-gray-500","children":"Operações"}]]}]]}],"$L17"]}],"$L18","$L19","$L1a"]}]}]]}]]}]
12:["$","div",null,{"className":"bg-white rounded-lg shadow","children":[["$","div",null,{"className":"px-6 py-4 border-b border-gray-200","children":["$","h2",null,{"className":"text-lg font-medium text-gray-900","children":"Absenteísmo por Departamento"}]}],["$","div",null,{"className":"p-6","children":["$","div",null,{"className":"overflow-x-auto","children":["$","table",null,{"className":"min-w-full divide-y divide-gray-200","children":[["$","thead",null,{"className":"bg-gray-50","children":["$","tr",null,{"children":[["$","th",null,{"className":"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider","children":"Departamento"}],["$","th",null,{"className":"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider","children":"Funcionários"}],["$","th",null,{"className":"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider","children":"Taxa de Absenteísmo"}],["$","th",null,{"className":"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider","children":"Total de Faltas"}]]}]}],["$","tbody",null,{"className":"bg-white divide-y divide-gray-200","children":[["$","tr","0",{"children":[["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900","children":"Operações"}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":35}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":["$","span",null,{"className":"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800","children":[5.2,"%"]}]}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":18}]]}],["$","tr","1",{"children":[["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900","children":"TI"}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":18}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":["$","span",null,{"className":"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800","children":[4.2,"%"]}]}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":8}]]}],["$","tr","2",{"children":[["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900","children":"Financeiro"}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":12}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":["$","span",null,{"className":"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800","children":[3.8,"%"]}]}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":5}]]}],["$","tr","3",{"children":[["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900","children":"Vendas"}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":25}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":["$","span",null,{"className":"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800","children":[2.1,"%"]}]}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":5}]]}],["$","tr","4",{"children":[["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900","children":"RH"}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":8}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":["$","span",null,{"className":"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800","children":[1.5,"%"]}]}],["$","td",null,{"className":"px-6 py-4 whitespace-nowrap text-sm text-gray-500","children":1}]]}]]}]]}]}]}]]}]
13:["$","div",null,{"className":"bg-white rounded-lg shadow","children":[["$","div",null,{"className":"px-6 py-4 border-b border-gray-200","children":["$","h2",null,{"className":"text-lg font-medium text-gray-900","children":"Alertas e Recomendações"}]}],["$","div",null,{"className":"p-6","children":["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-6","children":[["$","div",null,{"className":"p-4 bg-red-50 border border-red-200 rounded-lg","children":["$","div",null,{"className":"flex items-start","children":[["$","div",null,{"className":"flex-shrink-0","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-triangle-alert h-5 w-5 text-red-600","aria-hidden":"true","children":[["$","path","wmoenq",{"d":"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"}],["$","path","juzpu7",{"d":"M12 9v4"}],["$","path","p32p05",{"d":"M12 17h.01"}],"$undefined"]}]}],["$","div",null,{"className":"ml-3","children":[["$","h3",null,{"className":"text-sm font-medium text-red-800","children":"Atenção Necessária"}],["$","p",null,{"className":"text-sm text-red-700 mt-1","children":"O departamento de Operações apresenta taxa de absenteísmo acima da média. Recomenda-se investigação das causas."}]]}]]}]}],["$","div",null,{"className":"p-4 bg-yellow-50 border border-yellow-200 rounded-lg","children":["$","div",null,{"className":"flex items-start","children":[["$","div",null,{"className":"flex-shrink-0","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-5 w-5 text-yellow-600","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}]}],["$","div",null,{"className":"ml-3","children":[["$","h3",null,{"className":"text-sm font-medium text-yellow-800","children":"Acompanhamento"}],["$","p",null,{"className":"text-sm text-yellow-700 mt-1","children":"4 funcionários apresentam padrão de faltas recorrentes. Considere reuniões individuais para entender as causas."}]]}]]}]}]]}]}]]}]
14:["$","$L1b",null,{"children":["$L1c",["$","$L1d",null,{"promise":"$@1e"}]]}]
15:["$","$1","h",{"children":[null,[["$","$L1f",null,{"children":"$L20"}],null],["$","$L21",null,{"children":["$","div",null,{"hidden":true,"children":["$","$22",null,{"fallback":null,"children":"$L23"}]}]}]]}]
17:["$","div",null,{"className":"text-right","children":[["$","p",null,{"className":"text-sm font-medium text-red-600","children":[8," faltas"]}],["$","p",null,{"className":"text-xs text-gray-500","children":["Última: ","2024-01-20"]}]]}]
18:["$","div","1",{"className":"flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg","children":[["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-user-x w-4 h-4 text-red-600","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],["$","line","3nzzx3",{"x1":"17","x2":"22","y1":"8","y2":"13"}],["$","line","1swrse",{"x1":"22","x2":"17","y1":"8","y2":"13"}],"$undefined"]}]}],["$","div",null,{"className":"ml-3","children":[["$","p",null,{"className":"text-sm font-medium text-gray-900","children":"Fernanda Costa"}],["$","p",null,{"className":"text-xs text-gray-500","children":"Vendas"}]]}]]}],["$","div",null,{"className":"text-right","children":[["$","p",null,{"className":"text-sm font-medium text-red-600","children":[6," faltas"]}],["$","p",null,{"className":"text-xs text-gray-500","children":["Última: ","2024-01-18"]}]]}]]}]
19:["$","div","2",{"className":"flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg","children":[["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-user-x w-4 h-4 text-red-600","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],["$","line","3nzzx3",{"x1":"17","x2":"22","y1":"8","y2":"13"}],["$","line","1swrse",{"x1":"22","x2":"17","y1":"8","y2":"13"}],"$undefined"]}]}],["$","div",null,{"className":"ml-3","children":[["$","p",null,{"className":"text-sm font-medium text-gray-900","children":"Carlos Mendes"}],["$","p",null,{"className":"text-xs text-gray-500","children":"TI"}]]}]]}],["$","div",null,{"className":"text-right","children":[["$","p",null,{"className":"text-sm font-medium text-red-600","children":[5," faltas"]}],["$","p",null,{"className":"text-xs text-gray-500","children":["Última: ","2024-01-15"]}]]}]]}]
1a:["$","div","3",{"className":"flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg","children":[["$","div",null,{"className":"flex items-center","children":[["$","div",null,{"className":"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-user-x w-4 h-4 text-red-600","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],["$","line","3nzzx3",{"x1":"17","x2":"22","y1":"8","y2":"13"}],["$","line","1swrse",{"x1":"22","x2":"17","y1":"8","y2":"13"}],"$undefined"]}]}],["$","div",null,{"className":"ml-3","children":[["$","p",null,{"className":"text-sm font-medium text-gray-900","children":"Ana Rodrigues"}],["$","p",null,{"className":"text-xs text-gray-500","children":"Financeiro"}]]}]]}],["$","div",null,{"className":"text-right","children":[["$","p",null,{"className":"text-sm font-medium text-red-600","children":[4," faltas"]}],["$","p",null,{"className":"text-xs text-gray-500","children":["Última: ","2024-01-12"]}]]}]]}]
20:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
1c:null
24:I[8175,[],"IconMark"]
1e:{"metadata":[["$","title","0",{"children":"Análise de Absenteísmo - RLPONTO"}],["$","meta","1",{"name":"description","content":"Análise detalhada de absenteísmo e faltas"}],["$","meta","2",{"name":"author","content":"Your Name"}],["$","meta","3",{"name":"keywords","content":"Next.js,React,TypeScript,Tailwind CSS"}],["$","meta","4",{"name":"creator","content":"Your Name"}],["$","meta","5",{"name":"robots","content":"index, follow"}],["$","meta","6",{"property":"og:title","content":"My App"}],["$","meta","7",{"property":"og:description","content":"A professional Next.js application"}],["$","meta","8",{"property":"og:url","content":"http://************"}],["$","meta","9",{"property":"og:site_name","content":"My App"}],["$","meta","10",{"property":"og:locale","content":"en_US"}],["$","meta","11",{"property":"og:type","content":"website"}],["$","meta","12",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","13",{"name":"twitter:title","content":"My App"}],["$","meta","14",{"name":"twitter:description","content":"A professional Next.js application"}],["$","link","15",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L24","16",{}]],"error":null,"digest":"$undefined"}
23:"$1e:metadata"
