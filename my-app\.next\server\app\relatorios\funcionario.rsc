1:"$Sreact.fragment"
2:I[6062,["7177","static/chunks/app/layout-7e63b6183d7635de.js"],"SessionProvider"]
3:I[6403,["7177","static/chunks/app/layout-7e63b6183d7635de.js"],"TextFormattingProvider"]
4:I[7555,[],""]
5:I[1901,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","8039","static/chunks/app/error-da7a176665770fd1.js"],"default"]
6:I[1295,[],""]
7:I[2558,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","4345","static/chunks/app/not-found-0d232e224bc38f5f.js"],"default"]
8:I[277,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","9305","static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js"],"DashboardHeader"]
9:I[2623,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","9305","static/chunks/app/(dashboard)/layout-487ad92ee05a5977.js"],"DashboardNav"]
a:I[6874,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","2823","static/chunks/app/(dashboard)/relatorios/funcionario/page-e386721615fe5c41.js"],""]
b:"$Sreact.suspense"
12:I[8393,[],""]
:HL["/_next/static/css/b293d1867f231925.css","style"]
0:{"P":null,"b":"57groY_ofPMaGR4OOS9UN","p":"","c":["","relatorios","funcionario"],"i":false,"f":[[["",{"children":["(dashboard)",{"children":["relatorios",{"children":["funcionario",{"children":["__PAGE__",{}]}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/b293d1867f231925.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$5","errorStyles":[],"errorScripts":[],"template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","$L7",null,{}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]}]]}],{"children":["(dashboard)",["$","$1","c",{"children":[null,["$","div",null,{"className":"min-h-screen bg-gray-50","children":[["$","$L8",null,{}],["$","div",null,{"className":"flex","children":[["$","$L9",null,{}],["$","main",null,{"className":"flex-1 overflow-auto","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]]}]]}],{"children":["relatorios",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["funcionario",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-gray-50","children":["$","div",null,{"className":"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8","children":["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"className":"flex items-center justify-between","children":["$","div",null,{"className":"flex items-center space-x-4","children":[["$","$La",null,{"href":"/relatorios","children":["$","button",null,{"type":"button","className":"inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none border border-gray-400 bg-transparent text-gray-700 hover:bg-gray-50 hover:border-gray-500 h-8 px-3 text-sm","disabled":false,"onClick":"$undefined","children":[false,[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-left h-4 w-4 mr-2","aria-hidden":"true","children":[["$","path","1l729n",{"d":"m12 19-7-7 7-7"}],["$","path","x3x0zl",{"d":"M19 12H5"}],"$undefined"]}],"Voltar"]]}]}],["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"p-2 bg-blue-600 rounded-lg","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-user h-8 w-8 text-white","aria-hidden":"true","children":[["$","path","975kel",{"d":"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"}],["$","circle","17ys0d",{"cx":"12","cy":"7","r":"4"}],"$undefined"]}]}],["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold text-gray-900","children":"Relatório Individual"}],["$","p",null,{"className":"text-gray-600","children":"Configure e gere relatório de funcionário específico"}]]}]]}]]}]}],["$","div",null,{"className":"bg-white rounded-lg shadow","children":["$","$b",null,{"fallback":["$","div",null,{"className":"p-6 space-y-6","children":[["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-6","children":[["$","div","0",{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/4"}],["$","div",null,{"className":"h-10 bg-gray-200 rounded"}]]}],["$","div","1",{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/4"}],["$","div",null,{"className":"h-10 bg-gray-200 rounded"}]]}],["$","div","2",{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/4"}],["$","div",null,{"className":"h-10 bg-gray-200 rounded"}]]}],"$Lc"]}],"$Ld","$Le"]}],"children":"$Lf"}]}]]}]}]}],null,"$L10"]}],{},null,false]},null,false]},null,false]},null,false]},null,false],"$L11",false]],"m":"$undefined","G":["$12",[]],"s":false,"S":true}
13:I[4960,["6874","static/chunks/6874-d27b54d0b28e3259.js","4285","static/chunks/4285-3cbfa4a2f71248be.js","3769","static/chunks/3769-e62c0e969fff9246.js","2823","static/chunks/app/(dashboard)/relatorios/funcionario/page-e386721615fe5c41.js"],"ReportForm"]
14:I[9665,[],"OutletBoundary"]
16:I[4911,[],"AsyncMetadataOutlet"]
18:I[9665,[],"ViewportBoundary"]
1a:I[9665,[],"MetadataBoundary"]
c:["$","div","3",{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/4"}],["$","div",null,{"className":"h-10 bg-gray-200 rounded"}]]}]
d:["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/4"}],["$","div",null,{"className":"h-24 bg-gray-200 rounded"}]]}]
e:["$","div",null,{"className":"h-10 bg-gray-200 rounded w-32"}]
f:["$","$L13",null,{"config":{"type":"funcionario","title":"Relatório Individual","description":"Gere um relatório detalhado de um funcionário específico","color":"blue","fields":[{"name":"funcionario","label":"Funcionário","type":"select","required":true,"placeholder":"Selecione um funcionário","options":[{"value":"EMP001","label":"João Silva Santos - EMP001"},{"value":"EMP002","label":"Maria Oliveira Costa - EMP002"},{"value":"EMP003","label":"Carlos Roberto Lima - EMP003"},{"value":"EMP004","label":"Ana Paula Silva - EMP004"},{"value":"EMP005","label":"Pedro Henrique Souza - EMP005"},{"value":"EMP006","label":"Juliana Ferreira Alves - EMP006"},{"value":"EMP007","label":"Roberto Carlos Mendes - EMP007"},{"value":"EMP008","label":"Fernanda Lima Santos - EMP008"}]},{"name":"periodo","label":"Período","type":"daterange","required":true,"defaultValue":{"start":"$D2025-08-01T04:00:00.000Z","end":"$D2025-08-01T15:25:10.447Z"}},{"name":"incluirGraficos","label":"Incluir Gráficos","type":"checkbox","defaultValue":true,"description":"Adicionar gráficos de frequência e horas trabalhadas"},{"name":"incluirDetalhes","label":"Incluir Detalhes Diários","type":"checkbox","defaultValue":false,"description":"Mostrar registros detalhados dia a dia"},{"name":"incluirHorasExtras","label":"Incluir Horas Extras","type":"checkbox","defaultValue":true,"description":"Adicionar seção de horas extras"},{"name":"formato","label":"Formato de Saída","type":"radio","required":true,"defaultValue":"PDF","options":[{"value":"PDF","label":"PDF","description":"Melhor para visualização e impressão"},{"value":"Excel","label":"Excel","description":"Melhor para análise de dados"},{"value":"CSV","label":"CSV","description":"Para importação em outros sistemas"}]}],"sections":[{"title":"Dados Básicos","description":"Informações pessoais e profissionais do funcionário","included":true},{"title":"Resumo do Período","description":"Totalizadores de horas, frequência e estatísticas","included":true},{"title":"Registros de Ponto","description":"Histórico detalhado de entradas e saídas","included":true},{"title":"Análise de Frequência","description":"Gráficos e análises de presença","included":true,"optional":true},{"title":"Horas Extras","description":"Detalhamento de horas extras trabalhadas","included":true,"optional":true},{"title":"Observações","description":"Registros manuais e justificativas","included":false,"optional":true}]}}]
10:["$","$L14",null,{"children":["$L15",["$","$L16",null,{"promise":"$@17"}]]}]
11:["$","$1","h",{"children":[null,[["$","$L18",null,{"children":"$L19"}],null],["$","$L1a",null,{"children":["$","div",null,{"hidden":true,"children":["$","$b",null,{"fallback":null,"children":"$L1b"}]}]}]]}]
19:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
15:null
1c:I[8175,[],"IconMark"]
17:{"metadata":[["$","title","0",{"children":"Relatório Individual - RLPONTO"}],["$","meta","1",{"name":"description","content":"Gerar relatório individual de funcionário"}],["$","meta","2",{"name":"author","content":"Your Name"}],["$","meta","3",{"name":"keywords","content":"Next.js,React,TypeScript,Tailwind CSS"}],["$","meta","4",{"name":"creator","content":"Your Name"}],["$","meta","5",{"name":"robots","content":"index, follow"}],["$","meta","6",{"property":"og:title","content":"My App"}],["$","meta","7",{"property":"og:description","content":"A professional Next.js application"}],["$","meta","8",{"property":"og:url","content":"http://************"}],["$","meta","9",{"property":"og:site_name","content":"My App"}],["$","meta","10",{"property":"og:locale","content":"en_US"}],["$","meta","11",{"property":"og:type","content":"website"}],["$","meta","12",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","13",{"name":"twitter:title","content":"My App"}],["$","meta","14",{"name":"twitter:description","content":"A professional Next.js application"}],["$","link","15",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L1c","16",{}]],"error":null,"digest":"$undefined"}
1b:"$17:metadata"
