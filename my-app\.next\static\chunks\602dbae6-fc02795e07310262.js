"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1309],{5294:(e,t,r)=>{r.d(t,{JY:()=>ro,gL:()=>rH,sx:()=>rI});var n=r(2115),l=r(7650),i=r(52),a=r(4540),o=r(5889),d=r(7380),s=r(9630);function u(e,t){}u.bind(null,"warn");u.bind(null,"error");function c(){}function p(e,t,r){let n=t.map(t=>{var n;let l=(n=t.options,{...r,...n});return e.addEventListener(t.eventName,t.fn,l),function(){e.removeEventListener(t.eventName,t.fn,l)}});return function(){n.forEach(e=>{e()})}}class f extends Error{}function g(e,t){throw new f("Invariant failed")}f.prototype.toString=function(){return this.message};class m extends n.Component{constructor(...e){super(...e),this.callbacks=null,this.unbind=c,this.onWindowError=e=>{let t=this.getCallbacks();t.isDragging()&&t.tryAbort(),e.error instanceof f&&e.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=e=>{this.callbacks=e}}componentDidMount(){this.unbind=p(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(e){if(e instanceof f)return void this.setState({});throw e}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}let b=(e,t)=>{let r=e.droppableId===t.droppableId,n=e.index+1,l=t.index+1;return r?`
      You have moved the item from position ${n}
      to position ${l}
    `:`
    You have moved the item from position ${n}
    in list ${e.droppableId}
    to list ${t.droppableId}
    in position ${l}
  `},h=(e,t,r)=>t.droppableId===r.droppableId?`
      The item ${e}
      has been combined with ${r.draggableId}`:`
      The item ${e}
      in list ${t.droppableId}
      has been combined with ${r.draggableId}
      in list ${r.droppableId}
    `,y=e=>`
  The item has returned to its starting position
  of ${e.index+1}
`,I={dragHandleUsageInstructions:`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,onDragStart:e=>`
  You have lifted an item in position ${e.source.index+1}
`,onDragUpdate:e=>{let t=e.destination;if(t)return b(e.source,t);let r=e.combine;return r?h(e.draggableId,e.source,r):"You are over an area that cannot be dropped on"},onDragEnd:e=>{if("CANCEL"===e.reason)return`
      Movement cancelled.
      ${y(e.source)}
    `;let t=e.destination,r=e.combine;return t?`
      You have dropped the item.
      ${b(e.source,t)}
    `:r?`
      You have dropped the item.
      ${h(e.draggableId,e.source,r)}
    `:`
    The item has been dropped while not over a drop area.
    ${y(e.source)}
  `}};function v(e,t){if(e.length!==t.length)return!1;for(let l=0;l<e.length;l++){var r,n;if(!((r=e[l])===(n=t[l])||Number.isNaN(r)&&Number.isNaN(n))&&1)return!1}return!0}function x(e,t){let r=(0,n.useState)(()=>({inputs:t,result:e()}))[0],l=(0,n.useRef)(!0),i=(0,n.useRef)(r),a=l.current||t&&i.current.inputs&&v(t,i.current.inputs)?i.current:{inputs:t,result:e()};return(0,n.useEffect)(()=>{l.current=!1,i.current=a},[a]),a.result}function D(e,t){return x(()=>e,t)}let E={x:0,y:0},A=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),N=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),R=(e,t)=>e.x===t.x&&e.y===t.y,C=e=>({x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}),P=(e,t,r=0)=>"x"===e?{x:t,y:r}:{x:r,y:t},w=(e,t)=>Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2),O=(e,t)=>Math.min(...t.map(t=>w(e,t))),S=e=>t=>({x:e(t.x),y:e(t.y)}),B=(e,t)=>({top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}),G=e=>[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}];var L=({page:e,withPlaceholder:t,axis:r,frame:n})=>{let l=((e,t)=>t&&t.shouldClipSubject?((e,t)=>{let r=(0,o.l)({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return r.width<=0||r.height<=0?null:r})(t.pageMarginBox,e):(0,o.l)(e))(((e,t,r)=>r&&r.increasedBy?{...e,[t.end]:e[t.end]+r.increasedBy[t.line]}:e)(((e,t)=>t?B(e,t.scroll.diff.displacement):e)(e.marginBox,n),r,t),n);return{page:e,withPlaceholder:t,active:l}},T=(e,t)=>{e.frame||g();let r=e.frame,n=N(t,r.scroll.initial),l=C(n),i={...r,scroll:{initial:r.scroll.initial,current:t,diff:{value:n,displacement:l},max:r.scroll.max}},a=L({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:i});return{...e,frame:i,subject:a}};function _(e,t=v){let r=null;function n(...l){if(r&&r.lastThis===this&&t(l,r.lastArgs))return r.lastResult;let i=e.apply(this,l);return r={lastResult:i,lastArgs:l,lastThis:this},i}return n.clear=function(){r=null},n}let M=_(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),F=_(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),k=_(e=>Object.values(e)),$=_(e=>Object.values(e));var W=_((e,t)=>$(t).filter(t=>e===t.descriptor.droppableId).sort((e,t)=>e.descriptor.index-t.descriptor.index));function U(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function H(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var V=_((e,t)=>t.filter(t=>t.descriptor.id!==e.descriptor.id)),j=(e,t)=>e.descriptor.droppableId===t.descriptor.id;let z={point:E,value:0},q={invisible:{},visible:{},all:[]},Y={displaced:q,displacedBy:z,at:null};var J=(e,t)=>r=>e<=r&&r<=t,X=e=>{let t=J(e.top,e.bottom),r=J(e.left,e.right);return n=>{if(t(n.top)&&t(n.bottom)&&r(n.left)&&r(n.right))return!0;let l=t(n.top)||t(n.bottom),i=r(n.left)||r(n.right);if(l&&i)return!0;let a=n.top<e.top&&n.bottom>e.bottom,o=n.left<e.left&&n.right>e.right;return!!a&&!!o||a&&i||o&&l}},K=e=>{let t=J(e.top,e.bottom),r=J(e.left,e.right);return e=>t(e.top)&&t(e.bottom)&&r(e.left)&&r(e.right)};let Q={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},Z={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"},ee=({target:e,destination:t,viewport:r,withDroppableDisplacement:n,isVisibleThroughFrameFn:l})=>{let i=n?((e,t)=>B(e,t.frame?t.frame.scroll.diff.displacement:E))(e,t):e;return((e,t,r)=>!!t.subject.active&&r(t.subject.active)(e))(i,t,l)&&l(r)(i)},et=e=>ee({...e,isVisibleThroughFrameFn:K});function er({afterDragging:e,destination:t,displacedBy:r,viewport:n,forceShouldAnimate:l,last:i}){return e.reduce(function(e,a){let d=function(e,t){let r=e.page.marginBox,n={top:t.point.y,right:0,bottom:0,left:t.point.x};return(0,o.l)((0,o.fT)(r,n))}(a,r),s=a.descriptor.id;if(e.all.push(s),!ee({target:d,destination:t,viewport:n,withDroppableDisplacement:!0,isVisibleThroughFrameFn:X}))return e.invisible[a.descriptor.id]=!0,e;let u=((e,t,r)=>{if("boolean"==typeof r)return r;if(!t)return!0;let{invisible:n,visible:l}=t;if(n[e])return!1;let i=l[e];return!i||i.shouldAnimate})(s,i,l);return e.visible[s]={draggableId:s,shouldAnimate:u},e},{all:[],visible:{},invisible:{}})}function en({insideDestination:e,inHomeList:t,displacedBy:r,destination:n}){let l=function(e,t){if(!e.length)return 0;let r=e[e.length-1].descriptor.index;return t.inHomeList?r:r+1}(e,{inHomeList:t});return{displaced:q,displacedBy:r,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:l}}}}function el({draggable:e,insideDestination:t,destination:r,viewport:n,displacedBy:l,last:i,index:a,forceShouldAnimate:o}){let d=j(e,r);if(null==a)return en({insideDestination:t,inHomeList:d,displacedBy:l,destination:r});let s=t.find(e=>e.descriptor.index===a);if(!s)return en({insideDestination:t,inHomeList:d,displacedBy:l,destination:r});let u=V(e,t),c=t.indexOf(s);return{displaced:er({afterDragging:u.slice(c),destination:r,displacedBy:l,last:i,viewport:n.frame,forceShouldAnimate:o}),displacedBy:l,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:a}}}}function ei(e,t){return!!t.effected[e]}let ea=(e,t)=>t.margin[e.start]+t.borderBox[e.size]/2,eo=(e,t,r)=>t[e.crossAxisStart]+r.margin[e.crossAxisStart]+r.borderBox[e.crossAxisSize]/2,ed=({axis:e,moveRelativeTo:t,isMoving:r})=>P(e.line,t.marginBox[e.end]+ea(e,r),eo(e,t.marginBox,r)),es=({axis:e,moveRelativeTo:t,isMoving:r})=>P(e.line,t.marginBox[e.start]-((e,t)=>t.margin[e.end]+t.borderBox[e.size]/2)(e,r),eo(e,t.marginBox,r));var eu=(e,t)=>{let r=e.frame;return r?A(t,r.scroll.diff.displacement):t},ec=e=>{let t=(({impact:e,draggable:t,droppable:r,draggables:n,afterCritical:l})=>{let i=t.page.borderBox.center,a=e.at;return r&&a?"REORDER"===a.type?(({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:l})=>{let i=W(n.descriptor.id,r),a=t.page,d=n.axis;if(!i.length)return(({axis:e,moveInto:t,isMoving:r})=>P(e.line,t.contentBox[e.start]+ea(e,r),eo(e,t.contentBox,r)))({axis:d,moveInto:n.page,isMoving:a});let{displaced:s,displacedBy:u}=e,c=s.all[0];if(c){let e=r[c];return ei(c,l)?es({axis:d,moveRelativeTo:e.page,isMoving:a}):es({axis:d,moveRelativeTo:(0,o.cY)(e.page,u.point),isMoving:a})}let p=i[i.length-1];return p.descriptor.id===t.descriptor.id?a.borderBox.center:ei(p.descriptor.id,l)?ed({axis:d,moveRelativeTo:(0,o.cY)(p.page,C(l.displacedBy.point)),isMoving:a}):ed({axis:d,moveRelativeTo:p.page,isMoving:a})})({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:l}):(({afterCritical:e,impact:t,draggables:r})=>{let n=H(t);n||g();let l=n.draggableId;return A(r[l].page.borderBox.center,(({displaced:e,afterCritical:t,combineWith:r,displacedBy:n})=>{let l=!!(e.visible[r]||e.invisible[r]);return ei(r,t)?l?E:C(n.point):l?n.point:E})({displaced:t.displaced,afterCritical:e,combineWith:l,displacedBy:t.displacedBy}))})({impact:e,draggables:n,afterCritical:l}):i})(e),r=e.droppable;return r?eu(r,t):t},ep=(e,t)=>{let r=N(t,e.scroll.initial),n=C(r);return{frame:(0,o.l)({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:r,displacement:n}}}};function ef(e,t){return e.map(e=>t[e])}var eg=({pageBorderBoxCenter:e,draggable:t,viewport:r})=>{let n=N(A(r.scroll.diff.displacement,e),t.page.borderBox.center);return A(t.client.borderBox.center,n)},em=({draggable:e,destination:t,newPageBorderBoxCenter:r,viewport:n,withDroppableDisplacement:l,onlyOnMainAxis:i=!1})=>{let a=N(r,e.page.borderBox.center),o={target:B(e.page.borderBox,a),destination:t,withDroppableDisplacement:l,viewport:n};return i?(e=>{let t;return ee({...e,isVisibleThroughFrameFn:(t=e.destination.axis,e=>{let r=J(e.top,e.bottom),n=J(e.left,e.right);return e=>t===Q?r(e.top)&&r(e.bottom):n(e.left)&&n(e.right)})})})(o):et(o)};let eb=e=>{let t=e.subject.active;return t||g(),t},eh=(e,t)=>{let r=e.page.borderBox.center;return ei(e.descriptor.id,t)?N(r,t.displacedBy.point):r};var ey=_(function(e,t){let r=t[e.line];return{value:r,point:P(e.line,r)}});let eI=(e,t)=>({...e,scroll:{...e.scroll,max:t}}),ev=(e,t,r)=>{let n=e.frame;j(t,e)&&g(),e.subject.withPlaceholder&&g();let l=ey(e.axis,t.displaceBy).point,i=((e,t,r)=>{let n=e.axis;if("virtual"===e.descriptor.mode)return P(n.line,t[n.line]);let l=e.subject.page.contentBox[n.size],i=W(e.descriptor.id,r).reduce((e,t)=>e+t.client.marginBox[n.size],0)+t[n.line]-l;return i<=0?null:P(n.line,i)})(e,l,r),a={placeholderSize:l,increasedBy:i,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!n){let t=L({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:e.frame});return{...e,subject:t}}let o=i?A(n.scroll.max,i):n.scroll.max,d=eI(n,o),s=L({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:d});return{...e,subject:s,frame:d}};var ex=e=>{let t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null};function eD(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function eE(e){let t=J(e.top,e.bottom),r=J(e.left,e.right);return function(e){return t(e.y)&&r(e.x)}}let eA=(e,t)=>(0,o.l)(B(e,t));function eN({displaced:e,id:t}){return!!(e.visible[t]||e.invisible[t])}var eR=({pageOffset:e,draggable:t,draggables:r,droppables:n,previousImpact:l,viewport:i,afterCritical:a})=>{let o=eA(t.page.borderBox,e),d=function({pageBorderBox:e,draggable:t,droppables:r}){let n=k(r).filter(t=>{if(!t.isEnabled)return!1;let r=t.subject.active;if(!r||!(e.left<r.right)||!(e.right>r.left)||!(e.top<r.bottom)||!(e.bottom>r.top))return!1;if(eE(r)(e.center))return!0;let n=t.axis,l=r.center[n.crossAxisLine],i=e[n.crossAxisStart],a=e[n.crossAxisEnd],o=J(r[n.crossAxisStart],r[n.crossAxisEnd]),d=o(i),s=o(a);return!d&&!s||(d?i<l:a>l)});return n.length?1===n.length?n[0].descriptor.id:function({pageBorderBox:e,draggable:t,candidates:r}){let n=t.page.borderBox.center,l=r.map(t=>{let r=t.axis,l=P(t.axis.line,e.center[r.line],t.page.borderBox.center[r.crossAxisLine]);return{id:t.descriptor.id,distance:w(n,l)}}).sort((e,t)=>t.distance-e.distance);return l[0]?l[0].id:null}({pageBorderBox:e,draggable:t,candidates:n}):null}({pageBorderBox:o,draggable:t,droppables:n});if(!d)return Y;let s=n[d],u=W(s.descriptor.id,r),c=((e,t)=>{let r=e.frame;return r?eA(t,r.scroll.diff.value):t})(s,o);return(({draggable:e,pageBorderBoxWithDroppableScroll:t,previousImpact:r,destination:n,insideDestination:l,afterCritical:i})=>{if(!n.isCombineEnabled)return null;let a=n.axis,o=ey(n.axis,e.displaceBy),d=o.value,s=t[a.start],u=t[a.end],c=V(e,l).find(e=>{let t=e.descriptor.id,n=e.page.borderBox,l=n[a.size]/4,o=ei(t,i),c=eN({displaced:r.displaced,id:t});return o?c?u>n[a.start]+l&&u<n[a.end]-l:s>n[a.start]-d+l&&s<n[a.end]-d-l:c?u>n[a.start]+d+l&&u<n[a.end]+d-l:s>n[a.start]+l&&s<n[a.end]-l});return c?{displacedBy:o,displaced:r.displaced,at:{type:"COMBINE",combine:{draggableId:c.descriptor.id,droppableId:n.descriptor.id}}}:null})({pageBorderBoxWithDroppableScroll:c,draggable:t,previousImpact:l,destination:s,insideDestination:u,afterCritical:a})||(({pageBorderBoxWithDroppableScroll:e,draggable:t,destination:r,insideDestination:n,last:l,viewport:i,afterCritical:a})=>{let o=r.axis,d=ey(r.axis,t.displaceBy),s=d.value,u=e[o.start],c=e[o.end],p=V(t,n).find(e=>{let t=e.descriptor.id,r=e.page.borderBox.center[o.line],n=ei(t,a),i=eN({displaced:l,id:t});return n?i?c<=r:u<r-s:i?c<=r+s:u<r})||null,f=function({draggable:e,closest:t,inHomeList:r}){return t?r&&t.descriptor.index>e.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}({draggable:t,closest:p,inHomeList:j(t,r)});return el({draggable:t,insideDestination:n,destination:r,viewport:i,last:l,displacedBy:d,index:f})})({pageBorderBoxWithDroppableScroll:c,draggable:t,destination:s,insideDestination:u,last:l.displaced,viewport:i,afterCritical:a})},eC=(e,t)=>({...e,[t.descriptor.id]:t}),eP=({state:e,clientSelection:t,dimensions:r,viewport:n,impact:l,scrollJumpRequest:i})=>{let a=n||e.viewport,o=r||e.dimensions,d=t||e.current.client.selection,s=N(d,e.initial.client.selection),u={offset:s,selection:d,borderBoxCenter:A(e.initial.client.borderBoxCenter,s)},c={selection:A(u.selection,a.scroll.current),borderBoxCenter:A(u.borderBoxCenter,a.scroll.current),offset:A(u.offset,a.scroll.diff.value)},p={client:u,page:c};if("COLLECTING"===e.phase)return{...e,dimensions:o,viewport:a,current:p};let f=o.draggables[e.critical.draggable.id],m=l||eR({pageOffset:c.offset,draggable:f,draggables:o.draggables,droppables:o.droppables,previousImpact:e.impact,viewport:a,afterCritical:e.afterCritical}),b=(({draggable:e,draggables:t,droppables:r,previousImpact:n,impact:l})=>{let i=(({previousImpact:e,impact:t,droppables:r})=>{let n=ex(e),l=ex(t);if(!n||n===l)return r;let i=r[n];return i.subject.withPlaceholder?eC(r,(e=>{let t=e.subject.withPlaceholder;t||g();let r=e.frame;if(!r){let t=L({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return{...e,subject:t}}let n=t.oldFrameMaxScroll;n||g();let l=eI(r,n),i=L({page:e.subject.page,axis:e.axis,frame:l,withPlaceholder:null});return{...e,subject:i,frame:l}})(i)):r})({previousImpact:n,impact:l,droppables:r}),a=ex(l);if(!a)return i;let o=r[a];return j(e,o)||o.subject.withPlaceholder?i:eC(i,ev(o,e,t))})({draggable:f,impact:m,previousImpact:e.impact,draggables:o.draggables,droppables:o.droppables});return{...e,current:p,dimensions:{draggables:o.draggables,droppables:b},impact:m,viewport:a,scrollJumpRequest:i||null,forceShouldAnimate:!i&&null}},ew=({impact:e,viewport:t,draggables:r,destination:n,forceShouldAnimate:l})=>{var i;let a=e.displaced,o=er({afterDragging:(i=a.all,i.map(e=>r[e])),destination:n,displacedBy:e.displacedBy,viewport:t.frame,forceShouldAnimate:l,last:a});return{...e,displaced:o}},eO=({impact:e,draggable:t,droppable:r,draggables:n,viewport:l,afterCritical:i})=>eg({pageBorderBoxCenter:ec({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:i}),draggable:t,viewport:l}),eS=({state:e,dimensions:t,viewport:r})=>{"SNAP"!==e.movementMode&&g();let n=e.impact,l=r||e.viewport,i=t||e.dimensions,{draggables:a,droppables:o}=i,d=a[e.critical.draggable.id],s=ex(n);s||g();let u=o[s],c=ew({impact:n,viewport:l,destination:u,draggables:a}),p=eO({impact:c,draggable:d,droppable:u,draggables:a,viewport:l,afterCritical:e.afterCritical});return eP({impact:c,clientSelection:p,state:e,dimensions:i,viewport:l})},eB=({draggable:e,home:t,draggables:r,viewport:n})=>{let l=ey(t.axis,e.displaceBy),i=W(t.descriptor.id,r),a=i.indexOf(e);-1===a&&g();let o=i.slice(a+1),d=o.reduce((e,t)=>(e[t.descriptor.id]=!0,e),{}),s={inVirtualList:"virtual"===t.descriptor.mode,displacedBy:l,effected:d};return{impact:{displaced:er({afterDragging:o,destination:t,displacedBy:l,last:null,viewport:n.frame,forceShouldAnimate:!1}),displacedBy:l,at:{type:"REORDER",destination:(e=>({index:e.index,droppableId:e.droppableId}))(e.descriptor)}},afterCritical:s}};let eG=e=>{},eL=e=>{},eT=(e,t,r)=>{let n=((e,t)=>({draggables:e.draggables,droppables:eC(e.droppables,t)}))(e.dimensions,t);return"SNAP"!==e.movementMode||r?eP({state:e,dimensions:n}):eS({state:e,dimensions:n})};function e_(e){return e.isDragging&&"SNAP"===e.movementMode?{...e,scrollJumpRequest:null}:e}let eM={phase:"IDLE",completed:null,shouldFlush:!1};var eF=(e=eM,t)=>{if("FLUSH"===t.type)return{...eM,shouldFlush:!0};if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&g();let{critical:r,clientSelection:n,viewport:l,dimensions:i,movementMode:a}=t.payload,o=i.draggables[r.draggable.id],d=i.droppables[r.droppable.id],s={selection:n,borderBoxCenter:o.client.borderBox.center,offset:E},u={client:s,page:{selection:A(s.selection,l.scroll.initial),borderBoxCenter:A(s.selection,l.scroll.initial),offset:A(s.selection,l.scroll.diff.value)}},c=k(i.droppables).every(e=>!e.isFixedOnPage),{impact:p,afterCritical:f}=eB({draggable:o,home:d,draggables:i.draggables,viewport:l});return{phase:"DRAGGING",isDragging:!0,critical:r,movementMode:a,dimensions:i,initial:u,current:u,isWindowScrollAllowed:c,impact:p,afterCritical:f,onLiftImpact:p,viewport:l,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type)return"COLLECTING"===e.phase||"DROP_PENDING"===e.phase?e:("DRAGGING"!==e.phase&&g(),{...e,phase:"COLLECTING"});if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&g(),(({state:e,published:t})=>{eG();let r=t.modified.map(t=>T(e.dimensions.droppables[t.droppableId],t.scroll)),n={...e.dimensions.droppables,...M(r)},l=F((({additions:e,updatedDroppables:t,viewport:r})=>{let n=r.scroll.diff.value;return e.map(e=>{let l=A(n,(e=>{let t=e.frame;return t||g(),t})(t[e.descriptor.droppableId]).scroll.diff.value);return(({draggable:e,offset:t,initialWindowScroll:r})=>{let n=(0,o.cY)(e.client,t),l=(0,o.SQ)(n,r);return{...e,placeholder:{...e.placeholder,client:n},client:n,page:l}})({draggable:e,offset:l,initialWindowScroll:r.scroll.initial})})})({additions:t.additions,updatedDroppables:n,viewport:e.viewport})),i={...e.dimensions.draggables,...l};t.removals.forEach(e=>{delete i[e]});let a={droppables:n,draggables:i},d=ex(e.impact),s=d?a.droppables[d]:null,{impact:u,afterCritical:c}=eB({draggable:a.draggables[e.critical.draggable.id],home:a.droppables[e.critical.droppable.id],draggables:i,viewport:e.viewport}),p=s&&s.isCombineEnabled?e.impact:u,f=eR({pageOffset:e.current.page.offset,draggable:a.draggables[e.critical.draggable.id],draggables:a.draggables,droppables:a.droppables,previousImpact:p,viewport:e.viewport,afterCritical:c});eL();let m={...e,phase:"DRAGGING",impact:f,onLiftImpact:u,dimensions:a,afterCritical:c,forceShouldAnimate:!1};return"COLLECTING"===e.phase?m:{...m,phase:"DROP_PENDING",reason:e.reason,isWaiting:!1}})({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;eD(e)||g();let{client:r}=t.payload;return R(r,e.current.client.selection)?e:eP({state:e,clientSelection:r,impact:"SNAP"===e.movementMode?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"COLLECTING"===e.phase)return e_(e);eD(e)||g();let{id:r,newScroll:n}=t.payload,l=e.dimensions.droppables[r];return l?eT(e,T(l,n),!1):e}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;eD(e)||g();let{id:r,isEnabled:n}=t.payload,l=e.dimensions.droppables[r];return l||g(),l.isEnabled===n&&g(),eT(e,{...l,isEnabled:n},!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;eD(e)||g();let{id:r,isCombineEnabled:n}=t.payload,l=e.dimensions.droppables[r];return l||g(),l.isCombineEnabled===n&&g(),eT(e,{...l,isCombineEnabled:n},!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;eD(e)||g(),e.isWindowScrollAllowed||g();let r=t.payload.newScroll;if(R(e.viewport.scroll.current,r))return e_(e);let n=ep(e.viewport,r);return"SNAP"===e.movementMode?eS({state:e,viewport:n}):eP({state:e,viewport:n})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!eD(e))return e;let r=t.payload.maxScroll;if(R(r,e.viewport.scroll.max))return e;let n={...e.viewport,scroll:{...e.viewport.scroll,max:r}};return{...e,viewport:n}}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&g();let r=(({state:e,type:t})=>{let r=((e,t)=>{let r=ex(e);return r?t[r]:null})(e.impact,e.dimensions.droppables),n=!!r,l=e.dimensions.droppables[e.critical.droppable.id],i=r||l,a=i.axis.direction,o="vertical"===a&&("MOVE_UP"===t||"MOVE_DOWN"===t)||"horizontal"===a&&("MOVE_LEFT"===t||"MOVE_RIGHT"===t);if(o&&!n)return null;let d="MOVE_DOWN"===t||"MOVE_RIGHT"===t,s=e.dimensions.draggables[e.critical.draggable.id],u=e.current.page.borderBoxCenter,{draggables:c,droppables:p}=e.dimensions;return o?(({isMovingForward:e,draggable:t,destination:r,draggables:n,previousImpact:l,viewport:i,previousPageBorderBoxCenter:a,previousClientSelection:o,afterCritical:d})=>{if(!r.isEnabled)return null;let s=W(r.descriptor.id,n),u=j(t,r),c=(({isMovingForward:e,draggable:t,destination:r,insideDestination:n,previousImpact:l})=>{if(!r.isCombineEnabled||!U(l))return null;function i(e){let t={type:"COMBINE",combine:{draggableId:e,droppableId:r.descriptor.id}};return{...l,at:t}}let a=l.displaced.all,o=a.length?a[0]:null;if(e)return o?i(o):null;let d=V(t,n);if(!o)return d.length?i(d[d.length-1].descriptor.id):null;let s=d.findIndex(e=>e.descriptor.id===o);-1===s&&g();let u=s-1;return u<0?null:i(d[u].descriptor.id)})({isMovingForward:e,draggable:t,destination:r,insideDestination:s,previousImpact:l})||(({isMovingForward:e,isInHomeList:t,draggable:r,draggables:n,destination:l,insideDestination:i,previousImpact:a,viewport:o,afterCritical:d})=>{let s=a.at;if(s||g(),"REORDER"===s.type){let n=(({isMovingForward:e,isInHomeList:t,insideDestination:r,location:n})=>{if(!r.length)return null;let l=n.index,i=e?l+1:l-1,a=r[0].descriptor.index,o=r[r.length-1].descriptor.index;return i<a||i>(t?o:o+1)?null:i})({isMovingForward:e,isInHomeList:t,location:s.destination,insideDestination:i});return null==n?null:el({draggable:r,insideDestination:i,destination:l,viewport:o,last:a.displaced,displacedBy:a.displacedBy,index:n})}let u=(({isMovingForward:e,destination:t,draggables:r,combine:n,afterCritical:l})=>{if(!t.isCombineEnabled)return null;let i=n.draggableId,a=r[i].descriptor.index;return ei(i,l)?e?a:a-1:e?a+1:a})({isMovingForward:e,destination:l,displaced:a.displaced,draggables:n,combine:s.combine,afterCritical:d});return null==u?null:el({draggable:r,insideDestination:i,destination:l,viewport:o,last:a.displaced,displacedBy:a.displacedBy,index:u})})({isMovingForward:e,isInHomeList:u,draggable:t,draggables:n,destination:r,insideDestination:s,previousImpact:l,viewport:i,afterCritical:d});if(!c)return null;let p=ec({impact:c,draggable:t,droppable:r,draggables:n,afterCritical:d});if(em({draggable:t,destination:r,newPageBorderBoxCenter:p,viewport:i.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))return{clientSelection:eg({pageBorderBoxCenter:p,draggable:t,viewport:i}),impact:c,scrollJumpRequest:null};let f=N(p,a),m=(({impact:e,viewport:t,destination:r,draggables:n,maxScrollChange:l})=>{let i=ep(t,A(t.scroll.current,l)),a=r.frame?T(r,A(r.frame.scroll.current,l)):r,o=e.displaced,d=er({afterDragging:ef(o.all,n),destination:r,displacedBy:e.displacedBy,viewport:i.frame,last:o,forceShouldAnimate:!1}),s=er({afterDragging:ef(o.all,n),destination:a,displacedBy:e.displacedBy,viewport:t.frame,last:o,forceShouldAnimate:!1}),u={},c={},p=[o,d,s];return o.all.forEach(e=>{let t=function(e,t){for(let r=0;r<t.length;r++){let n=t[r].visible[e];if(n)return n}return null}(e,p);if(t){c[e]=t;return}u[e]=!0}),{...e,displaced:{all:o.all,invisible:u,visible:c}}})({impact:c,viewport:i,destination:r,draggables:n,maxScrollChange:f});return{clientSelection:o,impact:m,scrollJumpRequest:f}})({isMovingForward:d,previousPageBorderBoxCenter:u,draggable:s,destination:i,draggables:c,viewport:e.viewport,previousClientSelection:e.current.client.selection,previousImpact:e.impact,afterCritical:e.afterCritical}):(({isMovingForward:e,previousPageBorderBoxCenter:t,draggable:r,isOver:n,draggables:l,droppables:i,viewport:a,afterCritical:o})=>{let d=(({isMovingForward:e,pageBorderBoxCenter:t,source:r,droppables:n,viewport:l})=>{let i=r.subject.active;if(!i)return null;let a=r.axis,o=J(i[a.start],i[a.end]),d=k(n).filter(e=>e!==r).filter(e=>e.isEnabled).filter(e=>!!e.subject.active).filter(e=>X(l.frame)(eb(e))).filter(t=>{let r=eb(t);return e?i[a.crossAxisEnd]<r[a.crossAxisEnd]:r[a.crossAxisStart]<i[a.crossAxisStart]}).filter(e=>{let t=eb(e),r=J(t[a.start],t[a.end]);return o(t[a.start])||o(t[a.end])||r(i[a.start])||r(i[a.end])}).sort((t,r)=>{let n=eb(t)[a.crossAxisStart],l=eb(r)[a.crossAxisStart];return e?n-l:l-n}).filter((e,t,r)=>eb(e)[a.crossAxisStart]===eb(r[0])[a.crossAxisStart]);if(!d.length)return null;if(1===d.length)return d[0];let s=d.filter(e=>J(eb(e)[a.start],eb(e)[a.end])(t[a.line]));return 1===s.length?s[0]:s.length>1?s.sort((e,t)=>eb(e)[a.start]-eb(t)[a.start])[0]:d.sort((e,r)=>{let n=O(t,G(eb(e))),l=O(t,G(eb(r)));return n!==l?n-l:eb(e)[a.start]-eb(r)[a.start]})[0]})({isMovingForward:e,pageBorderBoxCenter:t,source:n,droppables:i,viewport:a});if(!d)return null;let s=W(d.descriptor.id,l),u=(({pageBorderBoxCenter:e,viewport:t,destination:r,insideDestination:n,afterCritical:l})=>n.filter(e=>et({target:((e,t)=>{let r=e.page.borderBox;return ei(e.descriptor.id,t)?B(r,C(t.displacedBy.point)):r})(e,l),destination:r,viewport:t.frame,withDroppableDisplacement:!0})).sort((t,n)=>{let i=w(e,eu(r,eh(t,l))),a=w(e,eu(r,eh(n,l)));return i<a?-1:a<i?1:t.descriptor.index-n.descriptor.index})[0]||null)({pageBorderBoxCenter:t,viewport:a,destination:d,insideDestination:s,afterCritical:o}),c=(({previousPageBorderBoxCenter:e,moveRelativeTo:t,insideDestination:r,draggable:n,draggables:l,destination:i,viewport:a,afterCritical:o})=>{if(!t){if(r.length)return null;let e={displaced:q,displacedBy:z,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:0}}},t=ec({impact:e,draggable:n,droppable:i,draggables:l,afterCritical:o}),d=j(n,i)?i:ev(i,n,l);return em({draggable:n,destination:d,newPageBorderBoxCenter:t,viewport:a.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?e:null}let d=e[i.axis.line]<=t.page.borderBox.center[i.axis.line],s=(()=>{let e=t.descriptor.index;return t.descriptor.id===n.descriptor.id||d?e:e+1})(),u=ey(i.axis,n.displaceBy);return el({draggable:n,insideDestination:r,destination:i,viewport:a,displacedBy:u,last:q,index:s})})({previousPageBorderBoxCenter:t,destination:d,draggable:r,draggables:l,moveRelativeTo:u,insideDestination:s,viewport:a,afterCritical:o});return c?{clientSelection:eg({pageBorderBoxCenter:ec({impact:c,draggable:r,droppable:d,draggables:l,afterCritical:o}),draggable:r,viewport:a}),impact:c,scrollJumpRequest:null}:null})({isMovingForward:d,previousPageBorderBoxCenter:u,draggable:s,isOver:i,draggables:c,droppables:p,viewport:e.viewport,afterCritical:e.afterCritical})})({state:e,type:t.type});return r?eP({state:e,impact:r.impact,clientSelection:r.clientSelection,scrollJumpRequest:r.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){let r=t.payload.reason;return"COLLECTING"!==e.phase&&g(),{...e,phase:"DROP_PENDING",isWaiting:!0,reason:r}}if("DROP_ANIMATE"===t.type){let{completed:r,dropDuration:n,newHomeClientOffset:l}=t.payload;return"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&g(),{phase:"DROP_ANIMATING",completed:r,dropDuration:n,newHomeClientOffset:l,dimensions:e.dimensions}}if("DROP_COMPLETE"===t.type){let{completed:e}=t.payload;return{phase:"IDLE",completed:e,shouldFlush:!1}}return e};function ek(e,t){return e instanceof Object&&"type"in e&&e.type===t}let e$=e=>({type:"PUBLISH_WHILE_DRAGGING",payload:e}),eW=()=>({type:"COLLECTION_STARTING",payload:null}),eU=e=>({type:"UPDATE_DROPPABLE_SCROLL",payload:e}),eH=e=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}),eV=e=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}),ej=e=>({type:"MOVE",payload:e}),ez=()=>({type:"MOVE_UP",payload:null}),eq=()=>({type:"MOVE_DOWN",payload:null}),eY=()=>({type:"MOVE_RIGHT",payload:null}),eJ=()=>({type:"MOVE_LEFT",payload:null}),eX=()=>({type:"FLUSH",payload:null}),eK=e=>({type:"DROP_COMPLETE",payload:e}),eQ=e=>({type:"DROP",payload:e}),eZ=()=>({type:"DROP_ANIMATION_FINISHED",payload:null}),e0={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},e1={opacity:{drop:0,combining:.7},scale:{drop:.75}},e2={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},e3=`${e2.outOfTheWay}s ${e0.outOfTheWay}`,e5={fluid:`opacity ${e3}`,snap:`transform ${e3}, opacity ${e3}`,drop:e=>{let t=`${e}s ${e0.drop}`;return`transform ${t}, opacity ${t}`},outOfTheWay:`transform ${e3}`,placeholder:`height ${e3}, width ${e3}, margin ${e3}`},e7=e=>R(e,E)?void 0:`translate(${e.x}px, ${e.y}px)`,e4={moveTo:e7,drop:(e,t)=>{let r=e7(e);if(r)return t?`${r} scale(${e1.scale.drop})`:r}},{minDropTime:e9,maxDropTime:e8}=e2,e6=e8-e9,te=({getState:e,dispatch:t})=>r=>n=>{if(!ek(n,"DROP"))return void r(n);let l=e(),i=n.payload.reason;if("COLLECTING"===l.phase)return void t({type:"DROP_PENDING",payload:{reason:i}});if("IDLE"===l.phase)return;"DROP_PENDING"===l.phase&&l.isWaiting&&g(),"DRAGGING"!==l.phase&&"DROP_PENDING"!==l.phase&&g();let a=l.critical,o=l.dimensions,d=o.draggables[l.critical.draggable.id],{impact:s,didDropInsideDroppable:u}=(({draggables:e,reason:t,lastImpact:r,home:n,viewport:l,onLiftImpact:i})=>r.at&&"DROP"===t?"REORDER"===r.at.type?{impact:r,didDropInsideDroppable:!0}:{impact:{...r,displaced:q},didDropInsideDroppable:!0}:{impact:ew({draggables:e,impact:i,destination:n,viewport:l,forceShouldAnimate:!0}),didDropInsideDroppable:!1})({reason:i,lastImpact:l.impact,afterCritical:l.afterCritical,onLiftImpact:l.onLiftImpact,home:l.dimensions.droppables[l.critical.droppable.id],viewport:l.viewport,draggables:l.dimensions.draggables}),c=u?U(s):null,p=u?H(s):null,f={index:a.draggable.index,droppableId:a.droppable.id},m={draggableId:d.descriptor.id,type:d.descriptor.type,source:f,reason:i,mode:l.movementMode,destination:c,combine:p},b=(({impact:e,draggable:t,dimensions:r,viewport:n,afterCritical:l})=>{let{draggables:i,droppables:a}=r,o=ex(e),d=o?a[o]:null,s=a[t.descriptor.droppableId];return N(eO({impact:e,draggable:t,draggables:i,afterCritical:l,droppable:d||s,viewport:n}),t.client.borderBox.center)})({impact:s,draggable:d,dimensions:o,viewport:l.viewport,afterCritical:l.afterCritical}),h={critical:l.critical,afterCritical:l.afterCritical,result:m,impact:s};if(!(!R(l.current.client.offset,b)||m.combine))return void t(eK({completed:h}));let y=(({current:e,destination:t,reason:r})=>{let n=w(e,t);if(n<=0)return e9;if(n>=1500)return e8;let l=e9+n/1500*e6;return Number(("CANCEL"===r?.6*l:l).toFixed(2))})({current:l.current.client.offset,destination:b,reason:i});t({type:"DROP_ANIMATE",payload:{newHomeClientOffset:b,dropDuration:y,completed:h}})};var tt=()=>({x:window.pageXOffset,y:window.pageYOffset});let tr=e=>{let t=function({onWindowScroll:e}){let t=(0,d.A)(function(){e(tt())}),r={eventName:"scroll",options:{passive:!0,capture:!1},fn:e=>{(e.target===window||e.target===window.document)&&t()}},n=c;function l(){return n!==c}return{start:function(){l()&&g(),n=p(window,[r])},stop:function(){l()||g(),t.cancel(),n(),n=c},isActive:l}}({onWindowScroll:t=>{e.dispatch({type:"MOVE_BY_WINDOW_SCROLL",payload:{newScroll:t}})}});return e=>r=>{!t.isActive()&&ek(r,"INITIAL_PUBLISH")&&t.start(),t.isActive()&&(e=>ek(e,"DROP_COMPLETE")||ek(e,"DROP_ANIMATE")||ek(e,"FLUSH"))(r)&&t.stop(),e(r)}},tn=(e,t)=>{eG(),t(),eL()},tl=(e,t)=>({draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t});function ti(e,t,r,n){if(!e)return void r(n(t));let l=(e=>{let t=!1,r=!1,n=setTimeout(()=>{r=!0}),l=l=>{!t&&(r||(t=!0,e(l),clearTimeout(n)))};return l.wasCalled=()=>t,l})(r);e(t,{announce:l}),l.wasCalled()||r(n(t))}let ta=e=>t=>r=>{if(!ek(r,"DROP_ANIMATION_FINISHED"))return void t(r);let n=e.getState();"DROP_ANIMATING"!==n.phase&&g(),e.dispatch(eK({completed:n.completed}))},to=e=>{let t=null,r=null;return n=>l=>{if((ek(l,"FLUSH")||ek(l,"DROP_COMPLETE")||ek(l,"DROP_ANIMATION_FINISHED"))&&(r&&(cancelAnimationFrame(r),r=null),t&&(t(),t=null)),n(l),!ek(l,"DROP_ANIMATE"))return;let i={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch(eZ())}};r=requestAnimationFrame(()=>{r=null,t=p(window,[i])})}},td=e=>t=>r=>{if(t(r),!ek(r,"PUBLISH_WHILE_DRAGGING"))return;let n=e.getState();"DROP_PENDING"===n.phase&&(n.isWaiting||e.dispatch(eQ({reason:n.reason})))},ts=i.Zz,tu=()=>({additions:{},removals:{},modified:{}});var tc=({scrollHeight:e,scrollWidth:t,height:r,width:n})=>{let l=N({x:t,y:e},{x:n,y:r});return{x:Math.max(0,l.x),y:Math.max(0,l.y)}},tp=()=>{let e=document.documentElement;return e||g(),e},tf=()=>{let e=tp();return tc({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})};function tg(e,t,r){return r.descriptor.id!==t.id&&r.descriptor.type===t.type&&"virtual"===e.droppable.getById(r.descriptor.droppableId).descriptor.mode}var tm=(e,t)=>"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason,tb=e=>{window.scrollBy(e.x,e.y)};let th=_(e=>k(e).filter(e=>!!e.isEnabled&&!!e.frame)),ty={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:e=>e**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var tI=({startOfRange:e,endOfRange:t,current:r})=>{let n=t-e;return 0===n?0:(r-e)/n},tv=({distanceToEdge:e,thresholds:t,dragStartTime:r,shouldUseTimeDampening:n,getAutoScrollerOptions:l})=>{let i=((e,t,r=()=>ty)=>{let n=r();if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return n.maxPixelScroll;if(e===t.startScrollingFrom)return 1;let l=tI({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e});return Math.ceil(n.maxPixelScroll*n.ease(1-l))})(e,t,l);return 0===i?0:n?Math.max(((e,t,r)=>{let n=r(),l=n.durationDampening.accelerateAt,i=n.durationDampening.stopDampeningAt,a=Date.now()-t;if(a>=i)return e;if(a<l)return 1;let o=tI({startOfRange:l,endOfRange:i,current:a});return Math.ceil(e*n.ease(o))})(i,r,l),1):i},tx=({container:e,distanceToEdges:t,dragStartTime:r,axis:n,shouldUseTimeDampening:l,getAutoScrollerOptions:i})=>{let a=((e,t,r=()=>ty)=>{let n=r(),l=e[t.size]*n.startFromPercentage;return{startScrollingFrom:l,maxScrollValueAt:e[t.size]*n.maxScrollAtPercentage}})(e,n,i);return t[n.end]<t[n.start]?tv({distanceToEdge:t[n.end],thresholds:a,dragStartTime:r,shouldUseTimeDampening:l,getAutoScrollerOptions:i}):-1*tv({distanceToEdge:t[n.start],thresholds:a,dragStartTime:r,shouldUseTimeDampening:l,getAutoScrollerOptions:i})};let tD=S(e=>0===e?0:e);var tE=({dragStartTime:e,container:t,subject:r,center:n,shouldUseTimeDampening:l,getAutoScrollerOptions:i})=>{let a={top:n.y-t.top,right:t.right-n.x,bottom:t.bottom-n.y,left:n.x-t.left},o=tx({container:t,distanceToEdges:a,dragStartTime:e,axis:Q,shouldUseTimeDampening:l,getAutoScrollerOptions:i}),d=tD({x:tx({container:t,distanceToEdges:a,dragStartTime:e,axis:Z,shouldUseTimeDampening:l,getAutoScrollerOptions:i}),y:o});if(R(d,E))return null;let s=(({container:e,subject:t,proposedScroll:r})=>{let n=t.height>e.height,l=t.width>e.width;return l||n?l&&n?null:{x:l?0:r.x,y:n?0:r.y}:r})({container:t,subject:r,proposedScroll:d});return s?R(s,E)?null:s:null};let tA=S(e=>0===e?0:e>0?1:-1),tN=(()=>{let e=(e,t)=>e<0?e:e>t?e-t:0;return({current:t,max:r,change:n})=>{let l=A(t,n),i={x:e(l.x,r.x),y:e(l.y,r.y)};return R(i,E)?null:i}})(),tR=({max:e,current:t,change:r})=>{let n={x:Math.max(t.x,e.x),y:Math.max(t.y,e.y)},l=tA(r),i=tN({max:n,current:t,change:l});return!i||0!==l.x&&0===i.x||0!==l.y&&0===i.y},tC=(e,t)=>tR({current:e.scroll.current,max:e.scroll.max,change:t}),tP=(e,t)=>{let r=e.frame;return!!r&&tR({current:r.scroll.current,max:r.scroll.max,change:t})};var tw=({state:e,dragStartTime:t,shouldUseTimeDampening:r,scrollWindow:n,scrollDroppable:l,getAutoScrollerOptions:i})=>{let a=e.current.page.borderBoxCenter,o=e.dimensions.draggables[e.critical.draggable.id].page.marginBox;if(e.isWindowScrollAllowed){let l=(({viewport:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:l,getAutoScrollerOptions:i})=>{let a=tE({dragStartTime:n,container:e.frame,subject:t,center:r,shouldUseTimeDampening:l,getAutoScrollerOptions:i});return a&&tC(e,a)?a:null})({dragStartTime:t,viewport:e.viewport,subject:o,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});if(l)return void n(l)}let d=(({center:e,destination:t,droppables:r})=>{if(t){let e=r[t];return e.frame?e:null}return th(r).find(t=>(t.frame||g(),eE(t.frame.pageMarginBox)(e)))||null})({center:a,destination:ex(e.impact),droppables:e.dimensions.droppables});if(!d)return;let s=(({droppable:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:l,getAutoScrollerOptions:i})=>{let a=e.frame;if(!a)return null;let o=tE({dragStartTime:n,container:a.pageMarginBox,subject:t,center:r,shouldUseTimeDampening:l,getAutoScrollerOptions:i});return o&&tP(e,o)?o:null})({dragStartTime:t,droppable:d,subject:o,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});s&&l(d.descriptor.id,s)};let tO="data-rfd",tS=(()=>{let e=`${tO}-drag-handle`;return{base:e,draggableId:`${e}-draggable-id`,contextId:`${e}-context-id`}})(),tB=(()=>{let e=`${tO}-draggable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),tG=(()=>{let e=`${tO}-droppable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),tL={contextId:`${tO}-scroll-container-context-id`},tT=(e,t)=>e.map(e=>{let r=e.styles[t];return r?`${e.selector} { ${r} }`:""}).join(" "),t_="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?n.useLayoutEffect:n.useEffect,tM=()=>{let e=document.querySelector("head");return e||g(),e},tF=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function tk(e,t){return Array.from(e.querySelectorAll(t))}var t$=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window;function tW(e){return e instanceof t$(e).HTMLElement}function tU(e,t){let r=tk(document,`[${tS.contextId}="${e}"]`);if(!r.length)return null;let n=r.find(e=>e.getAttribute(tS.draggableId)===t);return n&&tW(n)?n:null}function tH(){let e={draggables:{},droppables:{}},t=[];function r(e){t.length&&t.forEach(t=>t(e))}function n(t){return e.draggables[t]||null}function l(t){return e.droppables[t]||null}return{draggable:{register:t=>{e.draggables[t.descriptor.id]=t,r({type:"ADDITION",value:t})},update:(t,r)=>{let n=e.draggables[r.descriptor.id];n&&n.uniqueId===t.uniqueId&&(delete e.draggables[r.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:t=>{let l=t.descriptor.id,i=n(l);i&&t.uniqueId===i.uniqueId&&(delete e.draggables[l],e.droppables[t.descriptor.droppableId]&&r({type:"REMOVAL",value:t}))},getById:function(e){let t=n(e);return t||g(),t},findById:n,exists:e=>!!n(e),getAllByType:t=>Object.values(e.draggables).filter(e=>e.descriptor.type===t)},droppable:{register:t=>{e.droppables[t.descriptor.id]=t},unregister:t=>{let r=l(t.descriptor.id);r&&t.uniqueId===r.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){let t=l(e);return t||g(),t},findById:l,exists:e=>!!l(e),getAllByType:t=>Object.values(e.droppables).filter(e=>e.descriptor.type===t)},subscribe:function(e){return t.push(e),function(){let r=t.indexOf(e);-1!==r&&t.splice(r,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var tV=n.createContext(null),tj=()=>{let e=document.body;return e||g(),e};let tz={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},tq={separator:"::"};function tY(e,t=tq){let r=n.useId();return x(()=>`${e}${t.separator}${r}`,[t.separator,e,r])}var tJ=n.createContext(null),tX={react:"^18.0.0 || ^19.0.0"};let tK=/(\d+)\.(\d+)\.(\d+)/,tQ=e=>{let t=tK.exec(e);null==t&&g();let r=Number(t[1]);return{major:r,minor:Number(t[2]),patch:Number(t[3]),raw:e}};function tZ(e,t){}function t0(e){let t=(0,n.useRef)(e);return(0,n.useEffect)(()=>{t.current=e}),t}function t1(e){return"IDLE"!==e.phase&&"DROP_ANIMATING"!==e.phase&&e.isDragging}let t2={13:!0,9:!0};var t3=e=>{t2[e.keyCode]&&e.preventDefault()};let t5=(()=>{let e="visibilitychange";return"undefined"==typeof document?e:[e,`ms${e}`,`webkit${e}`,`moz${e}`,`o${e}`].find(e=>`on${e}`in document)||e})(),t7={type:"IDLE"};function t4(){}let t9={34:!0,33:!0,36:!0,35:!0},t8={type:"IDLE"},t6=["input","button","textarea","select","option","optgroup","video","audio"],re=(()=>{let e="matches";return"undefined"==typeof document?e:[e,"msMatchesSelector","webkitMatchesSelector"].find(e=>e in Element.prototype)||e})();function rt(e){e.preventDefault()}function rr({expected:e,phase:t,isLockActive:r,shouldWarn:n}){return!!r()&&e===t}function rn({lockAPI:e,store:t,registry:r,draggableId:n}){if(e.isClaimed())return!1;let l=r.draggable.findById(n);return!!l&&!!l.options.isEnabled&&!!tm(t.getState(),n)}let rl=[function(e){let t=(0,n.useRef)(t7),r=(0,n.useRef)(c),l=x(()=>({eventName:"mousedown",fn:function(t){if(t.defaultPrevented||0!==t.button||t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)return;let n=e.findClosestDraggableId(t);if(!n)return;let l=e.tryGetLock(n,o,{sourceEvent:t});if(!l)return;t.preventDefault();let i={x:t.clientX,y:t.clientY};r.current(),u(l,i)}}),[e]),i=x(()=>({eventName:"webkitmouseforcewillbegin",fn:t=>{if(t.defaultPrevented)return;let r=e.findClosestDraggableId(t);if(!r)return;let n=e.findOptionsForDraggable(r);n&&!n.shouldRespectForcePress&&e.canGetLock(r)&&t.preventDefault()}}),[e]),a=D(function(){r.current=p(window,[i,l],{passive:!1,capture:!0})},[i,l]),o=D(()=>{"IDLE"!==t.current.type&&(t.current=t7,r.current(),a())},[a]),d=D(()=>{let e=t.current;o(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[o]),s=D(function(){r.current=p(window,function({cancel:e,completed:t,getPhase:r,setPhase:n}){return[{eventName:"mousemove",fn:e=>{var t;let{button:l,clientX:i,clientY:a}=e;if(0!==l)return;let o={x:i,y:a},d=r();if("DRAGGING"===d.type){e.preventDefault(),d.actions.move(o);return}"PENDING"!==d.type&&g(),t=d.point,(Math.abs(o.x-t.x)>=5||Math.abs(o.y-t.y)>=5)&&(e.preventDefault(),n({type:"DRAGGING",actions:d.actions.fluidLift(o)}))}},{eventName:"mouseup",fn:n=>{let l=r();if("DRAGGING"!==l.type)return void e();n.preventDefault(),l.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"mousedown",fn:t=>{"DRAGGING"===r().type&&t.preventDefault(),e()}},{eventName:"keydown",fn:t=>{if("PENDING"===r().type)return void e();if(27===t.keyCode){t.preventDefault(),e();return}t3(t)}},{eventName:"resize",fn:e},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{"PENDING"===r().type&&e()}},{eventName:"webkitmouseforcedown",fn:t=>{let n=r();if("IDLE"===n.type&&g(),n.actions.shouldRespectForcePress())return void e();t.preventDefault()}},{eventName:t5,fn:e}]}({cancel:d,completed:o,getPhase:()=>t.current,setPhase:e=>{t.current=e}}),{capture:!0,passive:!1})},[d,o]),u=D(function(e,r){"IDLE"!==t.current.type&&g(),t.current={type:"PENDING",point:r,actions:e},s()},[s]);t_(function(){return a(),function(){r.current()}},[a])},function(e){let t=(0,n.useRef)(t4),r=x(()=>({eventName:"keydown",fn:function(r){if(r.defaultPrevented||32!==r.keyCode)return;let n=e.findClosestDraggableId(r);if(!n)return;let i=e.tryGetLock(n,d,{sourceEvent:r});if(!i)return;r.preventDefault();let a=!0,o=i.snapLift();function d(){a||g(),a=!1,t.current(),l()}t.current(),t.current=p(window,function(e,t){function r(){t(),e.cancel()}return[{eventName:"keydown",fn:n=>{if(27===n.keyCode){n.preventDefault(),r();return}if(32===n.keyCode){n.preventDefault(),t(),e.drop();return}if(40===n.keyCode){n.preventDefault(),e.moveDown();return}if(38===n.keyCode){n.preventDefault(),e.moveUp();return}if(39===n.keyCode){n.preventDefault(),e.moveRight();return}if(37===n.keyCode){n.preventDefault(),e.moveLeft();return}if(t9[n.keyCode])return void n.preventDefault();t3(n)}},{eventName:"mousedown",fn:r},{eventName:"mouseup",fn:r},{eventName:"click",fn:r},{eventName:"touchstart",fn:r},{eventName:"resize",fn:r},{eventName:"wheel",fn:r,options:{passive:!0}},{eventName:t5,fn:r}]}(o,d),{capture:!0,passive:!1})}}),[e]),l=D(function(){t.current=p(window,[r],{passive:!1,capture:!0})},[r]);t_(function(){return l(),function(){t.current()}},[l])},function(e){let t=(0,n.useRef)(t8),r=(0,n.useRef)(c),l=D(function(){return t.current},[]),i=D(function(e){t.current=e},[]),a=x(()=>({eventName:"touchstart",fn:function(t){if(t.defaultPrevented)return;let n=e.findClosestDraggableId(t);if(!n)return;let l=e.tryGetLock(n,d,{sourceEvent:t});if(!l)return;let{clientX:i,clientY:a}=t.touches[0];r.current(),m(l,{x:i,y:a})}}),[e]),o=D(function(){r.current=p(window,[a],{capture:!0,passive:!1})},[a]),d=D(()=>{let e=t.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),i(t8),r.current(),o())},[o,i]),s=D(()=>{let e=t.current;d(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[d]),u=D(function(){let e={capture:!0,passive:!1},t={cancel:s,completed:d,getPhase:l},n=p(window,function({cancel:e,completed:t,getPhase:r}){return[{eventName:"touchmove",options:{capture:!1},fn:t=>{let n=r();if("DRAGGING"!==n.type)return void e();n.hasMoved=!0;let{clientX:l,clientY:i}=t.touches[0];t.preventDefault(),n.actions.move({x:l,y:i})}},{eventName:"touchend",fn:n=>{let l=r();if("DRAGGING"!==l.type)return void e();n.preventDefault(),l.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"touchcancel",fn:t=>{if("DRAGGING"!==r().type)return void e();t.preventDefault(),e()}},{eventName:"touchforcechange",fn:t=>{let n=r();"IDLE"===n.type&&g();let l=t.touches[0];if(!l||!(l.force>=.15))return;let i=n.actions.shouldRespectForcePress();if("PENDING"===n.type){i&&e();return}if(i)return n.hasMoved?void t.preventDefault():void e();t.preventDefault()}},{eventName:t5,fn:e}]}(t),e),i=p(window,function({cancel:e,getPhase:t}){return[{eventName:"orientationchange",fn:e},{eventName:"resize",fn:e},{eventName:"contextmenu",fn:e=>{e.preventDefault()}},{eventName:"keydown",fn:r=>{if("DRAGGING"!==t().type)return void e();27===r.keyCode&&r.preventDefault(),e()}},{eventName:t5,fn:e}]}(t),e);r.current=function(){n(),i()}},[s,l,d]),f=D(function(){let e=l();"PENDING"!==e.type&&g(),i({type:"DRAGGING",actions:e.actions.fluidLift(e.point),hasMoved:!1})},[l,i]),m=D(function(e,t){"IDLE"!==l().type&&g(),i({type:"PENDING",point:t,actions:e,longPressTimerId:setTimeout(f,120)}),u()},[u,l,i,f]);t_(function(){return o(),function(){r.current();let e=l();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),i(t8))}},[l,o,i]),t_(function(){return p(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])},[])}];function ri(e){return e.current||g(),e.current}function ra(e){let{contextId:t,setCallbacks:r,sensors:u,nonce:f,dragHandleUsageInstructions:m}=e,b=(0,n.useRef)(null);tZ(()=>{((e,t)=>{let r,n;if(r=tQ(e),(n=tQ(t)).major>r.major||!(n.major<r.major)&&(n.minor>r.minor||!(n.minor<r.minor)&&n.patch>=r.patch))return})(tX.react,n.version);let e=document.doctype;e&&(e.name.toLowerCase(),e.publicId)},[]);let h=t0(e),y=D(()=>{let e;return{onBeforeCapture:t=>{(0,l.flushSync)(()=>{e.onBeforeCapture&&e.onBeforeCapture(t)})},onBeforeDragStart:(e=h.current).onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}},[h]),v=D(()=>{let e;return e=h.current,{...ty,...e.autoScrollerOptions,durationDampening:{...ty.durationDampening,...e.autoScrollerOptions}}},[h]),R=function(e){let t=x(()=>`rfd-announcement-${e}`,[e]),r=(0,n.useRef)(null);return(0,n.useEffect)(function(){let e=document.createElement("div");return r.current=e,e.id=t,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),(0,s.A)(e.style,tz),tj().appendChild(e),function(){setTimeout(function(){let t=tj();t.contains(e)&&t.removeChild(e),e===r.current&&(r.current=null)})}},[t]),D(e=>{let t=r.current;if(t){t.textContent=e;return}},[])}(t),C=function({contextId:e,text:t}){let r=tY("hidden-text",{separator:"-"}),l=x(()=>(function({contextId:e,uniqueId:t}){return`rfd-hidden-text-${e}-${t}`})({contextId:e,uniqueId:r}),[r,e]);return(0,n.useEffect)(function(){let e=document.createElement("div");return e.id=l,e.textContent=t,e.style.display="none",tj().appendChild(e),function(){let t=tj();t.contains(e)&&t.removeChild(e)}},[l,t]),l}({contextId:t,text:m}),P=function(e,t){let r=x(()=>(e=>{let t=t=>`[${t}="${e}"]`,r=(()=>{let e=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:t(tS.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:e,dragging:"pointer-events: none;",dropAnimating:e}}})(),n=(()=>{let e=`
      transition: ${e5.outOfTheWay};
    `;return{selector:t(tB.contextId),styles:{dragging:e,dropAnimating:e,userCancel:e}}})(),l=[n,r,{selector:t(tG.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}}];return{always:tT(l,"always"),resting:tT(l,"resting"),dragging:tT(l,"dragging"),dropAnimating:tT(l,"dropAnimating"),userCancel:tT(l,"userCancel")}})(e),[e]),l=(0,n.useRef)(null),i=(0,n.useRef)(null),a=D(_(e=>{let t=i.current;t||g(),t.textContent=e}),[]),o=D(e=>{let t=l.current;t||g(),t.textContent=e},[]);t_(()=>{(l.current||i.current)&&g();let n=tF(t),d=tF(t);return l.current=n,i.current=d,n.setAttribute(`${tO}-always`,e),d.setAttribute(`${tO}-dynamic`,e),tM().appendChild(n),tM().appendChild(d),o(r.always),a(r.resting),()=>{let e=e=>{let t=e.current;t||g(),tM().removeChild(t),e.current=null};e(l),e(i)}},[t,o,a,r.always,r.resting,e]);let d=D(()=>a(r.dragging),[a,r.dragging]),s=D(e=>{if("DROP"===e)return void a(r.dropAnimating);a(r.userCancel)},[a,r.dropAnimating,r.userCancel]),u=D(()=>{i.current&&a(r.resting)},[a,r.resting]);return x(()=>({dragging:d,dropping:s,resting:u}),[d,s,u])}(t,f),w=D(e=>{ri(b).dispatch(e)},[]),O=x(()=>(0,i.zH)({publishWhileDragging:e$,updateDroppableScroll:eU,updateDroppableIsEnabled:eH,updateDroppableIsCombineEnabled:eV,collectionStarting:eW},w),[w]),S=function(){let e=x(tH,[]);return(0,n.useEffect)(()=>function(){e.clean()},[e]),e}(),B=x(()=>((e,t)=>{let r=null,n=function({registry:e,callbacks:t}){let r=tu(),n=null,l=()=>{n||(t.collectionStarting(),n=requestAnimationFrame(()=>{n=null,eG();let{additions:l,removals:i,modified:a}=r,o=Object.keys(l).map(t=>e.draggable.getById(t).getDimension(E)).sort((e,t)=>e.descriptor.index-t.descriptor.index),d=Object.keys(a).map(t=>{let r=e.droppable.getById(t).callbacks.getScrollWhileDragging();return{droppableId:t,scroll:r}}),s={additions:o,removals:Object.keys(i),modified:d};r=tu(),eL(),t.publish(s)}))};return{add:e=>{let t=e.descriptor.id;r.additions[t]=e,r.modified[e.descriptor.droppableId]=!0,r.removals[t]&&delete r.removals[t],l()},remove:e=>{let t=e.descriptor;r.removals[t.id]=!0,r.modified[t.droppableId]=!0,r.additions[t.id]&&delete r.additions[t.id],l()},stop:()=>{n&&(cancelAnimationFrame(n),n=null,r=tu())}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),l=t=>{r||g();let l=r.critical.draggable;"ADDITION"===t.type&&tg(e,l,t.value)&&n.add(t.value),"REMOVAL"===t.type&&tg(e,l,t.value)&&n.remove(t.value)};return{updateDroppableIsEnabled:(n,l)=>{e.droppable.exists(n)||g(),r&&t.updateDroppableIsEnabled({id:n,isEnabled:l})},updateDroppableIsCombineEnabled:(n,l)=>{r&&(e.droppable.exists(n)||g(),t.updateDroppableIsCombineEnabled({id:n,isCombineEnabled:l}))},scrollDroppable:(t,n)=>{r&&e.droppable.getById(t).callbacks.scroll(n)},updateDroppableScroll:(n,l)=>{r&&(e.droppable.exists(n)||g(),t.updateDroppableScroll({id:n,newScroll:l}))},startPublishing:t=>{r&&g();let n=e.draggable.getById(t.draggableId),i=e.droppable.getById(n.descriptor.droppableId),a={draggable:n.descriptor,droppable:i.descriptor};return r={critical:a,unsubscribe:e.subscribe(l)},(({critical:e,scrollOptions:t,registry:r})=>{eG();let n=(()=>{let e=tt(),t=tf(),r=e.y,n=e.x,l=tp(),i=l.clientWidth,a=l.clientHeight;return{frame:(0,o.l)({top:r,left:n,right:n+i,bottom:r+a}),scroll:{initial:e,current:e,max:t,diff:{value:E,displacement:E}}}})(),l=n.scroll.current,i=e.droppable,a=r.droppable.getAllByType(i.type).map(e=>e.callbacks.getDimensionAndWatchScroll(l,t)),d={draggables:F(r.draggable.getAllByType(e.draggable.type).map(e=>e.getDimension(l))),droppables:M(a)};return eL(),{dimensions:d,critical:e,viewport:n}})({critical:a,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:()=>{if(!r)return;n.stop();let t=r.critical.droppable;e.droppable.getAllByType(t.type).forEach(e=>e.callbacks.dragStopped()),r.unsubscribe(),r=null}}})(S,O),[S,O]),G=x(()=>(({scrollDroppable:e,scrollWindow:t,move:r,getAutoScrollerOptions:n})=>{let l=(({scrollWindow:e,scrollDroppable:t,getAutoScrollerOptions:r=()=>ty})=>{let n=(0,d.A)(e),l=(0,d.A)(t),i=null,a=e=>{i||g();let{shouldUseTimeDampening:t,dragStartTime:a}=i;tw({state:e,scrollWindow:n,scrollDroppable:l,dragStartTime:a,shouldUseTimeDampening:t,getAutoScrollerOptions:r})};return{start:e=>{eG(),i&&g();let t=Date.now(),n=!1,l=()=>{n=!0};tw({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:l,scrollDroppable:l,getAutoScrollerOptions:r}),i={dragStartTime:t,shouldUseTimeDampening:n},eL(),n&&a(e)},stop:()=>{i&&(n.cancel(),l.cancel(),i=null)},scroll:a}})({scrollWindow:t,scrollDroppable:e,getAutoScrollerOptions:n}),i=(({move:e,scrollDroppable:t,scrollWindow:r})=>n=>{let l=n.scrollJumpRequest;if(!l)return;let i=ex(n.impact);i||g();let a=((e,r)=>{if(!tP(e,r))return r;let n=((e,t)=>{let r=e.frame;return r&&tP(e,t)?tN({current:r.scroll.current,max:r.scroll.max,change:t}):null})(e,r);if(!n)return t(e.descriptor.id,r),null;let l=N(r,n);return t(e.descriptor.id,l),N(r,l)})(n.dimensions.droppables[i],l);if(!a)return;let o=n.viewport,d=((e,t,n)=>{if(!e||!tC(t,n))return n;let l=((e,t)=>{if(!tC(e,t))return null;let r=e.scroll.max;return tN({current:e.scroll.current,max:r,change:t})})(t,n);if(!l)return r(n),null;let i=N(n,l);return r(i),N(n,i)})(n.isWindowScrollAllowed,o,a);d&&e({client:A(n.current.client.selection,d)})})({move:r,scrollWindow:t,scrollDroppable:e});return{scroll:e=>{if(!n().disabled&&"DRAGGING"===e.phase){if("FLUID"===e.movementMode)return void l.scroll(e);e.scrollJumpRequest&&i(e)}},start:l.start,stop:l.stop}})({scrollWindow:tb,scrollDroppable:B.scrollDroppable,getAutoScrollerOptions:v,...(0,i.zH)({move:ej},w)}),[B.scrollDroppable,w,v]),L=function(e){let t=(0,n.useRef)({}),r=(0,n.useRef)(null),l=(0,n.useRef)(null),i=(0,n.useRef)(!1),a=D(function(e,r){let n={id:e,focus:r};return t.current[e]=n,function(){let r=t.current;r[e]!==n&&delete r[e]}},[]),o=D(function(t){let r=tU(e,t);r&&r!==document.activeElement&&r.focus()},[e]),d=D(function(e,t){r.current===e&&(r.current=t)},[]),s=D(function(){!l.current&&i.current&&(l.current=requestAnimationFrame(()=>{l.current=null;let e=r.current;e&&o(e)}))},[o]),u=D(function(e){r.current=null;let t=document.activeElement;t&&t.getAttribute(tS.draggableId)===e&&(r.current=e)},[]);return t_(()=>(i.current=!0,function(){i.current=!1;let e=l.current;e&&cancelAnimationFrame(e)}),[]),x(()=>({register:a,tryRecordFocus:u,tryRestoreFocusRecorded:s,tryShiftRecord:d}),[a,u,s,d])}(t),T=x(()=>(({dimensionMarshal:e,focusMarshal:t,styleMarshal:r,getResponders:n,announce:l,autoScroller:a})=>{var o;let d,s,u;return(0,i.y$)(eF,ts((0,i.Tw)((d=r,()=>e=>t=>{ek(t,"INITIAL_PUBLISH")&&d.dragging(),ek(t,"DROP_ANIMATE")&&d.dropping(t.payload.completed.result.reason),(ek(t,"FLUSH")||ek(t,"DROP_COMPLETE"))&&d.resting(),e(t)}),()=>t=>r=>{(ek(r,"DROP_COMPLETE")||ek(r,"FLUSH")||ek(r,"DROP_ANIMATE"))&&e.stopPublishing(),t(r)},({getState:t,dispatch:r})=>n=>l=>{if(!ek(l,"LIFT"))return void n(l);let{id:i,clientSelection:a,movementMode:o}=l.payload,d=t();"DROP_ANIMATING"===d.phase&&r(eK({completed:d.completed})),"IDLE"!==t().phase&&g(),r(eX()),r({type:"BEFORE_INITIAL_CAPTURE",payload:{draggableId:i,movementMode:o}});let{critical:s,dimensions:u,viewport:c}=e.startPublishing({draggableId:i,scrollOptions:{shouldPublishImmediately:"SNAP"===o}});r({type:"INITIAL_PUBLISH",payload:{critical:s,dimensions:u,clientSelection:a,movementMode:o,viewport:c}})},te,ta,to,td,(s=a,e=>t=>r=>{let n;if(ek(n=r,"DROP_COMPLETE")||ek(n,"DROP_ANIMATE")||ek(n,"FLUSH")){s.stop(),t(r);return}if(ek(r,"INITIAL_PUBLISH")){t(r);let n=e.getState();"DRAGGING"!==n.phase&&g(),s.start(n);return}t(r),s.scroll(e.getState())}),tr,(o=t,u=!1,()=>e=>t=>{if(ek(t,"INITIAL_PUBLISH")){u=!0,o.tryRecordFocus(t.payload.critical.draggable.id),e(t),o.tryRestoreFocusRecorded();return}if(e(t),u){if(ek(t,"FLUSH")){u=!1,o.tryRestoreFocusRecorded();return}if(ek(t,"DROP_COMPLETE")){u=!1;let e=t.payload.completed.result;e.combine&&o.tryShiftRecord(e.draggableId,e.combine.draggableId),o.tryRestoreFocusRecorded()}}}),((e,t)=>{let r=((e,t)=>{let r=(()=>{let e=[];return{add:t=>{let r=setTimeout(()=>(t=>{let r=e.findIndex(e=>e.timerId===t);-1===r&&g();let[n]=e.splice(r,1);n.callback()})(r));e.push({timerId:r,callback:t})},flush:()=>{if(!e.length)return;let t=[...e];e.length=0,t.forEach(e=>{clearTimeout(e.timerId),e.callback()})}}})(),n=null,l=r=>{n||g(),n=null,tn("onDragEnd",()=>ti(e().onDragEnd,r,t,I.onDragEnd))};return{beforeCapture:(t,r)=>{n&&g(),tn("onBeforeCapture",()=>{let n=e().onBeforeCapture;n&&n({draggableId:t,mode:r})})},beforeStart:(t,r)=>{n&&g(),tn("onBeforeDragStart",()=>{let n=e().onBeforeDragStart;n&&n(tl(t,r))})},start:(l,i)=>{n&&g();let a=tl(l,i);n={mode:i,lastCritical:l,lastLocation:a.source,lastCombine:null},r.add(()=>{tn("onDragStart",()=>ti(e().onDragStart,a,t,I.onDragStart))})},update:(l,i)=>{let a,o,d,s,u=U(i),c=H(i);n||g();let p=!((e,t)=>{if(e===t)return!0;let r=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,n=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return r&&n})(l,n.lastCritical);p&&(n.lastCritical=l);let f=(a=n.lastLocation,o=u,(null!=a||null!=o)&&(null==a||null==o||a.droppableId!==o.droppableId||a.index!==o.index));f&&(n.lastLocation=u);let m=(d=n.lastCombine,s=c,(null!=d||null!=s)&&(null==d||null==s||d.draggableId!==s.draggableId||d.droppableId!==s.droppableId));if(m&&(n.lastCombine=c),!p&&!f&&!m)return;let b={...tl(l,n.mode),combine:c,destination:u};r.add(()=>{tn("onDragUpdate",()=>ti(e().onDragUpdate,b,t,I.onDragUpdate))})},flush:()=>{n||g(),r.flush()},drop:l,abort:()=>{n&&l({...tl(n.lastCritical,n.mode),combine:null,destination:null,reason:"CANCEL"})}}})(e,t);return e=>t=>n=>{if(ek(n,"BEFORE_INITIAL_CAPTURE"))return void r.beforeCapture(n.payload.draggableId,n.payload.movementMode);if(ek(n,"INITIAL_PUBLISH")){let e=n.payload.critical;r.beforeStart(e,n.payload.movementMode),t(n),r.start(e,n.payload.movementMode);return}if(ek(n,"DROP_COMPLETE")){let e=n.payload.completed.result;r.flush(),t(n),r.drop(e);return}if(t(n),ek(n,"FLUSH"))return void r.abort();let l=e.getState();"DRAGGING"===l.phase&&r.update(l.critical,l.impact)}})(n,l))))})({announce:R,autoScroller:G,dimensionMarshal:B,focusMarshal:L,getResponders:y,styleMarshal:P}),[R,G,B,L,y,P]);b.current=T;let k=D(()=>{let e=ri(b);"IDLE"!==e.getState().phase&&e.dispatch(eX())},[]),$=D(()=>{let e=ri(b).getState();return"DROP_ANIMATING"===e.phase||"IDLE"!==e.phase&&e.isDragging},[]);r(x(()=>({isDragging:$,tryAbort:k}),[$,k]));let W=D(e=>tm(ri(b).getState(),e),[]),V=D(()=>eD(ri(b).getState()),[]),j=x(()=>({marshal:B,focus:L,contextId:t,canLift:W,isMovementAllowed:V,dragHandleUsageInstructionsId:C,registry:S}),[t,B,C,L,W,V,S]);return!function({contextId:e,store:t,registry:r,customSensors:l,enableDefaultSensors:i}){let a=[...i?rl:[],...l||[]],s=(0,n.useState)(()=>(function(){let e=null;function t(){e||g(),e=null}return{isClaimed:function(){return!!e},isActive:function(t){return t===e},claim:function(t){e&&g();let r={abandon:t};return e=r,r},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}})())[0],u=D(function(e,t){t1(e)&&!t1(t)&&s.tryAbandon()},[s]);t_(function(){let e=t.getState();return t.subscribe(()=>{let r=t.getState();u(e,r),e=r})},[s,t,u]),t_(()=>s.tryAbandon,[s.tryAbandon]);let f=D(e=>rn({lockAPI:s,registry:r,store:t,draggableId:e}),[s,r,t]),m=D((n,l,i)=>(function({lockAPI:e,contextId:t,store:r,registry:n,draggableId:l,forceSensorStop:i,sourceEvent:a}){if(!rn({lockAPI:e,store:r,registry:n,draggableId:l}))return null;let s=n.draggable.getById(l),u=function(e,t){let r=tk(document,`[${tB.contextId}="${e}"]`).find(e=>e.getAttribute(tB.id)===t);return r&&tW(r)?r:null}(t,s.descriptor.id);if(!u||a&&!s.options.canDragInteractiveElements&&function(e,t){let r=t.target;return!!tW(r)&&function e(t,r){if(null==r)return!1;if(t6.includes(r.tagName.toLowerCase()))return!0;let n=r.getAttribute("contenteditable");return"true"===n||""===n||r!==t&&e(t,r.parentElement)}(e,r)}(u,a))return null;let f=e.claim(i||c),m="PRE_DRAG";function b(){return s.options.shouldRespectForcePress}function h(){return e.isActive(f)}let y=(function(e,t){rr({expected:e,phase:m,isLockActive:h,shouldWarn:!0})&&r.dispatch(t())}).bind(null,"DRAGGING");function I(t){function n(){e.release(),m="COMPLETED"}function l(e,i={shouldBlockNextClick:!1}){t.cleanup(),i.shouldBlockNextClick&&setTimeout(p(window,[{eventName:"click",fn:rt,options:{once:!0,passive:!1,capture:!0}}])),n(),r.dispatch(eQ({reason:e}))}return"PRE_DRAG"!==m&&(n(),g()),r.dispatch({type:"LIFT",payload:t.liftActionArgs}),m="DRAGGING",{isActive:()=>rr({expected:"DRAGGING",phase:m,isLockActive:h,shouldWarn:!1}),shouldRespectForcePress:b,drop:e=>l("DROP",e),cancel:e=>l("CANCEL",e),...t.actions}}return{isActive:()=>rr({expected:"PRE_DRAG",phase:m,isLockActive:h,shouldWarn:!1}),shouldRespectForcePress:b,fluidLift:function(e){let t=(0,d.A)(e=>{y(()=>ej({client:e}))});return{...I({liftActionArgs:{id:l,clientSelection:e,movementMode:"FLUID"},cleanup:()=>t.cancel(),actions:{move:t}}),move:t}},snapLift:function(){return I({liftActionArgs:{id:l,clientSelection:(0,o.l)(u.getBoundingClientRect()).center,movementMode:"SNAP"},cleanup:c,actions:{moveUp:()=>y(ez),moveRight:()=>y(eY),moveDown:()=>y(eq),moveLeft:()=>y(eJ)}})},abort:function(){rr({expected:"PRE_DRAG",phase:m,isLockActive:h,shouldWarn:!0})&&e.release()}}})({lockAPI:s,registry:r,contextId:e,store:t,draggableId:n,forceSensorStop:l||null,sourceEvent:i&&i.sourceEvent?i.sourceEvent:null}),[e,s,r,t]),b=D(t=>(function(e,t){let r=function(e,t){let r=t.target;if(!(r instanceof t$(r).Element))return null;let n=`[${tS.contextId}="${e}"]`,l=r.closest?r.closest(n):function e(t,r){return null==t?null:t[re](r)?t:e(t.parentElement,r)}(r,n);return l&&tW(l)?l:null}(e,t);return r?r.getAttribute(tS.draggableId):null})(e,t),[e]),h=D(e=>{let t=r.draggable.findById(e);return t?t.options:null},[r.draggable]),y=D(function(){s.isClaimed()&&(s.tryAbandon(),"IDLE"!==t.getState().phase&&t.dispatch(eX()))},[s,t]),I=D(()=>s.isClaimed(),[s]),v=x(()=>({canGetLock:f,tryGetLock:m,findClosestDraggableId:b,findOptionsForDraggable:h,tryReleaseLock:y,isLockClaimed:I}),[f,m,b,h,y,I]);for(let e=0;e<a.length;e++)a[e](v)}({contextId:t,store:T,registry:S,customSensors:u||null,enableDefaultSensors:!1!==e.enableDefaultSensors}),(0,n.useEffect)(()=>k,[k]),n.createElement(tJ.Provider,{value:j},n.createElement(a.Kq,{context:tV,store:T},e.children))}function ro(e){let t=n.useId(),r=e.dragHandleUsageInstructions||I.dragHandleUsageInstructions;return n.createElement(m,null,l=>n.createElement(ra,{nonce:e.nonce,contextId:t,setCallbacks:l,dragHandleUsageInstructions:r,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd,autoScrollerOptions:e.autoScrollerOptions},e.children))}let rd={dragging:5e3,dropAnimating:4500};var rs=n.createContext(null);function ru(e){e&&tW(e)||g()}function rc(e){let t=(0,n.useContext)(e);return t||g(),t}function rp(e){e.preventDefault()}var rf=(e,t)=>e===t,rg=e=>{let{combine:t,destination:r}=e;return r?r.droppableId:t?t.droppableId:null};function rm(e=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}let rb={mapped:{type:"SECONDARY",offset:E,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:rm(null)}},rh=(0,a.Ng)(()=>{let e=function(){let e=_((e,t)=>({x:e,y:t})),t=_((e,t,r=null,n=null,l=null)=>({isDragging:!0,isClone:t,isDropAnimating:!!l,dropAnimation:l,mode:e,draggingOver:r,combineWith:n,combineTargetFor:null})),r=_((e,r,n,l,i=null,a=null,o=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:i,combineWith:a,mode:r,offset:e,dimension:n,forceShouldAnimate:o,snapshot:t(r,l,i,a,null)}}));return(n,l)=>{if(t1(n)){let t;if(n.critical.draggable.id!==l.draggableId)return null;let i=n.current.client.offset,a=n.dimensions.draggables[l.draggableId],o=ex(n.impact),d=(t=n.impact).at&&"COMBINE"===t.at.type?t.at.combine.draggableId:null,s=n.forceShouldAnimate;return r(e(i.x,i.y),n.movementMode,a,l.isClone,o,d,s)}if("DROP_ANIMATING"===n.phase){let e=n.completed;if(e.result.draggableId!==l.draggableId)return null;let r=l.isClone,i=n.dimensions.draggables[l.draggableId],a=e.result,o=a.mode,d=rg(a),s=a.combine?a.combine.draggableId:null,u={duration:n.dropDuration,curve:e0.drop,moveTo:n.newHomeClientOffset,opacity:s?e1.opacity.drop:null,scale:s?e1.scale.drop:null};return{mapped:{type:"DRAGGING",offset:n.newHomeClientOffset,dimension:i,dropping:u,draggingOver:d,combineWith:s,mode:o,forceShouldAnimate:null,snapshot:t(o,r,d,s,u)}}}return null}}(),t=function(){let e=_((e,t)=>({x:e,y:t})),t=_(rm),r=_((e,r=null,n)=>({mapped:{type:"SECONDARY",offset:e,combineTargetFor:r,shouldAnimateDisplacement:n,snapshot:t(r)}})),n=e=>e?r(E,e,!0):null,l=(t,l,i,a)=>{let o=i.displaced.visible[t],d=!!(a.inVirtualList&&a.effected[t]),s=H(i),u=s&&s.draggableId===t?l:null;if(!o){if(!d)return n(u);if(i.displaced.invisible[t])return null;let l=C(a.displacedBy.point);return r(e(l.x,l.y),u,!0)}if(d)return n(u);let c=i.displacedBy.point;return r(e(c.x,c.y),u,o.shouldAnimate)};return(e,t)=>{if(t1(e))return e.critical.draggable.id===t.draggableId?null:l(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){let r=e.completed;return r.result.draggableId===t.draggableId?null:l(t.draggableId,r.result.draggableId,r.impact,r.afterCritical)}return null}}();return(r,n)=>e(r,n)||t(r,n)||rb},{dropAnimationFinished:eZ},null,{context:tV,areStatePropsEqual:rf})(e=>{let t=(0,n.useRef)(null),r=D((e=null)=>{t.current=e},[]),i=D(()=>t.current,[]),{contextId:a,dragHandleUsageInstructionsId:d,registry:s}=rc(tJ),{type:u,droppableId:c}=rc(rs),p=x(()=>({id:e.draggableId,index:e.index,type:u,droppableId:c}),[e.draggableId,e.index,u,c]),{children:f,draggableId:m,isEnabled:b,shouldRespectForcePress:h,canDragInteractiveElements:y,isClone:I,mapped:v,dropAnimationFinished:A}=e;!function(e,t,r){tZ(()=>{let n=e.draggableId;n||g(!1),"string"!=typeof n&&g(!1),Number.isInteger(e.index)||g(!1),"DRAGGING"!==e.mapped.type&&(ru(r()),e.isEnabled&&(tU(t,n)||g(!1)))})}(e,a,i),I||function(e){let t=tY("draggable"),{descriptor:r,registry:l,getDraggableRef:i,canDragInteractiveElements:a,shouldRespectForcePress:d,isEnabled:s}=e,u=x(()=>({canDragInteractiveElements:a,shouldRespectForcePress:d,isEnabled:s}),[a,s,d]),c=D(e=>{let t=i();return t||g(),function(e,t,r=E){let n=window.getComputedStyle(t),l=t.getBoundingClientRect(),i=(0,o.a)(l,n),a=(0,o.SQ)(i,r),d={client:i,tagName:t.tagName.toLowerCase(),display:n.display};return{descriptor:e,placeholder:d,displaceBy:{x:i.marginBox.width,y:i.marginBox.height},client:i,page:a}}(r,t,e)},[r,i]),p=x(()=>({uniqueId:t,descriptor:r,options:u,getDimension:c}),[r,c,u,t]),f=(0,n.useRef)(p),m=(0,n.useRef)(!0);t_(()=>(l.draggable.register(f.current),()=>l.draggable.unregister(f.current)),[l.draggable]),t_(()=>{if(m.current){m.current=!1;return}let e=f.current;f.current=p,l.draggable.update(p,e)},[p,l.draggable])}(x(()=>({descriptor:p,registry:s,getDraggableRef:i,canDragInteractiveElements:y,shouldRespectForcePress:h,isEnabled:b}),[p,s,i,y,h,b]));let N=x(()=>b?{tabIndex:0,role:"button","aria-describedby":d,"data-rfd-drag-handle-draggable-id":m,"data-rfd-drag-handle-context-id":a,draggable:!1,onDragStart:rp}:null,[a,d,m,b]),R=D(e=>{"DRAGGING"===v.type&&v.dropping&&"transform"===e.propertyName&&(0,l.flushSync)(A)},[A,v]),C=x(()=>{let e=function(e){return"DRAGGING"===e.type?function(e){let t=e.dimension.client,{offset:r,combineWith:n,dropping:l}=e,i=!!n,a=null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode,o=!!l,d=o?e4.drop(r,i):e4.moveTo(r);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:l?e5.drop(l.duration):a?e5.snap:e5.fluid,transform:d,opacity:((e,t)=>{if(e)return t?e1.opacity.drop:e1.opacity.combining})(i,o),zIndex:o?rd.dropAnimating:rd.dragging,pointerEvents:"none"}}(e):{transform:e4.moveTo(e.offset),transition:e.shouldAnimateDisplacement?void 0:"none"}}(v);return{innerRef:r,draggableProps:{"data-rfd-draggable-context-id":a,"data-rfd-draggable-id":m,style:e,onTransitionEnd:"DRAGGING"===v.type&&v.dropping?R:void 0},dragHandleProps:N}},[a,N,m,v,R,r]),P=x(()=>({draggableId:p.id,type:p.type,source:{index:p.index,droppableId:p.droppableId}}),[p.droppableId,p.id,p.index,p.type]);return n.createElement(n.Fragment,null,f(C,v.snapshot,P))});function ry(e){return rc(rs).isUsingCloneFor!==e.draggableId||e.isClone?n.createElement(rh,e):null}function rI(e){let t="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,r=!!e.disableInteractiveElementBlocking,l=!!e.shouldRespectForcePress;return n.createElement(ry,(0,s.A)({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:r,shouldRespectForcePress:l}))}let rv=e=>t=>e===t,rx=rv("scroll"),rD=rv("auto");rv("visible");let rE=(e,t)=>t(e.overflowX)||t(e.overflowY),rA=e=>null==e||e===document.body||e===document.documentElement?null:(e=>{let t=window.getComputedStyle(e),r={overflowX:t.overflowX,overflowY:t.overflowY};return rE(r,rx)||rE(r,rD)})(e)?e:rA(e.parentElement);var rN=e=>({x:e.scrollLeft,y:e.scrollTop});let rR=e=>!!e&&("fixed"===window.getComputedStyle(e).position||rR(e.parentElement)),rC={passive:!1},rP={passive:!0};var rw=e=>e.shouldPublishImmediately?rC:rP;let rO=e=>e&&e.env.closestScrollable||null;function rS(){}let rB={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}};var rG=n.memo(e=>{let t=(0,n.useRef)(null),r=D(()=>{t.current&&(clearTimeout(t.current),t.current=null)},[]),{animate:l,onTransitionEnd:i,onClose:a,contextId:o}=e,[d,s]=(0,n.useState)("open"===e.animate);(0,n.useEffect)(()=>d?"open"!==l?(r(),s(!1),rS):t.current?rS:(t.current=setTimeout(()=>{t.current=null,s(!1)}),r):rS,[l,d,r]);let u=D(e=>{"height"===e.propertyName&&(i(),"close"===l&&a())},[l,a,i]),c=(({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>{let n=(({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>e||"close"===r?rB:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin})({isAnimatingOpenOnMount:e,placeholder:t,animate:r});return{display:t.display,boxSizing:"border-box",width:n.width,height:n.height,marginTop:n.margin.top,marginRight:n.margin.right,marginBottom:n.margin.bottom,marginLeft:n.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==r?e5.placeholder:null}})({isAnimatingOpenOnMount:d,animate:e.animate,placeholder:e.placeholder});return n.createElement(e.placeholder.tagName,{style:c,"data-rfd-placeholder-context-id":o,onTransitionEnd:u,ref:e.innerRef})});function rL(e){return"boolean"==typeof e}function rT(e,t){t.forEach(t=>t(e))}let r_=[function({props:e}){e.droppableId||g(),"string"!=typeof e.droppableId&&g()},function({props:e}){rL(e.isDropDisabled)||g(),rL(e.isCombineEnabled)||g(),rL(e.ignoreContainerClipping)||g()},function({getDroppableRef:e}){ru(e())}],rM=[function({props:e,getPlaceholderRef:t}){if(!e.placeholder||t())return}],rF=[function({props:e}){e.renderClone||g()},function({getPlaceholderRef:e}){e()&&g()}];class rk extends n.PureComponent{constructor(...e){super(...e),this.state={isVisible:!!this.props.on,data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{"close"===this.state.animate&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!e.on,data:e.on,animate:"none"}}render(){if(!this.state.isVisible)return null;let e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)}}let r$={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||g(),document.body}},rW=e=>{let t,r={...e};for(t in r$)void 0===e[t]&&(r={...r,[t]:r$[t]});return r},rU=(e,t)=>t.draggables[e.draggable.id],rH=(0,a.Ng)(()=>{let e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t={...e,shouldAnimatePlaceholder:!1},r=_(e=>({draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}})),n=_((n,l,i,a,o,d)=>{let s=o.descriptor.id;if(o.descriptor.droppableId===n){let e=d?{render:d,dragging:r(o.descriptor)}:null;return{placeholder:o.placeholder,shouldAnimatePlaceholder:!1,snapshot:{isDraggingOver:i,draggingOverWith:i?s:null,draggingFromThisWith:s,isUsingPlaceholder:!0},useClone:e}}return l?a?{placeholder:o.placeholder,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:i,draggingOverWith:s,draggingFromThisWith:null,isUsingPlaceholder:!0},useClone:null}:e:t});return(r,l)=>{let i=rW(l),a=i.droppableId,o=i.type,d=!i.isDropDisabled,s=i.renderClone;if(t1(r)){let e=r.critical;if(o!==e.droppable.type)return t;let l=rU(e,r.dimensions),i=ex(r.impact)===a;return n(a,d,i,i,l,s)}if("DROP_ANIMATING"===r.phase){let e=r.completed;if(o!==e.critical.droppable.type)return t;let l=rU(e.critical,r.dimensions);return n(a,d,rg(e.result)===a,ex(e.impact)===a,l,s)}if("IDLE"===r.phase&&r.completed&&!r.shouldFlush){let n=r.completed;if(o!==n.critical.droppable.type)return t;let l=ex(n.impact)===a,i=!!(n.impact.at&&"COMBINE"===n.impact.at.type),d=n.critical.droppable.id===a;if(l)return i?e:t;if(d)return e}return t}},{updateViewportMaxScroll:e=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e})},(e,t,r)=>({...rW(r),...e,...t}),{context:tV,areStatePropsEqual:rf})(e=>{let t=(0,n.useContext)(tJ);t||g();let{contextId:r,isMovementAllowed:i}=t,a=(0,n.useRef)(null),s=(0,n.useRef)(null),{children:u,droppableId:c,type:p,mode:f,direction:m,ignoreContainerClipping:b,isDropDisabled:h,isCombineEnabled:y,snapshot:I,useClone:v,updateViewportMaxScroll:A,getContainerForClone:N}=e,R=D(()=>a.current,[]),C=D((e=null)=>{a.current=e},[]),P=D(()=>s.current,[]),w=D((e=null)=>{s.current=e},[]);!function(e){tZ(()=>{rT(e,r_),"standard"===e.props.mode&&rT(e,rM),"virtual"===e.props.mode&&rT(e,rF)})}({props:e,getDroppableRef:R,getPlaceholderRef:P});let O=D(()=>{i()&&A({maxScroll:tf()})},[i,A]);!function(e){let t=(0,n.useRef)(null),r=rc(tJ),l=tY("droppable"),{registry:i,marshal:a}=r,s=t0(e),u=x(()=>({id:e.droppableId,type:e.type,mode:e.mode}),[e.droppableId,e.mode,e.type]),c=(0,n.useRef)(u),p=x(()=>_((e,r)=>{t.current||g(),a.updateDroppableScroll(u.id,{x:e,y:r})}),[u.id,a]),f=D(()=>{let e=t.current;return e&&e.env.closestScrollable?rN(e.env.closestScrollable):E},[]),m=D(()=>{let e=f();p(e.x,e.y)},[f,p]),b=x(()=>(0,d.A)(m),[m]),h=D(()=>{let e=t.current,r=rO(e);if(e&&r||g(),e.scrollOptions.shouldPublishImmediately)return void m();b()},[b,m]),y=D((e,n)=>{var l;t.current&&g();let i=s.current,a=i.getDroppableRef();a||g();let d={closestScrollable:rA(l=a),isFixedOnPage:rR(l)},c={ref:a,descriptor:u,env:d,scrollOptions:n};t.current=c;let p=(({ref:e,descriptor:t,env:r,windowScroll:n,direction:l,isDropDisabled:i,isCombineEnabled:a,shouldClipSubject:d})=>{let s=r.closestScrollable,u=((e,t)=>{let r=(0,o.YH)(e);if(!t||e!==t)return r;let n=r.paddingBox.top-t.scrollTop,l=r.paddingBox.left-t.scrollLeft,i=n+t.scrollHeight,a=l+t.scrollWidth,d=(0,o.fT)({top:n,right:a,bottom:i,left:l},r.border);return(0,o.ge)({borderBox:d,margin:r.margin,border:r.border,padding:r.padding})})(e,s),c=(0,o.SQ)(u,n),p=(()=>{if(!s)return null;let e=(0,o.YH)(s),t={scrollHeight:s.scrollHeight,scrollWidth:s.scrollWidth};return{client:e,page:(0,o.SQ)(e,n),scroll:rN(s),scrollSize:t,shouldClipSubject:d}})();return(({descriptor:e,isEnabled:t,isCombineEnabled:r,isFixedOnPage:n,direction:l,client:i,page:a,closest:o})=>{let d=(()=>{if(!o)return null;let{scrollSize:e,client:t}=o,r=tc({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:o.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:o.shouldClipSubject,scroll:{initial:o.scroll,current:o.scroll,max:r,diff:{value:E,displacement:E}}}})(),s="vertical"===l?Q:Z,u=L({page:a,withPlaceholder:null,axis:s,frame:d});return{descriptor:e,isCombineEnabled:r,isFixedOnPage:n,axis:s,isEnabled:t,client:i,page:a,frame:d,subject:u}})({descriptor:t,isEnabled:!i,isCombineEnabled:a,isFixedOnPage:r.isFixedOnPage,direction:l,client:u,page:c,closest:p})})({ref:a,descriptor:u,env:d,windowScroll:e,direction:i.direction,isDropDisabled:i.isDropDisabled,isCombineEnabled:i.isCombineEnabled,shouldClipSubject:!i.ignoreContainerClipping}),f=d.closestScrollable;return f&&(f.setAttribute(tL.contextId,r.contextId),f.addEventListener("scroll",h,rw(c.scrollOptions))),p},[r.contextId,u,h,s]),I=D(()=>{let e=t.current,r=rO(e);return e&&r||g(),rN(r)},[]),v=D(()=>{let e=t.current;e||g();let r=rO(e);t.current=null,r&&(b.cancel(),r.removeAttribute(tL.contextId),r.removeEventListener("scroll",h,rw(e.scrollOptions)))},[h,b]),A=D(e=>{let r=t.current;r||g();let n=rO(r);n||g(),n.scrollTop+=e.y,n.scrollLeft+=e.x},[]),N=x(()=>({getDimensionAndWatchScroll:y,getScrollWhileDragging:I,dragStopped:v,scroll:A}),[v,y,I,A]),R=x(()=>({uniqueId:l,descriptor:u,callbacks:N}),[N,u,l]);t_(()=>(c.current=R.descriptor,i.droppable.register(R),()=>{t.current&&v(),i.droppable.unregister(R)}),[N,u,v,R,a,i.droppable]),t_(()=>{t.current&&a.updateDroppableIsEnabled(c.current.id,!e.isDropDisabled)},[e.isDropDisabled,a]),t_(()=>{t.current&&a.updateDroppableIsCombineEnabled(c.current.id,e.isCombineEnabled)},[e.isCombineEnabled,a])}({droppableId:c,type:p,mode:f,direction:m,isDropDisabled:h,isCombineEnabled:y,ignoreContainerClipping:b,getDroppableRef:R});let S=x(()=>n.createElement(rk,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},({onClose:e,data:t,animate:l})=>n.createElement(rG,{placeholder:t,onClose:e,innerRef:w,animate:l,contextId:r,onTransitionEnd:O})),[r,O,e.placeholder,e.shouldAnimatePlaceholder,w]),B=x(()=>({innerRef:C,placeholder:S,droppableProps:{"data-rfd-droppable-id":c,"data-rfd-droppable-context-id":r}}),[r,c,S,C]),G=v?v.dragging.draggableId:null,T=x(()=>({droppableId:c,type:p,isUsingCloneFor:G}),[c,G,p]);return n.createElement(rs.Provider,{value:T},u(B,I),function(){if(!v)return null;let{dragging:e,render:t}=v,r=n.createElement(ry,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(r,n)=>t(r,n,e));return l.createPortal(r,N())}())})}}]);