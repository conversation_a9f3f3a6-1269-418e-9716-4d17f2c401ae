(()=>{var a={};a.id=9419,a.ids=[9419],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76488:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>D,patchFetch:()=>C,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var d={};c.r(d),c.d(d,{GET:()=>w,POST:()=>x});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(98889);async function w(a){try{let{searchParams:b}=new URL(a.url),c={action:b.get("action")||void 0,resource:b.get("resource")||void 0,userId:b.get("userId")||void 0,startDate:b.get("startDate")?new Date(b.get("startDate")):void 0,endDate:b.get("endDate")?new Date(b.get("endDate")):void 0,limit:parseInt(b.get("limit")||"100"),offset:parseInt(b.get("offset")||"0")},d=await v._O.getLogs(c),e=await v._O.getStats("day");return u.NextResponse.json({success:!0,logs:d,stats:e,filters:c,total:d.length})}catch(a){return console.error("Erro ao buscar logs de auditoria:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}async function x(a){try{let b=await a.json(),{action:c}=b;if("cleanup"===c){let a=parseInt(b.retentionDays||"1825"),c=await v._O.cleanOldLogs(a);return u.NextResponse.json({success:!0,message:`${c} logs antigos foram removidos`,removedCount:c})}if("stats"===c){let a=b.period||"day",c=await v._O.getStats(a);return u.NextResponse.json({success:!0,stats:c,period:a})}return u.NextResponse.json({success:!1,error:"A\xe7\xe3o inv\xe1lida"},{status:400})}catch(a){return console.error("Erro ao processar a\xe7\xe3o de auditoria:",a),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/audit/route",pathname:"/api/audit",filename:"route",bundlePath:"app/api/audit/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\audit\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function C(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function D(a,b,c){var d;let e="/api/audit/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{},98889:(a,b,c)=>{"use strict";c.d(b,{Yi:()=>f,_O:()=>e});class d{async log(a,b=!0,c){let d={id:this.generateId(),timestamp:new Date,success:b,errorMessage:c,...a};this.logs.push(d),this.logs.length>this.maxLogsInMemory&&(this.logs=this.logs.slice(-this.maxLogsInMemory));try{await this.saveToDatabase(d)}catch(a){console.error("Erro ao salvar log de auditoria:",a)}console.log("\uD83D\uDD0D AUDIT LOG:",{timestamp:d.timestamp.toISOString(),action:d.action,resource:d.resource,resourceId:d.resourceId,userId:d.userId,success:d.success,details:d.details})}async getLogs(a={}){let{action:b,resource:c,userId:d,startDate:e,endDate:f,limit:g=100,offset:h=0}=a,i=[...this.logs];return b&&(i=i.filter(a=>a.action===b)),c&&(i=i.filter(a=>a.resource===c)),d&&(i=i.filter(a=>a.userId===d)),e&&(i=i.filter(a=>a.timestamp>=e)),f&&(i=i.filter(a=>a.timestamp<=f)),i.sort((a,b)=>b.timestamp.getTime()-a.timestamp.getTime()),i.slice(h,h+g)}async getStats(a="day"){let b=new Date,c=new Date;switch(a){case"day":c.setDate(b.getDate()-1);break;case"week":c.setDate(b.getDate()-7);break;case"month":c.setMonth(b.getMonth()-1)}let d=this.logs.filter(a=>a.timestamp>=c),e={totalActions:d.length,successfulActions:d.filter(a=>a.success).length,failedActions:d.filter(a=>!a.success).length,actionsByType:{},resourcesByType:{}};return d.forEach(a=>{e.actionsByType[a.action]=(e.actionsByType[a.action]||0)+1,e.resourcesByType[a.resource]=(e.resourcesByType[a.resource]||0)+1}),e}async cleanOldLogs(a=1825){let b=new Date;b.setDate(b.getDate()-a);let c=this.logs.length;this.logs=this.logs.filter(a=>a.timestamp>b);let d=c-this.logs.length;return console.log(`🧹 Limpeza de logs: ${d} logs removidos (mais antigos que ${b.toISOString()})`),d}async saveToDatabase(a){}generateId(){return`audit_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}extractRequestInfo(a){return{ipAddress:a.headers.get("x-forwarded-for")||a.headers.get("x-real-ip")||"unknown",userAgent:a.headers.get("user-agent")||"unknown"}}constructor(){this.logs=[],this.maxLogsInMemory=1e3}}let e=new d,f=async(a,b,c,d,f)=>{let g=f?e.extractRequestInfo(f):{};await e.log({action:a,resource:"funcionario",resourceId:b,userId:c,details:d,...g})}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,6055],()=>b(b.s=76488));module.exports=c})();