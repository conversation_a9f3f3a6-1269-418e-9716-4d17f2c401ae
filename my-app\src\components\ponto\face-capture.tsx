'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui';
import { 
  Camera, 
  Loader2, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  User,
  Clock,
  Square,
  Play,
  Pause
} from 'lucide-react';

// Tipos para MediaPipe
interface FaceDetection {
  boundingBox: {
    xCenter: number;
    yCenter: number;
    width: number;
    height: number;
  };
  landmarks: Array<{
    x: number;
    y: number;
    z?: number;
  }>;
  score: number;
}

interface FaceCaptureProps {
  onCapture?: (faceData: string) => void;
  onError?: (error: string) => void;
  mode?: 'register' | 'recognize';
  className?: string;
  autoStart?: boolean; // Controlar se inicia automaticamente
}

type CaptureStatus = 'idle' | 'initializing' | 'ready' | 'capturing' | 'processing' | 'success' | 'error';

export function FaceCapture({
  onCapture,
  onError,
  mode = 'recognize',
  className = '',
  autoStart = true
}: FaceCaptureProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  
  const [status, setStatus] = useState<CaptureStatus>('idle');
  const [faceDetected, setFaceDetected] = useState(false);
  const [faceQuality, setFaceQuality] = useState(0);
  const [error, setError] = useState<string>('');
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  // MediaPipe instances
  const faceDetectionRef = useRef<unknown>(null);
  const faceMeshRef = useRef<unknown>(null);

  // Inicializar MediaPipe
  const initializeMediaPipe = useCallback(async () => {
    try {
      setStatus('initializing');

      // Tentar carregar MediaPipe com fallback
      try {
        // Importar MediaPipe dinamicamente
        const { FaceDetection } = await import('@mediapipe/face_detection');
        const { FaceMesh } = await import('@mediapipe/face_mesh');

        // Configurar Face Detection
        const faceDetection = new FaceDetection({
          locateFile: (file) => `https://cdn.jsdelivr.net/npm/@mediapipe/face_detection/${file}`
        });

        faceDetection.setOptions({
          model: 'short',
          minDetectionConfidence: 0.5,
        });

        faceDetection.onResults((results) => {
          handleFaceDetectionResults(results);
        });

        // Configurar Face Mesh para landmarks
        const faceMesh = new FaceMesh({
          locateFile: (file) => `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/${file}`
        });

        faceMesh.setOptions({
          maxNumFaces: 1,
          refineLandmarks: true,
          minDetectionConfidence: 0.5,
          minTrackingConfidence: 0.5,
        });

        faceMesh.onResults((results) => {
          handleFaceMeshResults(results);
        });

        faceDetectionRef.current = faceDetection;
        faceMeshRef.current = faceMesh;

        console.log('✅ MediaPipe inicializado com sucesso');

      } catch (mediaPipeError) {
        console.warn('⚠️ MediaPipe não disponível, usando modo simplificado:', mediaPipeError);

        // Modo simplificado sem MediaPipe - apenas captura de imagem
        faceDetectionRef.current = null;
        faceMeshRef.current = null;

        // Simular detecção básica
        setFaceDetected(true);
        setFaceQuality(0.8);
      }

      setStatus('ready');
    } catch (err) {
      console.error('Erro ao inicializar sistema de captura:', err);
      setError('Erro ao carregar sistema de captura facial');
      setStatus('error');
      onError?.('Erro ao inicializar sistema de captura');
    }
  }, [onError]);

  // Inicializar câmera
  const initializeCamera = useCallback(async () => {
    try {
      // Verificar se getUserMedia está disponível
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Câmera não suportada neste navegador');
      }

      // Verificar permissões primeiro
      const permissions = await navigator.permissions.query({ name: 'camera' as PermissionName });
      console.log('Permissão da câmera:', permissions.state);

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user'
        }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
      }

      return true;
    } catch (err: any) {
      console.error('Erro ao acessar câmera:', err);

      let errorMessage = 'Erro ao acessar câmera';

      if (err.name === 'NotAllowedError') {
        errorMessage = 'Permissão de câmera negada. Clique no ícone da câmera na barra de endereços e permita o acesso.';
      } else if (err.name === 'NotFoundError') {
        errorMessage = 'Nenhuma câmera encontrada no dispositivo.';
      } else if (err.name === 'NotSupportedError') {
        errorMessage = 'Câmera não suportada neste navegador.';
      } else if (err.name === 'NotReadableError') {
        errorMessage = 'Câmera está sendo usada por outro aplicativo.';
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      setStatus('error');
      onError?.(errorMessage);
      return false;
    }
  }, [onError]);

  // Processar resultados da detecção facial
  const handleFaceDetectionResults = useCallback((results: { detections?: unknown[] }) => {
    if (!canvasRef.current || !videoRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Limpar canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Desenhar vídeo
    ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
    
    if (results.detections && results.detections.length > 0) {
      const detection = results.detections[0];
      setFaceDetected(true);
      
      // Calcular qualidade da face baseada na confiança e posição
      const quality = calculateFaceQuality(detection);
      setFaceQuality(quality);
      
      // Desenhar bounding box
      drawFaceBoundingBox(ctx, detection, canvas.width, canvas.height);
      
      // Se qualidade boa e modo de captura, processar (com debounce)
      if (quality > 0.8 && status === 'capturing' && !isProcessing) {
        setIsProcessing(true);
        // Adicionar pequeno delay para evitar múltiplas capturas
        setTimeout(() => {
          if (status === 'capturing' && !isProcessing) {
            processFaceCapture(detection);
          }
        }, 500);
      }
    } else {
      setFaceDetected(false);
      setFaceQuality(0);
    }
  }, [status]);

  // Processar resultados do Face Mesh
  const handleFaceMeshResults = useCallback((results: { multiFaceLandmarks?: unknown[] }) => {
    // Processar landmarks para extração de características
    if (results.multiFaceLandmarks && results.multiFaceLandmarks.length > 0) {
      const landmarks = results.multiFaceLandmarks[0];
      // Aqui podemos extrair características específicas dos landmarks
    }
  }, []);

  // Calcular qualidade da face
  const calculateFaceQuality = (detection: any): number => {
    const bbox = detection.boundingBox;
    const score = detection.score;
    
    // Fatores de qualidade:
    // 1. Confiança da detecção
    // 2. Tamanho da face (não muito pequena/grande)
    // 3. Posição central
    
    let quality = score;
    
    // Penalizar faces muito pequenas ou grandes
    const faceSize = bbox.width * bbox.height;
    if (faceSize < 0.1 || faceSize > 0.6) {
      quality *= 0.7;
    }
    
    // Penalizar faces não centralizadas
    const centerX = bbox.xCenter;
    const centerY = bbox.yCenter;
    const distanceFromCenter = Math.sqrt(
      Math.pow(centerX - 0.5, 2) + Math.pow(centerY - 0.5, 2)
    );
    
    if (distanceFromCenter > 0.2) {
      quality *= 0.8;
    }
    
    return Math.min(quality, 1.0);
  };

  // Desenhar bounding box da face
  const drawFaceBoundingBox = (
    ctx: CanvasRenderingContext2D,
    detection: { boundingBox: { xCenter: number; yCenter: number; width: number; height: number } },
    width: number,
    height: number
  ) => {
    const bbox = detection.boundingBox;
    
    // Converter coordenadas normalizadas para pixels
    const x = (bbox.xCenter - bbox.width / 2) * width;
    const y = (bbox.yCenter - bbox.height / 2) * height;
    const w = bbox.width * width;
    const h = bbox.height * height;
    
    // Cor baseada na qualidade
    const quality = faceQuality;
    const color = quality > 0.8 ? '#00ff00' : quality > 0.5 ? '#ffff00' : '#ff0000';
    
    // Desenhar retângulo
    ctx.strokeStyle = color;
    ctx.lineWidth = 3;
    ctx.strokeRect(x, y, w, h);
    
    // Desenhar indicador de qualidade
    ctx.fillStyle = color;
    ctx.font = '16px Arial';
    ctx.fillText(`${Math.round(quality * 100)}%`, x, y - 10);
  };

  // Processar captura da face
  const processFaceCapture = async (detection: unknown) => {
    try {
      setStatus('processing');

      // Extrair região da face
      const faceData = await extractFaceFeatures(detection);

      if (faceData) {
        setStatus('success');
        onCapture?.(faceData);
      } else {
        throw new Error('Falha ao extrair características faciais');
      }
    } catch (err) {
      console.error('Erro ao processar captura:', err);
      setStatus('error');
      setError('Erro ao processar face capturada');
      onError?.('Erro ao processar captura facial');
    } finally {
      setIsProcessing(false);
    }
  };

  // Extrair características faciais
  const extractFaceFeatures = async (detection: unknown): Promise<string | null> => {
    if (!canvasRef.current || !videoRef.current) return null;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return null;
    
    // Capturar frame atual
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = canvas.width;
    tempCanvas.height = canvas.height;
    const tempCtx = tempCanvas.getContext('2d');
    if (!tempCtx) return null;
    
    tempCtx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
    
    // Extrair região da face
    const bbox = detection.boundingBox;
    const x = Math.max(0, (bbox.xCenter - bbox.width / 2) * canvas.width);
    const y = Math.max(0, (bbox.yCenter - bbox.height / 2) * canvas.height);
    const w = Math.min(canvas.width - x, bbox.width * canvas.width);
    const h = Math.min(canvas.height - y, bbox.height * canvas.height);
    
    const faceImageData = tempCtx.getImageData(x, y, w, h);
    
    // Converter para base64
    const faceCanvas = document.createElement('canvas');
    faceCanvas.width = w;
    faceCanvas.height = h;
    const faceCtx = faceCanvas.getContext('2d');
    if (!faceCtx) return null;
    
    faceCtx.putImageData(faceImageData, 0, 0);
    
    return faceCanvas.toDataURL('image/jpeg', 0.8);
  };

  // Iniciar captura
  const startCapture = useCallback(async () => {
    if (status !== 'ready') return;

    setStatus('capturing');
    setError('');

    // Se não há MediaPipe, fazer captura simples
    if (!faceDetectionRef.current) {
      setTimeout(() => {
        captureSimpleImage();
      }, 1000);
    }
  }, [status]);

  // Captura simples sem MediaPipe
  const captureSimpleImage = useCallback(async () => {
    if (!videoRef.current || !canvasRef.current) return;

    try {
      setStatus('processing');

      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Erro ao acessar canvas');

      // Capturar frame atual do vídeo
      ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);

      // Converter para base64
      const imageData = canvas.toDataURL('image/jpeg', 0.8);

      setStatus('success');
      onCapture?.(imageData);

    } catch (err) {
      console.error('Erro na captura simples:', err);
      setStatus('error');
      setError('Erro ao capturar imagem');
      onError?.('Erro ao capturar imagem');
    }
  }, [onCapture, onError]);

  // Parar captura
  const stopCapture = useCallback(() => {
    setStatus('ready');
    setError('');
    setIsProcessing(false);
  }, []);

  // Processar frame de vídeo
  const processVideoFrame = useCallback(async () => {
    if (!videoRef.current || !faceDetectionRef.current || !isRecording) return;
    
    try {
      await faceDetectionRef.current.send({ image: videoRef.current });
      
      if (faceMeshRef.current) {
        await faceMeshRef.current.send({ image: videoRef.current });
      }
    } catch (err) {
      console.error('Erro ao processar frame:', err);
    }
  }, [isRecording]);

  // Effect para inicialização
  useEffect(() => {
    initializeMediaPipe();
    
    return () => {
      // Cleanup
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, [initializeMediaPipe]);

  // Effect para iniciar câmera quando MediaPipe estiver pronto
  useEffect(() => {
    if (status === 'ready' && autoStart) {
      initializeCamera();
    }
  }, [status, initializeCamera, autoStart]);

  // Effect para processar frames
  useEffect(() => {
    if (!isRecording) return;
    
    const interval = setInterval(processVideoFrame, 100); // 10 FPS
    
    return () => clearInterval(interval);
  }, [isRecording, processVideoFrame]);

  // Iniciar/parar gravação quando vídeo carrega
  const handleVideoLoad = () => {
    setIsRecording(true);
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'initializing':
        return 'Inicializando sistema de reconhecimento...';
      case 'ready':
        return faceDetected 
          ? `Face detectada - Qualidade: ${Math.round(faceQuality * 100)}%`
          : 'Posicione seu rosto na câmera';
      case 'capturing':
        return 'Capturando face...';
      case 'processing':
        return 'Processando características faciais...';
      case 'success':
        return 'Face capturada com sucesso!';
      case 'error':
        return error || 'Erro no sistema';
      default:
        return 'Clique para iniciar';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      case 'ready':
        return faceQuality > 0.8 ? 'text-green-600' : 'text-yellow-600';
      default:
        return 'text-blue-600';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Área de vídeo */}
      <div className="relative bg-gray-900 rounded-lg overflow-hidden">
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className="w-full h-64 object-cover"
          onLoadedData={handleVideoLoad}
        />
        
        <canvas
          ref={canvasRef}
          width={640}
          height={480}
          className="absolute inset-0 w-full h-full object-cover"
        />
        
        {/* Overlay de status */}
        <div className="absolute inset-0 flex items-center justify-center">
          {status === 'initializing' && (
            <div className="bg-black bg-opacity-50 text-white p-4 rounded-lg flex items-center space-x-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Carregando...</span>
            </div>
          )}
          
          {status === 'capturing' && (
            <div className="bg-red-500 bg-opacity-20 border-2 border-red-500 rounded-lg p-2">
              <div className="w-4 h-4 bg-red-500 rounded-full animate-pulse" />
            </div>
          )}
        </div>
        
        {/* Indicadores de qualidade */}
        {faceDetected && (
          <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded">
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span className="text-sm">
                {Math.round(faceQuality * 100)}%
              </span>
            </div>
          </div>
        )}
      </div>
      
      {/* Status */}
      <div className={`text-center ${getStatusColor()}`}>
        <p className="font-medium">{getStatusMessage()}</p>
      </div>
      
      {/* Controles */}
      <div className="flex justify-center space-x-3">
        {status === 'ready' && !autoStart && !streamRef.current && (
          <Button
            onClick={initializeCamera}
            variant="primary"
            className="flex items-center space-x-2"
          >
            <Camera className="h-4 w-4" />
            <span>Iniciar Câmera</span>
          </Button>
        )}

        {status === 'ready' && streamRef.current && (
          <Button
            onClick={startCapture}
            disabled={!faceDetected || faceQuality < 0.5}
            variant="primary"
            className="flex items-center space-x-2"
          >
            <Camera className="h-4 w-4" />
            <span>{mode === 'register' ? 'Cadastrar Face' : 'Reconhecer'}</span>
          </Button>
        )}
        
        {status === 'capturing' && (
          <Button
            onClick={stopCapture}
            variant="secondary"
            className="flex items-center space-x-2"
          >
            <Square className="h-4 w-4" />
            <span>Cancelar</span>
          </Button>
        )}
        
        {status === 'success' && (
          <div className="flex items-center space-x-2 text-green-600">
            <CheckCircle className="h-5 w-5" />
            <span>Sucesso!</span>
          </div>
        )}
        
        {status === 'error' && (
          <div className="flex space-x-3">
            <Button
              onClick={async () => {
                setStatus('ready');
                setError('');
                await initializeCamera();
              }}
              variant="primary"
              className="flex items-center space-x-2"
            >
              <Camera className="h-4 w-4" />
              <span>Tentar Novamente</span>
            </Button>

            <Button
              onClick={() => {
                setStatus('ready');
                setError('');
              }}
              variant="secondary"
              className="flex items-center space-x-2"
            >
              <AlertTriangle className="h-4 w-4" />
              <span>Tentar Novamente</span>
            </Button>
          </div>
        )}
      </div>
      
      {/* Instruções */}
      <div className="text-sm text-gray-600 text-center space-y-1">
        <p>• Mantenha o rosto bem iluminado</p>
        <p>• Olhe diretamente para a câmera</p>
        <p>• Mantenha-se a uma distância confortável</p>
        {mode === 'register' && (
          <p className="text-blue-600 font-medium">
            • Modo cadastro: sua face será salva no sistema
          </p>
        )}
      </div>
    </div>
  );
}
