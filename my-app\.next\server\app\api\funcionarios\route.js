"use strict";(()=>{var a={};a.id=1086,a.ids=[1086],a.modules={261:a=>{a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:a=>{a.exports=require("querystring")},12412:a=>{a.exports=require("assert")},28354:a=>{a.exports=require("util")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45231:(a,b,c)=>{c.d(b,{N:()=>h});var d=c(13581),e=c(96330);let f=globalThis.prisma??new e.PrismaClient;var g=c(85663);let h={providers:[(0,d.A)({name:"credentials",credentials:{usuario:{label:"Usu\xe1rio",type:"text"},senha:{label:"Senha",type:"password"}},async authorize(a){if(!a?.usuario||!a?.senha)return null;try{let b=await f.usuario.findUnique({where:{usuario:a.usuario},include:{funcionario:!0}});if(!b||!b.ativo||!await (0,g.UD)(a.senha,b.senhaHash))return null;return{id:b.id.toString(),name:b.nome,email:b.email||"",role:b.nivelAcesso,usuario:b.usuario}}catch(a){return console.error("Erro na autentica\xe7\xe3o:",a),null}}})],session:{strategy:"jwt",maxAge:28800},jwt:{secret:process.env.NEXTAUTH_SECRET,maxAge:28800},callbacks:{jwt:async({token:a,user:b})=>(b&&(a.role=b.role,a.usuario=b.usuario),a),session:async({session:a,token:b})=>(b&&(a.user.id=b.sub,a.user.role=b.role,a.user.usuario=b.usuario),a)},pages:{signIn:"/login",error:"/login?error=auth_error"},debug:!1}},55511:a=>{a.exports=require("crypto")},55591:a=>{a.exports=require("https")},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{a.exports=require("zlib")},79428:a=>{a.exports=require("buffer")},79551:a=>{a.exports=require("url")},80435:(a,b,c)=>{c.r(b),c.d(b,{handler:()=>Y,patchFetch:()=>X,routeModule:()=>T,serverHooks:()=>W,workAsyncStorage:()=>U,workUnitAsyncStorage:()=>V});var d={};c.r(d),c.d(d,{GET:()=>Q,OPTIONS:()=>S,POST:()=>R});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(50639),w=c(19854),x=c(45231),y=c(98889);class z{constructor(a){this.cache=new Map,this.config=a,setInterval(()=>{this.cleanup()},this.config.interval)}cleanup(){let a=Date.now();for(let[b,c]of this.cache.entries())a>c.resetTime&&this.cache.delete(b)}getClientIP(a){let b=a.headers.get("x-forwarded-for"),c=a.headers.get("x-real-ip"),d=a.headers.get("cf-connecting-ip");return b?b.split(",")[0].trim():c||d||a.ip||"unknown"}async check(a,b){let c=Date.now(),d=b||this.getClientIP(a);if(this.cache.size>=this.config.uniqueTokenPerInterval&&(this.cleanup(),this.cache.size>=this.config.uniqueTokenPerInterval&&!this.cache.has(d)))return{success:!1,limit:this.config.maxRequests,remaining:0,resetTime:c+this.config.interval,error:"Muitas requisi\xe7\xf5es simult\xe2neas. Tente novamente em alguns minutos."};let e=this.cache.get(d);(!e||c>e.resetTime)&&(e={count:0,resetTime:c+this.config.interval}),e.count++,this.cache.set(d,e);let f=Math.max(0,this.config.maxRequests-e.count),g=e.count<=this.config.maxRequests;return{success:g,limit:this.config.maxRequests,remaining:f,resetTime:e.resetTime,error:g?void 0:"Limite de requisi\xe7\xf5es excedido. Tente novamente mais tarde."}}getStats(){return{totalEntries:this.cache.size,maxEntries:this.config.uniqueTokenPerInterval,interval:this.config.interval,maxRequests:this.config.maxRequests}}}let A={funcionarios:{interval:6e4,uniqueTokenPerInterval:100,maxRequests:5},consultas:{interval:6e4,uniqueTokenPerInterval:500,maxRequests:60},geral:{interval:6e4,uniqueTokenPerInterval:200,maxRequests:30}},B={funcionarios:new z(A.funcionarios),consultas:new z(A.consultas),geral:new z(A.geral)};async function C(a,b="geral",c){let d=B[b];return await d.check(a,c)}var D=c(36463),E=c(85698);function F(a){return a.replace(/\D/g,"")}function G(a,b){let c=0,d=b+1;for(let e=0;e<b;e++)c+=parseInt(a[e])*(d-e);let e=c%11;return e<2?0:11-e}var H=c(4483);let I=new Map;[{id:"1",nomeCompleto:"Jo\xe3o Silva",cpf:"12345678900",email:"<EMAIL>",telefone:"(11) 99999-9999",status:"ativo",dadosPessoais:{nomeCompleto:"Jo\xe3o Silva",cpf:"12345678900",rg:"123456789",email:"<EMAIL>",telefone:"(11) 99999-9999",dataNascimento:"1990-01-15",estadoCivil:"solteiro",endereco:{cep:"01234-567",logradouro:"Rua das Flores, 123",numero:"123",bairro:"Centro",cidade:"S\xe3o Paulo",uf:"SP"}},dadosProfissionais:{matricula:"EMP001",cargo:"Desenvolvedor",setor:"TI",salario:5e3,dataAdmissao:"2024-01-15",horarioTrabalho:{entrada:"08:00",saida:"17:00",intervaloInicio:"12:00",intervaloFim:"13:00"}},createdAt:new Date,updatedAt:new Date}].forEach(a=>{I.set(a.id,a)});class J{static getInstance(){return J.instance||(J.instance=new J),J.instance}async getAll(){try{return D.vF.info("Listando funcion\xe1rios (fallback)",{count:I.size}),Array.from(I.values())}catch(a){throw D.vF.error("Erro ao listar funcion\xe1rios (fallback)",{error:a instanceof Error?a.message:String(a)}),Error("Erro ao carregar funcion\xe1rios")}}async findAll(){return this.getAll()}async getById(a){try{let b=I.get(a)||null;return D.vF.info("Buscando funcion\xe1rio por ID (fallback)",{id:a,found:!!b}),b}catch(b){throw D.vF.error("Erro ao buscar funcion\xe1rio por ID (fallback)",{error:b instanceof Error?b.message:String(b),id:a}),Error("Erro ao buscar funcion\xe1rio")}}async create(a){try{let b=`func_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,c=a.matricula||this.generateMatricula(),d={...a,id:b,matricula:c,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return I.set(b,d),D.vF.info("Funcion\xe1rio criado (fallback)",{id:b,nome:d.nome,matricula:c}),d}catch(a){throw D.vF.error("Erro ao criar funcion\xe1rio (fallback)",{error:a instanceof Error?a.message:String(a)}),Error("Erro ao criar funcion\xe1rio")}}async update(a,b){try{let c=I.get(a);if(!c)return!1;let d={...c,...b,id:a,updatedAt:new Date().toISOString()};return I.set(a,d),D.vF.info("Funcion\xe1rio atualizado (fallback)",{id:a,nome:d.nome}),!0}catch(b){throw D.vF.error("Erro ao atualizar funcion\xe1rio (fallback)",{error:b instanceof Error?b.message:String(b),id:a}),Error("Erro ao atualizar funcion\xe1rio")}}async delete(a){try{let b=I.get(a);if(!b)return!1;return I.delete(a),D.vF.info("Funcion\xe1rio removido (fallback)",{id:a,nome:b.nome}),!0}catch(b){throw D.vF.error("Erro ao remover funcion\xe1rio (fallback)",{error:b instanceof Error?b.message:String(b),id:a}),Error("Erro ao remover funcion\xe1rio")}}async search(a){try{let b=a.toLowerCase(),c=Array.from(I.values()).filter(c=>c.nome.toLowerCase().includes(b)||c.cpf.includes(a)||c.matricula.toLowerCase().includes(b)||c.email.toLowerCase().includes(b));return D.vF.info("Busca de funcion\xe1rios (fallback)",{term:a,found:c.length}),c}catch(b){throw D.vF.error("Erro na busca de funcion\xe1rios (fallback)",{error:b instanceof Error?b.message:String(b),term:a}),Error("Erro na busca de funcion\xe1rios")}}generateMatricula(){let a=Array.from(I.values()).map(a=>a.matricula),b=1,c=`EMP${b.toString().padStart(3,"0")}`;for(;a.includes(c);)b++,c=`EMP${b.toString().padStart(3,"0")}`;return c}async cpfExists(a,b){return Array.from(I.values()).some(c=>c.cpf===a&&c.id!==b)}async emailExists(a,b){return Array.from(I.values()).some(c=>c.email===a&&c.id!==b)}async matriculaExists(a,b){return Array.from(I.values()).some(c=>c.dadosProfissionais.matricula===a&&c.id!==b)}async findByCpf(a){return Array.from(I.values()).find(b=>b.cpf===a)||null}async getNextMatricula(){return this.generateMatricula()}async save(a){try{return I.set(a.id,a),D.vF.info("Funcion\xe1rio salvo (fallback)",{id:a.id,nome:a.nomeCompleto}),!0}catch(a){return D.vF.error("Erro ao salvar funcion\xe1rio (fallback)",{error:a instanceof Error?a.message:String(a)}),!1}}}let K=J.getInstance();function L(a,b,c){let d=u.NextResponse.json(a,b),e=c?.headers.get("origin")||void 0;return(0,E.Lq)(d,e)}let M=v.Ik({nomeCompleto:v.Yj().min(2,"Nome deve ter pelo menos 2 caracteres"),cpf:v.Yj().transform(a=>F(a)).refine(a=>11===a.length,"CPF deve ter 11 d\xedgitos").refine(function(a){let b=F(a);if(!function(a){let b=F(a);return 11===b.length&&/^\d{11}$/.test(b)}(b)||function(a){let b=F(a);return/^(\d)\1{10}$/.test(b)}(b))return!1;let c=G(b,9);if(parseInt(b[9])!==c)return!1;let d=G(b,10);return parseInt(b[10])===d},"CPF inv\xe1lido"),rg:v.Yj().optional(),email:v.Yj().email("Email inv\xe1lido").optional(),telefone:v.Yj().optional(),celular:v.Yj().optional(),endereco:v.Ik({cep:v.Yj().optional(),logradouro:v.Yj().optional(),numero:v.Yj().optional(),complemento:v.Yj().optional(),bairro:v.Yj().optional(),cidade:v.Yj().optional(),uf:v.Yj().optional()}).optional(),dadosProfissionais:v.Ik({matricula:v.Yj().optional(),cargo:v.Yj().min(1,"Cargo \xe9 obrigat\xf3rio"),setor:v.Yj().min(1,"Setor \xe9 obrigat\xf3rio"),dataAdmissao:v.Yj().transform(a=>new Date(a)),salario:v.ai().optional(),cargaHoraria:v.ai().min(1,"Carga hor\xe1ria \xe9 obrigat\xf3ria"),horarioTrabalho:v.Ik({entrada:v.Yj(),saida:v.Yj(),intervaloInicio:v.Yj().optional(),intervaloFim:v.Yj().optional()})}),observacoes:v.Yj().optional()}),N=null,O=!1;async function P(){if(N&&O)return N;try{await H.v.findAll(),N=H.v,O=!0,D.vF.info("Usando storage com banco de dados")}catch(a){N=K,O=!0,D.vF.warn("Banco indispon\xedvel, usando fallback em mem\xf3ria",{error:a instanceof Error?a.message:String(a)})}return N}async function Q(a){try{let b=await (0,w.getServerSession)(x.N);if(!b)return L({success:!1,error:"N\xe3o autorizado"},{status:401},a);let c=await C(a,"consultas");if(!c.success)return u.NextResponse.json({success:!1,error:c.error,type:"rate_limit_exceeded"},{status:429,headers:{"X-RateLimit-Limit":c.limit?.toString()||"60","X-RateLimit-Remaining":c.remaining?.toString()||"0","X-RateLimit-Reset":new Date(c.resetTime||Date.now()+6e4).toISOString()}});let{searchParams:d}=new URL(a.url),e=d.get("search"),f=d.get("setor"),g=d.get("status"),h=parseInt(d.get("page")||"1"),i=parseInt(d.get("limit")||"10");await (0,y.Yi)("READ","list",b.user?.id||"unknown",{search:e,setor:f,status:g,page:h,limit:i},a);let j=await P(),k=await j.findAll?await j.findAll():await j.getAll();if(e){let a=e.toLowerCase();k=k.filter(b=>b.nomeCompleto.toLowerCase().includes(a)||b.cpf.includes(e)||b.dadosProfissionais.matricula.toLowerCase().includes(a))}f&&(k=k.filter(a=>a.dadosProfissionais.setor.toLowerCase()===f.toLowerCase())),g&&(k=k.filter(a=>a.status===g));let l=(h-1)*i,m=k.slice(l,l+i);return L({success:!0,funcionarios:m,pagination:{page:h,limit:i,total:k.length,totalPages:Math.ceil(k.length/i)}},void 0,a)}catch(a){return D.vF.error("Erro ao buscar funcion\xe1rios",{component:"FuncionariosAPI",action:"GET"},a instanceof Error?a:Error(String(a))),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}async function R(a){try{let b=await (0,w.getServerSession)(x.N);if(!b)return u.NextResponse.json({success:!1,error:"N\xe3o autorizado"},{status:401});let c=await C(a,"funcionarios");if(!c.success)return u.NextResponse.json({success:!1,error:c.error,type:"rate_limit_exceeded"},{status:429,headers:{"X-RateLimit-Limit":c.limit?.toString()||"5","X-RateLimit-Remaining":c.remaining?.toString()||"0","X-RateLimit-Reset":new Date(c.resetTime||Date.now()+6e4).toISOString(),"Retry-After":Math.ceil(((c.resetTime||Date.now()+6e4)-Date.now())/1e3).toString()}});let d=await a.json(),e=M.safeParse(d);if(!e.success)return u.NextResponse.json({success:!1,error:"Dados inv\xe1lidos",details:e.error.issues},{status:400});D.vF.debug("Gerando matr\xedcula automaticamente",{component:"FuncionariosAPI",action:"generateMatricula"});let f=await N.getNextMatricula();D.vF.info("Matr\xedcula gerada",{component:"FuncionariosAPI",matricula:f});try{if(await N.findByCpf(e.data.cpf))return D.vF.warn("Tentativa de cadastro com CPF duplicado",{component:"FuncionariosAPI",action:"validateCPF",cpf:e.data.cpf.substring(0,3)+"***"}),u.NextResponse.json({success:!1,error:"CPF j\xe1 est\xe1 cadastrado",details:[`CPF ${e.data.cpf} j\xe1 est\xe1 em uso por outro funcion\xe1rio`]},{status:409});D.vF.debug("Valida\xe7\xe3o conclu\xedda, prosseguindo com cadastro",{component:"FuncionariosAPI",action:"validateComplete"});let c=async()=>{let a=Date.now().toString(36),b=Math.random().toString(36).substring(2,15),c=((await N.findAll()).length+1).toString(36);return`func_${a}_${b}_${c}`},d={id:await c(),...e.data,dadosProfissionais:{...e.data.dadosProfissionais,matricula:f},foto:void 0,biometria:{cadastrada:!1,templates:0},status:"ativo",createdAt:new Date,updatedAt:new Date};if(!await N.save(d))throw Error("Falha ao salvar funcion\xe1rio no storage");await (0,y.Yi)("CREATE",d.id,b.user?.id||b.user?.email||"unknown",{nomeCompleto:d.nomeCompleto,cpf:d.cpf,matricula:d.dadosProfissionais.matricula,cargo:d.dadosProfissionais.cargo,setor:d.dadosProfissionais.setor},a),D.vF.info("Funcion\xe1rio cadastrado com sucesso",{component:"FuncionariosAPI",action:"create",matricula:d.dadosProfissionais.matricula,funcionarioId:d.id});let g=(await N.findAll()).length;return D.vF.info("Estat\xedsticas atualizadas",{component:"FuncionariosAPI",totalFuncionarios:g}),L({success:!0,funcionario:d,message:"Funcion\xe1rio cadastrado com sucesso"},void 0,a)}catch(a){return D.vF.error("Erro ao criar funcion\xe1rio",{component:"FuncionariosAPI",action:"POST"},a instanceof Error?a:Error(String(a))),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}catch(a){return D.vF.error("Erro na valida\xe7\xe3o",{component:"FuncionariosAPI",action:"POST"},a instanceof Error?a:Error(String(a))),u.NextResponse.json({success:!1,error:"Erro interno do servidor"},{status:500})}}async function S(a){let b=a.headers.get("origin"),c=new u.NextResponse(null,{status:204});return(0,E.Lq)(c,b||void 0)}let T=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/funcionarios/route",pathname:"/api/funcionarios",filename:"route",bundlePath:"app/api/funcionarios/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\api\\funcionarios\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:U,workUnitAsyncStorage:V,serverHooks:W}=T;function X(){return(0,g.patchFetch)({workAsyncStorage:U,workUnitAsyncStorage:V})}async function Y(a,b,c){var d;let e="/api/funcionarios/route";"/index"===e&&(e="/");let g=await T.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=g,D=(0,j.normalizeAppPath)(e),E=!!(y.dynamicRoutes[D]||y.routes[C]);if(E&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[D];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let F=null;!E||T.isDev||x||(F="/index"===(F=C)?"/":F);let G=!0===T.isDev||!E,H=E&&!G,I=a.method||"GET",J=(0,i.getTracer)(),K=J.getActiveScopeSpan(),L={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:G,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:H,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>T.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},M=new k.NodeNextRequest(a),N=new k.NodeNextResponse(b),O=l.NextRequestAdapter.fromNodeNextRequest(M,(0,l.signalFromNodeResponse)(b));try{let d=async c=>T.handle(O,L).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=J.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${I} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${I} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&B&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=L.renderOpts.fetchMetrics;let i=L.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=L.renderOpts.collectedTags;if(!E)return await (0,o.I)(M,N,e,L.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==L.renderOpts.collectedRevalidate&&!(L.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&L.renderOpts.collectedRevalidate,d=void 0===L.renderOpts.collectedExpire||L.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:L.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await T.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:H,isOnDemandRevalidate:A})},z),b}},l=await T.handleResponse({req:a,nextConfig:w,cacheKey:F,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!E)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&E||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(M,N,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};K?await g(K):await J.withPropagatedContext(a.headers,()=>J.trace(m.BaseServerSpan.handleRequest,{spanName:`${I} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":I,"http.target":a.url}},g))}catch(b){if(K||b instanceof s.NoFallbackError||await T.onRequestError(a,b,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:H,isOnDemandRevalidate:A})}),E)throw b;return await (0,o.I)(M,N,new Response(null,{status:500})),null}}},81630:a=>{a.exports=require("http")},85698:(a,b,c)=>{c.d(b,{Lq:()=>h});var d=c(32190);let e={cors:{origin:[process.env.NEXTAUTH_URL||"https://rlponto.com"],methods:["GET","POST","PUT","DELETE","OPTIONS"],allowedHeaders:["Content-Type","Authorization","X-Requested-With","Accept","Origin","X-CSRF-Token"],credentials:!0},csp:{enabled:!0,directives:{"default-src":["'self'"],"script-src":["'self'","'unsafe-inline'","'unsafe-eval'","https://cdn.jsdelivr.net","https://storage.googleapis.com"],"style-src":["'self'","'unsafe-inline'","https://fonts.googleapis.com"],"font-src":["'self'","https://fonts.gstatic.com"],"img-src":["'self'","data:","blob:","https:"],"media-src":["'self'","blob:"],"connect-src":["'self'","https://viacep.com.br","https://cdn.jsdelivr.net","https://storage.googleapis.com"],"worker-src":["'self'","blob:"],"frame-ancestors":["'none'"],"base-uri":["'self'"],"form-action":["'self'"],"upgrade-insecure-requests":[]}},hsts:{enabled:!0,maxAge:31536e3,includeSubDomains:!0,preload:!0}};class f{constructor(a){this.config={...e,...a,cors:{...e.cors,...a?.cors},csp:{...e.csp,...a?.csp},hsts:{...e.hsts,...a?.hsts}}}apply(a,b){return this.config.cors&&this.applyCorsHeaders(a,b),this.applySecurityHeaders(a),this.config.csp?.enabled&&this.applyCspHeaders(a),this.config.hsts?.enabled&&this.applyHstsHeaders(a),a}applyCorsHeaders(a,b){let{cors:c}=this.config;if(c){if(b&&c.origin){let d=Array.isArray(c.origin)?c.origin:[c.origin];(!0===c.origin||d.includes(b))&&a.headers.set("Access-Control-Allow-Origin",b)}else!0===c.origin&&a.headers.set("Access-Control-Allow-Origin","*");c.methods&&a.headers.set("Access-Control-Allow-Methods",c.methods.join(", ")),c.allowedHeaders&&a.headers.set("Access-Control-Allow-Headers",c.allowedHeaders.join(", ")),c.credentials&&a.headers.set("Access-Control-Allow-Credentials","true"),a.headers.set("Access-Control-Max-Age","86400")}}applySecurityHeaders(a){a.headers.set("X-Content-Type-Options","nosniff"),a.headers.set("X-Frame-Options","DENY"),a.headers.set("X-XSS-Protection","1; mode=block"),a.headers.set("Referrer-Policy","strict-origin-when-cross-origin"),a.headers.set("Permissions-Policy","camera=(self), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()"),a.headers.get("content-type")?.includes("application/json")&&(a.headers.set("Cache-Control","no-store, no-cache, must-revalidate, proxy-revalidate"),a.headers.set("Pragma","no-cache"),a.headers.set("Expires","0"))}applyCspHeaders(a){let{csp:b}=this.config;if(!b?.directives)return;let c=Object.entries(b.directives).map(([a,b])=>0===b.length?a:`${a} ${b.join(" ")}`).join("; ");a.headers.set("Content-Security-Policy",c)}applyHstsHeaders(a){let{hsts:b}=this.config;if(!b)return;let c=`max-age=${b.maxAge}`;b.includeSubDomains&&(c+="; includeSubDomains"),b.preload&&(c+="; preload"),a.headers.set("Strict-Transport-Security",c)}isOriginAllowed(a){let{cors:b}=this.config;return!!b?.origin&&(!0===b.origin||(Array.isArray(b.origin)?b.origin:[b.origin]).includes(a))}createPreflightResponse(a){let b=new d.NextResponse(null,{status:204});return this.apply(b,a)}}let g=new f;function h(a,b,c){return(c?new f(c):g).apply(a,b)}},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94735:a=>{a.exports=require("events")},96330:a=>{a.exports=require("@prisma/client")},98889:(a,b,c)=>{c.d(b,{Yi:()=>f,_O:()=>e});class d{async log(a,b=!0,c){let d={id:this.generateId(),timestamp:new Date,success:b,errorMessage:c,...a};this.logs.push(d),this.logs.length>this.maxLogsInMemory&&(this.logs=this.logs.slice(-this.maxLogsInMemory));try{await this.saveToDatabase(d)}catch(a){console.error("Erro ao salvar log de auditoria:",a)}console.log("\uD83D\uDD0D AUDIT LOG:",{timestamp:d.timestamp.toISOString(),action:d.action,resource:d.resource,resourceId:d.resourceId,userId:d.userId,success:d.success,details:d.details})}async getLogs(a={}){let{action:b,resource:c,userId:d,startDate:e,endDate:f,limit:g=100,offset:h=0}=a,i=[...this.logs];return b&&(i=i.filter(a=>a.action===b)),c&&(i=i.filter(a=>a.resource===c)),d&&(i=i.filter(a=>a.userId===d)),e&&(i=i.filter(a=>a.timestamp>=e)),f&&(i=i.filter(a=>a.timestamp<=f)),i.sort((a,b)=>b.timestamp.getTime()-a.timestamp.getTime()),i.slice(h,h+g)}async getStats(a="day"){let b=new Date,c=new Date;switch(a){case"day":c.setDate(b.getDate()-1);break;case"week":c.setDate(b.getDate()-7);break;case"month":c.setMonth(b.getMonth()-1)}let d=this.logs.filter(a=>a.timestamp>=c),e={totalActions:d.length,successfulActions:d.filter(a=>a.success).length,failedActions:d.filter(a=>!a.success).length,actionsByType:{},resourcesByType:{}};return d.forEach(a=>{e.actionsByType[a.action]=(e.actionsByType[a.action]||0)+1,e.resourcesByType[a.resource]=(e.resourcesByType[a.resource]||0)+1}),e}async cleanOldLogs(a=1825){let b=new Date;b.setDate(b.getDate()-a);let c=this.logs.length;this.logs=this.logs.filter(a=>a.timestamp>b);let d=c-this.logs.length;return console.log(`🧹 Limpeza de logs: ${d} logs removidos (mais antigos que ${b.toISOString()})`),d}async saveToDatabase(a){}generateId(){return`audit_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}extractRequestInfo(a){return{ipAddress:a.headers.get("x-forwarded-for")||a.headers.get("x-real-ip")||"unknown",userAgent:a.headers.get("user-agent")||"unknown"}}constructor(){this.logs=[],this.maxLogsInMemory=1e3}}let e=new d,f=async(a,b,c,d,f)=>{let g=f?e.extractRequestInfo(f):{};await e.log({action:a,resource:"funcionario",resourceId:b,userId:c,details:d,...g})}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,6055,3916,639,9135],()=>b(b.s=80435));module.exports=c})();