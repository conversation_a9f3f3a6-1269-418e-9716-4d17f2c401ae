exports.id=2121,exports.ids=[2121],exports.modules={3867:(a,b,c)=>{"use strict";c.d(b,{DashboardNav:()=>s});var d=c(60687),e=c(85814),f=c.n(e),g=c(16189),h=c(49384),i=c(32192),j=c(41312),k=c(23026),l=c(48730),m=c(6123),n=c(53411),o=c(10022),p=c(25541),q=c(84027);let r=[{name:"Dashboard",href:"/dashboard",icon:i.A,description:"Vis\xe3o geral do sistema"},{name:"Funcion\xe1rios",href:"/funcionarios",icon:j.A,description:"Gest\xe3o de funcion\xe1rios",children:[{name:"Lista de Funcion\xe1rios",href:"/funcionarios",icon:j.A},{name:"Novo Funcion\xe1rio",href:"/funcionarios/novo",icon:k.A}]},{name:"<PERSON><PERSON>",href:"/ponto/biometrico",icon:l.A,description:"Registro de ponto",children:[{name:"Ponto Biom\xe9trico",href:"/ponto/biometrico",icon:l.A},{name:"Ponto Manual",href:"/ponto/manual",icon:m.A}]},{name:"Per\xedodo de Apura\xe7\xe3o",href:"/periodo-apuracao",icon:n.A,description:"An\xe1lise mensal"},{name:"Relat\xf3rios",href:"/relatorios",icon:o.A,description:"Relat\xf3rios e an\xe1lises",children:[{name:"Relat\xf3rio Individual",href:"/relatorios/funcionario",icon:o.A},{name:"Relat\xf3rio por Per\xedodo",href:"/relatorios/periodo",icon:o.A},{name:"Relat\xf3rios Anal\xedticos",href:"/relatorios/analiticos",icon:o.A}]},{name:"Estat\xedsticas",href:"/estatisticas",icon:p.A,description:"An\xe1lises e KPIs",children:[{name:"Produtividade",href:"/estatisticas/produtividade",icon:p.A},{name:"Absente\xedsmo",href:"/estatisticas/absenteismo",icon:p.A},{name:"Tend\xeancias",href:"/estatisticas/tendencias",icon:p.A},{name:"Comparativos",href:"/estatisticas/comparativos",icon:p.A}]},{name:"Administra\xe7\xe3o",href:"/administracao",icon:q.A,description:"Configura\xe7\xf5es do sistema"}];function s(){let a=(0,g.usePathname)();return(0,d.jsx)("nav",{className:"w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen",children:(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)("div",{className:"space-y-1",children:r.map(b=>{let c=a===b.href||a.startsWith(b.href+"/");return(0,d.jsxs)("div",{children:[(0,d.jsxs)(f(),{href:b.href,className:(0,h.$)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",c?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-50 hover:text-gray-900"),children:[(0,d.jsx)(b.icon,{className:(0,h.$)("mr-3 h-5 w-5",c?"text-blue-700":"text-gray-400")}),b.name]}),b.children&&c&&(0,d.jsx)("div",{className:"ml-8 mt-1 space-y-1",children:b.children.map(b=>{let c=a===b.href;return(0,d.jsxs)(f(),{href:b.href,className:(0,h.$)("flex items-center px-3 py-1 text-xs font-medium rounded-md transition-colors",c?"bg-blue-100 text-blue-800":"text-gray-600 hover:bg-gray-50 hover:text-gray-800"),children:[(0,d.jsx)(b.icon,{className:"mr-2 h-3 w-3"}),b.name]},b.name)})})]},b.name)})})})})}},30705:(a,b,c)=>{"use strict";c.d(b,{DashboardHeader:()=>m});var d=c(60687),e=c(43210),f=c(42613),g=c(22502),h=c(97051),i=c(58869),j=c(78272),k=c(84027),l=c(40083);function m(){let{toUpperCaseText:a}=(0,g.w3)(),[b,c]=(0,e.useState)(!1),[m,n]=(0,e.useState)("");return(0,d.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,d.jsx)("div",{className:"px-6 py-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-white font-bold text-sm",children:"RL"})}),(0,d.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"RLPONTO"})]})}),(0,d.jsx)("div",{className:"flex-1 max-w-lg mx-8",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(f.WI,{className:"absolute left-3 top-1/2 -translate-y-1/2"}),(0,d.jsx)("input",{type:"text",value:m,onChange:b=>n(a(b.target.value)),placeholder:"Buscar funcion\xe1rios, relat\xf3rios...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder:text-gray-600 text-gray-900 font-semibold"})]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("button",{className:"relative p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100",children:[(0,d.jsx)(h.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("button",{onClick:()=>c(!b),className:"flex items-center space-x-2 p-2 text-gray-700 hover:text-gray-900 rounded-lg hover:bg-gray-100",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,d.jsx)(i.A,{className:"h-4 w-4"})}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Administrador"}),(0,d.jsx)(j.A,{className:"h-4 w-4"})]}),b&&(0,d.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:[(0,d.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,d.jsx)(i.A,{className:"mr-3 h-4 w-4"}),"Meu Perfil"]}),(0,d.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,d.jsx)(k.A,{className:"mr-3 h-4 w-4"}),"Configura\xe7\xf5es"]}),(0,d.jsx)("hr",{className:"my-1"}),(0,d.jsxs)("button",{onClick:()=>{window.location.href="/login"},className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50",children:[(0,d.jsx)(l.A,{className:"mr-3 h-4 w-4"}),"Sair"]})]})]})]})]})})})}},57675:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,metadata:()=>g});var d=c(37413),e=c(80857),f=c(71199);let g={title:"Dashboard - RLPONTO",description:"Sistema de controle de ponto eletr\xf4nico"};function h({children:a}){return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(f.DashboardHeader,{}),(0,d.jsxs)("div",{className:"flex",children:[(0,d.jsx)(e.DashboardNav,{}),(0,d.jsx)("main",{className:"flex-1 overflow-auto",children:a})]})]})}},58570:(a,b,c)=>{"use strict";c.d(b,{SearchIcon:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call SearchIcon() from the server but SearchIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\ui\\SearchIcon.tsx","SearchIcon")},59601:(a,b,c)=>{Promise.resolve().then(c.bind(c,30705)),Promise.resolve().then(c.bind(c,3867))},69329:(a,b,c)=>{Promise.resolve().then(c.bind(c,71199)),Promise.resolve().then(c.bind(c,80857))},71199:(a,b,c)=>{"use strict";c.d(b,{DashboardHeader:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call DashboardHeader() from the server but DashboardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\dashboard\\dashboard-header.tsx","DashboardHeader")},75243:(a,b,c)=>{"use strict";c.d(b,{$n:()=>g});var d=c(37413);c(61120);var e=c(75986),f=c(8974);let g=({children:a,className:b,variant:c="primary",size:g="md",disabled:h=!1,loading:i=!1,type:j="button",onClick:k,...l})=>(0,d.jsxs)("button",{type:j,className:function(...a){return(0,f.QP)((0,e.$)(a))}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300",outline:"border border-gray-400 bg-transparent text-gray-700 hover:bg-gray-50 hover:border-gray-500",ghost:"text-gray-700 hover:bg-gray-100",destructive:"bg-red-600 text-white hover:bg-red-700"}[c],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-12 px-6 text-lg"}[g],b),disabled:h||i,onClick:k,...l,children:[i&&(0,d.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a]});var h=c(91142),i=c(89633),j=c(27467),k=c(65276);h.A,i.A,j.A,k.A,c(58570)},80857:(a,b,c)=>{"use strict";c.d(b,{DashboardNav:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call DashboardNav() from the server but DashboardNav is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\dashboard\\dashboard-nav.tsx","DashboardNav")}};