(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6827],{6827:function(t,e,n){(function(){"use strict";function t(t){var e=0;return function(){return e<t.length?{done:!1,value:t[e++]}:{done:!0}}}var e,r,i="function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,n){return t==Array.prototype||t==Object.prototype||(t[e]=n.value),t},o=function(t){t=["object"==typeof globalThis&&globalThis,t,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof n.g&&n.g];for(var e=0;e<t.length;++e){var r=t[e];if(r&&r.Math==Math)return r}throw Error("Cannot find global object")}(this);function a(t,e){if(e)t:{var n=o;t=t.split(".");for(var r=0;r<t.length-1;r++){var a=t[r];if(!(a in n))break t;n=n[a]}(e=e(r=n[t=t[t.length-1]]))!=r&&null!=e&&i(n,t,{configurable:!0,writable:!0,value:e})}}function u(e){var n="undefined"!=typeof Symbol&&Symbol.iterator&&e[Symbol.iterator];return n?n.call(e):{next:t(e)}}function s(t){if(!(t instanceof Array)){t=u(t);for(var e,n=[];!(e=t.next()).done;)n.push(e.value);t=n}return t}a("Symbol",function(t){function e(t,e){this.g=t,i(this,"description",{configurable:!0,writable:!0,value:e})}if(t)return t;e.prototype.toString=function(){return this.g};var n="jscomp_symbol_"+(1e9*Math.random()>>>0)+"_",r=0;return function t(i){if(this instanceof t)throw TypeError("Symbol is not a constructor");return new e(n+(i||"")+"_"+r++,i)}}),a("Symbol.iterator",function(e){if(e)return e;e=Symbol("Symbol.iterator");for(var n="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),r=0;r<n.length;r++){var a=o[n[r]];"function"==typeof a&&"function"!=typeof a.prototype[e]&&i(a.prototype,e,{configurable:!0,writable:!0,value:function(){var e;return(e={next:e=t(this)})[Symbol.iterator]=function(){return this},e}})}return e});var c,f,l="function"==typeof Object.create?Object.create:function(t){function e(){}return e.prototype=t,new e};if("function"==typeof Object.setPrototypeOf)f=Object.setPrototypeOf;else{t:{var h={};try{h.__proto__={a:!0},F=h.a;break t}catch(t){}F=!1}f=F?function(t,e){if(t.__proto__=e,t.__proto__!==e)throw TypeError(t+" is not extensible");return t}:null}var g=f;function d(t,e){if(t.prototype=l(e.prototype),t.prototype.constructor=t,g)g(t,e);else for(var n in e)if("prototype"!=n)if(Object.defineProperties){var r=Object.getOwnPropertyDescriptor(e,n);r&&Object.defineProperty(t,n,r)}else t[n]=e[n];t.na=e.prototype}function p(){this.l=!1,this.i=null,this.h=void 0,this.g=1,this.u=this.o=0,this.j=null}function v(t){if(t.l)throw TypeError("Generator is already running");t.l=!0}function y(t,e){t.j={da:e,ea:!0},t.g=t.o||t.u}function b(t,e,n){return t.g=n,{value:e}}function m(t){this.g=new p,this.h=t}function w(t,e,n,r){try{var i=e.call(t.g.i,n);if(!(i instanceof Object))throw TypeError("Iterator result "+i+" is not an object");if(!i.done)return t.g.l=!1,i;var o=i.value}catch(e){return t.g.i=null,y(t.g,e),A(t)}return t.g.i=null,r.call(t.g,o),A(t)}function A(t){for(;t.g.g;)try{var e=t.h(t.g);if(e)return t.g.l=!1,{value:e.value,done:!1}}catch(e){t.g.h=void 0,y(t.g,e)}if(t.g.l=!1,t.g.j){if(e=t.g.j,t.g.j=null,e.ea)throw e.da;return{value:e.return,done:!0}}return{value:void 0,done:!0}}function E(t){this.next=function(e){return v(t.g),t.g.i?e=w(t,t.g.i.next,e,t.g.s):(t.g.s(e),e=A(t)),e},this.throw=function(e){return v(t.g),t.g.i?e=w(t,t.g.i.throw,e,t.g.s):(y(t.g,e),e=A(t)),e},this.return=function(e){var n;return v(t.g),(n=t.g.i)?w(t,"return"in n?n.return:function(t){return{value:t,done:!0}},e,t.g.return):(t.g.return(e),A(t))},this[Symbol.iterator]=function(){return this}}function x(t){var e=new E(new m(t));function n(t){return e.next(t)}function r(t){return e.throw(t)}return new Promise(function(t,i){!function e(o){o.done?t(o.value):Promise.resolve(o.value).then(n,r).then(e,i)}(e.next())})}p.prototype.s=function(t){this.h=t},p.prototype.return=function(t){this.j={return:t},this.g=this.u},a("Promise",function(t){function e(t){this.h=0,this.i=void 0,this.g=[],this.s=!1;var e=this.j();try{t(e.resolve,e.reject)}catch(t){e.reject(t)}}function n(){this.g=null}function r(t){return t instanceof e?t:new e(function(e){e(t)})}if(t)return t;n.prototype.h=function(t){if(null==this.g){this.g=[];var e=this;this.i(function(){e.l()})}this.g.push(t)};var i=o.setTimeout;n.prototype.i=function(t){i(t,0)},n.prototype.l=function(){for(;this.g&&this.g.length;){var t=this.g;this.g=[];for(var e=0;e<t.length;++e){var n=t[e];t[e]=null;try{n()}catch(t){this.j(t)}}}this.g=null},n.prototype.j=function(t){this.i(function(){throw t})},e.prototype.j=function(){function t(t){return function(r){n||(n=!0,t.call(e,r))}}var e=this,n=!1;return{resolve:t(this.D),reject:t(this.l)}},e.prototype.D=function(t){if(t===this)this.l(TypeError("A Promise cannot resolve to itself"));else if(t instanceof e)this.H(t);else{switch(typeof t){case"object":var n=null!=t;break;case"function":n=!0;break;default:n=!1}n?this.A(t):this.o(t)}},e.prototype.A=function(t){var e=void 0;try{e=t.then}catch(t){this.l(t);return}"function"==typeof e?this.I(e,t):this.o(t)},e.prototype.l=function(t){this.u(2,t)},e.prototype.o=function(t){this.u(1,t)},e.prototype.u=function(t,e){if(0!=this.h)throw Error("Cannot settle("+t+", "+e+"): Promise already settled in state"+this.h);this.h=t,this.i=e,2===this.h&&this.G(),this.B()},e.prototype.G=function(){var t=this;i(function(){if(t.C()){var e=o.console;void 0!==e&&e.error(t.i)}},1)},e.prototype.C=function(){if(this.s)return!1;var t=o.CustomEvent,e=o.Event,n=o.dispatchEvent;return void 0===n||("function"==typeof t?t=new t("unhandledrejection",{cancelable:!0}):"function"==typeof e?t=new e("unhandledrejection",{cancelable:!0}):(t=o.document.createEvent("CustomEvent")).initCustomEvent("unhandledrejection",!1,!0,t),t.promise=this,t.reason=this.i,n(t))},e.prototype.B=function(){if(null!=this.g){for(var t=0;t<this.g.length;++t)a.h(this.g[t]);this.g=null}};var a=new n;return e.prototype.H=function(t){var e=this.j();t.M(e.resolve,e.reject)},e.prototype.I=function(t,e){var n=this.j();try{t.call(e,n.resolve,n.reject)}catch(t){n.reject(t)}},e.prototype.then=function(t,n){function r(t,e){return"function"==typeof t?function(e){try{i(t(e))}catch(t){o(t)}}:e}var i,o,a=new e(function(t,e){i=t,o=e});return this.M(r(t,i),r(n,o)),a},e.prototype.catch=function(t){return this.then(void 0,t)},e.prototype.M=function(t,e){function n(){switch(r.h){case 1:t(r.i);break;case 2:e(r.i);break;default:throw Error("Unexpected state: "+r.h)}}var r=this;null==this.g?a.h(n):this.g.push(n),this.s=!0},e.resolve=r,e.reject=function(t){return new e(function(e,n){n(t)})},e.race=function(t){return new e(function(e,n){for(var i=u(t),o=i.next();!o.done;o=i.next())r(o.value).M(e,n)})},e.all=function(t){var n=u(t),i=n.next();return i.done?r([]):new e(function(t,e){var o=[],a=0;do o.push(void 0),a++,r(i.value).M(function(e){return function(n){o[e]=n,0==--a&&t(o)}}(o.length-1),e),i=n.next();while(!i.done)})},e});var _="function"==typeof Object.assign?Object.assign:function(t,e){for(var n=1;n<arguments.length;n++){var r=arguments[n];if(r)for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t};a("Object.assign",function(t){return t||_}),a("Object.is",function(t){return t||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}}),a("Array.prototype.includes",function(t){return t||function(t,e){var n=this;n instanceof String&&(n=String(n));var r=n.length;for(0>(e=e||0)&&(e=Math.max(e+r,0));e<r;e++){var i=n[e];if(i===t||Object.is(i,t))return!0}return!1}}),a("String.prototype.includes",function(t){return t||function(t,e){if(null==this)throw TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(t instanceof RegExp)throw TypeError("First argument to String.prototype.includes must not be a regular expression");return -1!==this.indexOf(t,e||0)}}),a("Array.prototype.keys",function(t){return t||function(){var t,e,n,r,i;return t=this,e=function(t){return t},t instanceof String&&(t+=""),n=0,r=!1,(i={next:function(){if(!r&&n<t.length){var i=n++;return{value:e(i,t[i]),done:!1}}return r=!0,{done:!0,value:void 0}}})[Symbol.iterator]=function(){return i},i}});var j=this||self;function O(t,e){t=t.split(".");var n,r=j;for((t[0]in r)||void 0===r.execScript||r.execScript("var "+t[0]);t.length&&(n=t.shift());)t.length||void 0===e?r=r[n]&&r[n]!==Object.prototype[n]?r[n]:r[n]={}:r[n]=e}function T(){throw Error("Invalid UTF8")}function S(t,e){return e=String.fromCharCode.apply(null,e),null==t?e:t+e}var F,C,k,R="undefined"!=typeof TextDecoder,U="undefined"!=typeof TextEncoder,P={},I=null;function B(t){var e;void 0===e&&(e=0),N(),e=P[e];for(var n=Array(Math.floor(t.length/3)),r=e[64]||"",i=0,o=0;i<t.length-2;i+=3){var a=t[i],u=t[i+1],s=t[i+2],c=e[a>>2];a=e[(3&a)<<4|u>>4],u=e[(15&u)<<2|s>>6],s=e[63&s],n[o++]=c+a+u+s}switch(c=0,s=r,t.length-i){case 2:s=e[(15&(c=t[i+1]))<<2]||r;case 1:t=t[i],n[o]=e[t>>2]+e[(3&t)<<4|c>>4]+s+r}return n.join("")}function L(t){var e=t.length,n=3*e/4;n%3?n=Math.floor(n):-1!="=.".indexOf(t[e-1])&&(n=-1!="=.".indexOf(t[e-2])?n-2:n-1);var r=new Uint8Array(n),i=0;return function(t,e){function n(e){for(;r<t.length;){var n=t.charAt(r++),i=I[n];if(null!=i)return i;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n)}return e}N();for(var r=0;;){var i=n(-1),o=n(0),a=n(64),u=n(64);if(64===u&&-1===i)break;e(i<<2|o>>4),64!=a&&(e(o<<4&240|a>>2),64!=u&&e(a<<6&192|u))}}(t,function(t){r[i++]=t}),i!==n?r.subarray(0,i):r}function N(){if(!I){I={};for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),e=["+/=","+/","-_=","-_.","-_"],n=0;5>n;n++){var r=t.concat(e[n].split(""));P[n]=r;for(var i=0;i<r.length;i++){var o=r[i];void 0===I[o]&&(I[o]=i)}}}}var M="function"==typeof Uint8Array;function D(t){return M&&null!=t&&t instanceof Uint8Array}function G(t){if(this.L=t,null!==t&&0===t.length)throw Error("ByteString should be constructed with non-empty values")}var W="function"==typeof Uint8Array.prototype.slice,V=0,Y=0;function H(t,e){return Error("Invalid wire type: "+t+" (at position "+e+")")}function z(){return Error("Failed to read varint, encoding is invalid.")}function X(t,e){e=void 0!==(e=void 0===e?{}:e).v&&e.v,this.h=null,this.g=this.i=this.j=0,this.v=e,t&&J(this,t)}function J(t,n){t.h=function(t,n){if(t.constructor===Uint8Array)return t;if(t.constructor===ArrayBuffer||t.constructor===Array)return new Uint8Array(t);if(t.constructor===String)return L(t);if(t.constructor===G)return!n&&(n=t.L)&&n.constructor===Uint8Array?n:(n=null==(n=t.L)||D(n)?n:"string"==typeof n?L(n):null,(t=t.L=n)?new Uint8Array(t):e||(e=new Uint8Array(0)));if(t instanceof Uint8Array)return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw Error("Type not convertible to a Uint8Array, expected a Uint8Array, an ArrayBuffer, a base64 encoded string, or Array of numbers")}(n,t.v),t.j=0,t.i=t.h.length,t.g=t.j}function K(t){if(t.g>t.i)throw Error("Tried to read past the end of the data "+t.g+" > "+t.i)}function $(t){var e=t.h,n=e[t.g],r=127&n;if(128>n)return t.g+=1,K(t),r;if(r|=(127&(n=e[t.g+1]))<<7,128>n)return t.g+=2,K(t),r;if(r|=(127&(n=e[t.g+2]))<<14,128>n)return t.g+=3,K(t),r;if(r|=(127&(n=e[t.g+3]))<<21,128>n)return t.g+=4,K(t),r;if(n=e[t.g+4],t.g+=5,r|=(15&n)<<28,128>n)return K(t),r;if(128<=e[t.g++]&&128<=e[t.g++]&&128<=e[t.g++]&&128<=e[t.g++]&&128<=e[t.g++])throw z();return K(t),r}X.prototype.reset=function(){this.g=this.j};var Z=[];function q(){this.g=[]}function Q(t,e){for(;127<e;)t.g.push(127&e|128),e>>>=7;t.g.push(e)}function tt(t){var e={},n=void 0!==e.W&&e.W;this.l={v:void 0!==e.v&&e.v},this.W=n,e=this.l,Z.length?(n=Z.pop(),e&&(n.v=e.v),t&&J(n,t),t=n):t=new X(t,e),this.g=t,this.j=this.g.g,this.h=this.i=-1}function te(t){var e=t.g;if(e.g==e.i)return!1;t.j=t.g.g;var n=$(t.g)>>>0;if(e=n>>>3,!(0<=(n&=7)&&5>=n))throw H(n,t.j);if(1>e)throw Error("Invalid field number: "+e+" (at position "+t.j+")");return t.i=e,t.h=n,!0}q.prototype.length=function(){return this.g.length},q.prototype.end=function(){var t=this.g;return this.g=[],t},tt.prototype.reset=function(){this.g.reset(),this.j=this.g.g,this.h=this.i=-1};var tn=[];function tr(){this.i=[],this.h=0,this.g=new q}function ti(t,e){0!==e.length&&(t.i.push(e),t.h+=e.length)}var to="function"==typeof Symbol&&"symbol"==typeof Symbol()?Symbol(void 0):void 0;function ta(t,e){Object.isFrozen(t)||(to?t[to]|=e:void 0!==t.N?t.N|=e:Object.defineProperties(t,{N:{value:e,configurable:!0,writable:!0,enumerable:!1}}))}function tu(t){var e;return null==(e=to?t[to]:t.N)?0:e}function ts(t){return ta(t,1),t}function tc(t){return!!Array.isArray(t)&&!!(2&tu(t))}function tf(t){if(!Array.isArray(t))throw Error("cannot mark non-array as immutable");ta(t,2)}function tl(t){return null!==t&&"object"==typeof t&&!Array.isArray(t)&&t.constructor===Object}var th=Object.freeze(ts([]));function tg(t){if(tc(t.m))throw Error("Cannot mutate an immutable Message")}var td="undefined"!=typeof Symbol&&void 0!==Symbol.hasInstance;function tp(t){return{value:t,configurable:!1,writable:!1,enumerable:!1}}function tv(t,e,n){return -1===e?null:e>=t.i?t.g?t.g[e]:void 0:(void 0===n?0:n)&&t.g&&null!=(n=t.g[e])?n:t.m[e+t.h]}function ty(t,e,n,r){r=void 0!==r&&r,tg(t),e<t.i&&!r?t.m[e+t.h]=n:(t.g||(t.g=t.m[t.i+t.h]={}))[e]=n}function tb(t,e,n,r){n=void 0===n||n;var i=tv(t,e,r=void 0!==r&&r);return null==i&&(i=th),tc(t.m)?n&&(tf(i),Object.freeze(i)):(i===th||tc(i))&&ty(t,e,i=ts(i.slice()),r),i}function tm(t,e,n){return null==(t=null==(t=tv(t,e))?t:+t)?void 0===n?0:n:t}function tw(t,e,n,r){t.j||(t.j={});var i=tc(t.m),o=t.j[n];if(!o){r=tb(t,n,!0,void 0!==r&&r),o=[],i=i||tc(r);for(var a=0;a<r.length;a++)o[a]=new e(r[a]),i&&tf(o[a].m);i&&(tf(o),Object.freeze(o)),t.j[n]=o}return o}function tA(t,e,n,r,i){var o=void 0!==o&&o;return tg(t),o=tw(t,n,e,o),n=r||new n,t=tb(t,e),void 0!=i?(o.splice(i,0,n),t.splice(i,0,n.m)):(o.push(n),t.push(n.m)),n}function tE(t,e){return null==(t=tv(t,e))?0:t}function tx(t,e){return null==(t=tv(t,e))?"":t}function t_(t){var e=tj;return function t(e,n){for(var r=e.slice(),i=0;i<r.length;i++)r[i]=function e(n,r){if(null!=n){if(Array.isArray(n))n=t(n,r);else if(tl(n)){var i,o={};for(i in n)o[i]=e(n[i],r);n=o}else n=r(n);return n}}(r[i],n);return Array.isArray(e)&&1&tu(e)&&ts(r),r}(t,e=void 0===e?tO:e)}function tj(t){return t&&"object"==typeof t&&t.toJSON?t.toJSON():Array.isArray(t=function(t){switch(typeof t){case"number":return isFinite(t)?t:String(t);case"object":if(t&&!Array.isArray(t)){if(D(t))return B(t);if(t instanceof G){var e=t.L;return(t.L=e=null==e||"string"==typeof e?e:M&&e instanceof Uint8Array?B(e):null)||""}}}return t}(t))?t_(t):t}function tO(t){return D(t)?new Uint8Array(t):t}function tT(t,e,n){t||(t=r),r=null;var i=this.constructor.h;t||(t=i?[i]:[]),this.h=(i?0:-1)-(this.constructor.g||0),this.j=void 0,this.m=t;t:{if(t=(i=this.m.length)-1,i&&tl(i=this.m[t])){this.i=t-this.h,this.g=i;break t}void 0!==e&&-1<e?(this.i=Math.max(e,t+1-this.h),this.g=void 0):this.i=Number.MAX_VALUE}if(n)for(e=0;e<n.length;e++)if((t=n[e])<this.i)t+=this.h,(i=this.m[t])?Array.isArray(i)&&ts(i):this.m[t]=th;else{var o=(i=this.g||(this.g=this.m[this.i+this.h]={}))[t];o?Array.isArray(o)&&ts(o):i[t]=th}}function tS(){tT.apply(this,arguments)}if(tT.prototype.toJSON=function(){return t_(this.m)},tT.prototype.toString=function(){return this.m.toString()},d(tS,tT),td){var tF={};Object.defineProperties(tS,(tF[Symbol.hasInstance]=tp(function(){throw Error("Cannot perform instanceof checks for MutableMessage")}),tF))}function tC(t,e,n){if(n){var r,i={};for(r in n){var o=n[r],a=o.ha;a||(i.F=o.la||o.fa.P,o.aa?(i.U=tB(o.aa),a=function(t){return function(e,n,r){return t.F(e,n,r,t.U)}}(i)):o.ca?(i.T=tL(o.X.g,o.ca),a=function(t){return function(e,n,r){return t.F(e,n,r,t.T)}}(i)):a=i.F,o.ha=a),a(e,t,o.X),i={F:i.F,U:i.U,T:i.T}}}var u=t;if(u=u.ba){ti(e,e.g.end());for(var s=0;s<u.length;s++)ti(e,u[s])}}var tk=Symbol();function tR(t,e,n){return t[tk]||(t[tk]=function(t,r){return e(t,r,n)})}function tU(t){var e=t[tk];if(!e){var n=tK(t);e=function(t,e){return t$(t,e,n)},t[tk]=e}return e}function tP(t,e,n,r,i,o){t=t();var a=0;for(t.length&&"number"!=typeof t[0]&&(n(e,t[0]),a++);a<t.length;){n=t[a++];for(var u=a+1;u<t.length&&"number"!=typeof t[u];)u++;var s=t[a++];switch(u-=a){case 0:r(e,n,s);break;case 1:r(e,n,s,t[a++]);break;case 2:i(e,n,s,t[a++],t[a++]);break;case 3:u=t[a++];var c=t[a++],f=t[a++];Array.isArray(f)?i(e,n,s,u,c,f):o(e,n,s,u,c,f);break;case 4:o(e,n,s,t[a++],t[a++],t[a++],t[a++]);break;default:throw Error("unexpected number of binary field arguments: "+u)}}return e}var tI=Symbol();function tB(t){var e=t[tI];if(!e){var n=tV(t);e=function(t,e){return tq(t,e,n)},t[tI]=e}return e}function tL(t,e){var n=t[tI];return n||(n=function(t,n){return tC(t,n,e)},t[tI]=n),n}var tN=Symbol();function tM(t,e){t.push(e)}function tD(t,e,n){t.push(e,n.P)}function tG(t,e,n,r,i){var o=tB(i),a=n.P;t.push(e,function(t,e,n){return a(t,e,n,r,o)})}function tW(t,e,n,r,i,o){var a=tL(r,o),u=n.P;t.push(e,function(t,e,n){return u(t,e,n,r,a)})}function tV(t){return t[tN]||tP(t,t[tN]=[],tM,tD,tG,tW)}var tY=Symbol();function tH(t,e){t[0]=e}function tz(t,e,n,r){var i=n.O;t[e]=r?function(t,e,n){return i(t,e,n,r)}:i}function tX(t,e,n,r,i,o){var a=n.O,u=tU(i);t[e]=function(t,e,n){return a(t,e,n,r,u,o)}}function tJ(t,e,n,r,i,o,a){var u=n.O,s=tR(r,i,o);t[e]=function(t,e,n){return u(t,e,n,r,s,a)}}function tK(t){return t[tY]||tP(t,t[tY]={},tH,tz,tX,tJ)}function t$(t,n,r){for(;te(n)&&4!=n.h;){var i=n.i,o=r[i];if(!o){var a=r[0];a&&(a=a[i])&&(o=r[i]=function(t){var e,n=(e=t.aa)?tU(e):(e=t.ka)?tR(t.X.g,e,t.ca):void 0,r=t.X,i=t.fa.O;return n?function(t,e){return i(t,e,r,n)}:function(t,e){return i(t,e,r)}}(a))}if((!o||!o(n,t,i))&&(o=n,i=t,a=o.j,function t(e){switch(e.h){case 0:if(0!=e.h)t(e);else t:{e=e.g;for(var n=e.g,r=n+10;n<r;)if(0==(128&e.h[n++])){e.g=n,K(e);break t}throw z()}break;case 1:e=e.g,e.g+=8,K(e);break;case 2:2!=e.h?t(e):(n=$(e.g)>>>0,e=e.g,e.g+=n,K(e));break;case 5:e=e.g,e.g+=4,K(e);break;case 3:for(n=e.i;;){if(!te(e))throw Error("Unmatched start-group tag: stream EOF");if(4==e.h){if(e.i!=n)throw Error("Unmatched end-group tag");break}t(e)}break;default:throw H(e.h,e.j)}}(o),!o.W)){var u=o.g.h;o=a===(o=o.g.g)?e||(e=new Uint8Array(0)):W?u.slice(a,o):new Uint8Array(u.subarray(a,o)),(a=i.ba)?a.push(o):i.ba=[o]}}return t}function tZ(t,e,n){if(tn.length){var r=tn.pop();t&&(J(r.g,t),r.i=-1,r.h=-1),t=r}else t=new tt(t);try{return t$(new e,t,tK(n))}finally{(e=t.g).h=null,e.j=0,e.i=0,e.g=0,e.v=!1,t.i=-1,t.h=-1,100>tn.length&&tn.push(t)}}function tq(t,e,n){for(var r=n.length,i=1==r%2,o=+!!i;o<r;o+=2)(0,n[o+1])(e,t,n[o]);tC(t,e,i?n[0]:void 0)}function tQ(t,e){var n=new tr;tq(t,n,tV(e)),ti(n,n.g.end()),t=new Uint8Array(n.h),e=n.i;for(var r=e.length,i=0,o=0;o<r;o++){var a=e[o];t.set(a,i),i+=a.length}return n.i=[t],t}function t2(t,e){return{O:t,P:e}}var t1=t2(function(t,e,n){if(5!==t.h)return!1;var r=(t=t.g).h[t.g],i=t.h[t.g+1],o=t.h[t.g+2],a=t.h[t.g+3];return t.g+=4,K(t),t=2*((i=(0|r|i<<8|o<<16|a<<24)>>>0)>>31)+1,r=i>>>23&255,i&=8388607,ty(e,n,255==r?i?NaN:1/0*t:0==r?1401298464324817e-60*t*i:t*Math.pow(2,r-150)*(i+8388608)),!0},function(t,e,n){if(null!=(e=tv(e,n))){Q(t.g,8*n+5),t=t.g;var r=e;0===(r=(n=+(0>r))?-r:r)?0<1/r?V=Y=0:(Y=0,V=0x80000000):isNaN(r)?(Y=0,V=0x7fffffff):34028234663852886e22<r?(Y=0,V=(n<<31|0x7f800000)>>>0):11754943508222875e-54>r?(Y=0,V=(n<<31|(r=Math.round(r/1401298464324817e-60)))>>>0):(e=Math.floor(Math.log(r)/Math.LN2),r*=Math.pow(2,-e),0x1000000<=(r=Math.round(8388608*r))&&++e,Y=0,V=(n<<31|e+127<<23|8388607&r)>>>0),n=V,t.g.push(n>>>0&255),t.g.push(n>>>8&255),t.g.push(n>>>16&255),t.g.push(n>>>24&255)}}),t3=t2(function(t,e,n){if(0!==t.h)return!1;for(var r=t.g,i=128,o=0,a=t=0;4>a&&128<=i;a++)i=r.h[r.g++],K(r),o|=(127&i)<<7*a;if(128<=i&&(i=r.h[r.g++],K(r),o|=(127&i)<<28,t|=(127&i)>>4),128<=i)for(a=0;5>a&&128<=i;a++)i=r.h[r.g++],K(r),t|=(127&i)<<7*a+3;if(128>i)r=o>>>0,(t=0x80000000&(i=t>>>0))&&(i=~i>>>0,0==(r=~r+1>>>0)&&(i=i+1>>>0)),r=0x100000000*i+(r>>>0);else throw z();return ty(e,n,t?-r:r),!0},function(t,e,n){if(null!=(e=tv(e,n))&&null!=e){Q(t.g,8*n),t=t.g;var r=e;for(n=0>r,e=(r=Math.abs(r))>>>0,r=Math.floor((r-e)/0x100000000)>>>0,n&&(r=~r>>>0,0xffffffff<(e=(~e>>>0)+1)&&(e=0,0xffffffff<++r&&(r=0))),V=e,Y=r,n=V,e=Y;0<e||127<n;)t.g.push(127&n|128),n=(n>>>7|e<<25)>>>0,e>>>=7;t.g.push(n)}}),t4=t2(function(t,e,n){return 0===t.h&&(ty(e,n,$(t.g)),!0)},function(t,e,n){if(null!=(e=tv(e,n))&&null!=e)if(Q(t.g,8*n),t=t.g,0<=(n=e))Q(t,n);else{for(e=0;9>e;e++)t.g.push(127&n|128),n>>=7;t.g.push(1)}}),t0=t2(function(t,e,n){if(2!==t.h)return!1;var r=$(t.g)>>>0,i=(t=t.g).g;if(t.g+=r,K(t),t=t.h,R)(o=C)||(o=C=new TextDecoder("utf-8",{fatal:!0})),o=o.decode(t.subarray(i,i+r));else{r=i+r;for(var o,a,u,s,c=[],f=null;i<r;)128>(a=t[i++])?c.push(a):224>a?i>=r?T():(u=t[i++],194>a||128!=(192&u)?(i--,T()):c.push((31&a)<<6|63&u)):240>a?i>=r-1?T():128!=(192&(u=t[i++]))||224===a&&160>u||237===a&&160<=u||128!=(192&(o=t[i++]))?(i--,T()):c.push((15&a)<<12|(63&u)<<6|63&o):244>=a?i>=r-2?T():128!=(192&(u=t[i++]))||0!=(a<<28)+(u-144)>>30||128!=(192&(o=t[i++]))||128!=(192&(s=t[i++]))?(i--,T()):(a=((7&a)<<18|(63&u)<<12|(63&o)<<6|63&s)-65536,c.push((a>>10&1023)+55296,(1023&a)+56320)):T(),8192<=c.length&&(f=S(f,c),c.length=0);o=S(f,c)}return ty(e,n,o),!0},function(t,e,n){if(null!=(e=tv(e,n))){if(U){0;e=(k||(k=new TextEncoder)).encode(e)}else{for(var r=0,i=new Uint8Array(3*e.length),o=0;o<e.length;o++){var a=e.charCodeAt(o);if(128>a)i[r++]=a;else{if(2048>a)i[r++]=a>>6|192;else{if(55296<=a&&57343>=a){if(56319>=a&&o<e.length){var u=e.charCodeAt(++o);if(56320<=u&&57343>=u){a=1024*(a-55296)+u-56320+65536,i[r++]=a>>18|240,i[r++]=a>>12&63|128,i[r++]=a>>6&63|128,i[r++]=63&a|128;continue}o--}0;a=65533}i[r++]=a>>12|224,i[r++]=a>>6&63|128}i[r++]=63&a|128}}e=i.subarray(0,r)}Q(t.g,8*n+2),Q(t.g,e.length),ti(t,t.g.end()),ti(t,e)}}),t6=t2(function(t,e,n,r,i){if(2!==t.h)return!1;e=tA(e,n,r),n=t.g.i,r=$(t.g)>>>0;var o=t.g.g+r,a=o-n;if(0>=a&&(t.g.i=o,i(e,t),a=o-t.g.g),a)throw Error("Message parsing ended unexpectedly. Expected to read "+(r+" bytes, instead read ")+(r-a)+" bytes, either the data ended unexpectedly or the message misreported its own length");return t.g.g=o,t.g.i=n,!0},function(t,e,n,r,i){if(null!=(e=tw(e,r,n)))for(r=0;r<e.length;r++){var o=t;Q(o.g,8*n+2);var a=o.g.end();ti(o,a),a.push(o.h),o=a,i(e[r],t),a=t;var u=o.pop();for(u=a.h+a.g.length()-u;127<u;)o.push(127&u|128),u>>>=7,a.h++;o.push(u),a.h++}});function t5(){tS.apply(this,arguments)}if(d(t5,tS),td){var t7={};Object.defineProperties(t5,(t7[Symbol.hasInstance]=tp(Object[Symbol.hasInstance]),t7))}function t8(t){t5.call(this,t)}function t9(){return[1,t4,2,t1,3,t0,4,t0]}function et(t){t5.call(this,t,-1,en)}function ee(){return[1,t6,t8,t9]}d(t8,t5),d(et,t5),et.prototype.addClassification=function(t,e){return tA(this,1,t8,t,e),this};var en=[1];function er(t){t5.call(this,t)}function ei(){return[1,t1,2,t1,3,t1,4,t1,5,t1]}function eo(t){t5.call(this,t,-1,eu)}function ea(){return[1,t6,er,ei]}d(er,t5),d(eo,t5);var eu=[1];function es(t){t5.call(this,t)}function ec(){return[1,t1,2,t1,3,t1,4,t1,5,t1,6,t3]}d(es,t5);var ef=[[61,146],[146,91],[91,181],[181,84],[84,17],[17,314],[314,405],[405,321],[321,375],[375,291],[61,185],[185,40],[40,39],[39,37],[37,0],[0,267],[267,269],[269,270],[270,409],[409,291],[78,95],[95,88],[88,178],[178,87],[87,14],[14,317],[317,402],[402,318],[318,324],[324,308],[78,191],[191,80],[80,81],[81,82],[82,13],[13,312],[312,311],[311,310],[310,415],[415,308]],el=[[263,249],[249,390],[390,373],[373,374],[374,380],[380,381],[381,382],[382,362],[263,466],[466,388],[388,387],[387,386],[386,385],[385,384],[384,398],[398,362]],eh=[[276,283],[283,282],[282,295],[295,285],[300,293],[293,334],[334,296],[296,336]],eg=[[33,7],[7,163],[163,144],[144,145],[145,153],[153,154],[154,155],[155,133],[33,246],[246,161],[161,160],[160,159],[159,158],[158,157],[157,173],[173,133]],ed=[[46,53],[53,52],[52,65],[65,55],[70,63],[63,105],[105,66],[66,107]],ep=[[10,338],[338,297],[297,332],[332,284],[284,251],[251,389],[389,356],[356,454],[454,323],[323,361],[361,288],[288,397],[397,365],[365,379],[379,378],[378,400],[400,377],[377,152],[152,148],[148,176],[176,149],[149,150],[150,136],[136,172],[172,58],[58,132],[132,93],[93,234],[234,127],[127,162],[162,21],[21,54],[54,103],[103,67],[67,109],[109,10]],ev=[].concat(s(ef),s(el),s(eh),s(eg),s(ed),s(ep));function ey(t,e,n){if(n=t.createShader(0===n?t.VERTEX_SHADER:t.FRAGMENT_SHADER),t.shaderSource(n,e),t.compileShader(n),!t.getShaderParameter(n,t.COMPILE_STATUS))throw Error("Could not compile WebGL shader.\n\n"+t.getShaderInfoLog(n));return n}function eb(t){return{x:tm(t,1),y:tm(t,2),z:tm(t,3),visibility:null!=tv(t,4)?tm(t,4):void 0}}function em(t,e){this.h=t,this.g=e,this.l=0}function ew(t,e,n){return(function(t,e){var n=t.g;if(void 0===t.o){var r=ey(n,"\n  attribute vec2 aVertex;\n  attribute vec2 aTex;\n  varying vec2 vTex;\n  void main(void) {\n    gl_Position = vec4(aVertex, 0.0, 1.0);\n    vTex = aTex;\n  }",0),i=ey(n,"\n  precision mediump float;\n  varying vec2 vTex;\n  uniform sampler2D sampler0;\n  void main(){\n    gl_FragColor = texture2D(sampler0, vTex);\n  }",1),o=n.createProgram();if(n.attachShader(o,r),n.attachShader(o,i),n.linkProgram(o),!n.getProgramParameter(o,n.LINK_STATUS))throw Error("Could not compile WebGL program.\n\n"+n.getProgramInfoLog(o));r=t.o=o,n.useProgram(r),i=n.getUniformLocation(r,"sampler0"),t.j={K:n.getAttribLocation(r,"aVertex"),J:n.getAttribLocation(r,"aTex"),ma:i},t.u=n.createBuffer(),n.bindBuffer(n.ARRAY_BUFFER,t.u),n.enableVertexAttribArray(t.j.K),n.vertexAttribPointer(t.j.K,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),n.STATIC_DRAW),n.bindBuffer(n.ARRAY_BUFFER,null),t.s=n.createBuffer(),n.bindBuffer(n.ARRAY_BUFFER,t.s),n.enableVertexAttribArray(t.j.J),n.vertexAttribPointer(t.j.J,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array([0,1,0,0,1,0,1,1]),n.STATIC_DRAW),n.bindBuffer(n.ARRAY_BUFFER,null),n.uniform1i(i,0)}r=t.j,n.useProgram(t.o),n.canvas.width=e.width,n.canvas.height=e.height,n.viewport(0,0,e.width,e.height),n.activeTexture(n.TEXTURE0),t.h.bindTexture2d(e.glName),n.enableVertexAttribArray(r.K),n.bindBuffer(n.ARRAY_BUFFER,t.u),n.vertexAttribPointer(r.K,2,n.FLOAT,!1,0,0),n.enableVertexAttribArray(r.J),n.bindBuffer(n.ARRAY_BUFFER,t.s),n.vertexAttribPointer(r.J,2,n.FLOAT,!1,0,0),n.bindFramebuffer(n.DRAW_FRAMEBUFFER?n.DRAW_FRAMEBUFFER:n.FRAMEBUFFER,null),n.clearColor(0,0,0,0),n.clear(n.COLOR_BUFFER_BIT),n.colorMask(!0,!0,!0,!0),n.drawArrays(n.TRIANGLE_FAN,0,4),n.disableVertexAttribArray(r.K),n.disableVertexAttribArray(r.J),n.bindBuffer(n.ARRAY_BUFFER,null),t.h.bindTexture2d(0)}(t,e),"function"==typeof t.g.canvas.transferToImageBitmap)?Promise.resolve(t.g.canvas.transferToImageBitmap()):n?Promise.resolve(t.g.canvas):"function"==typeof createImageBitmap?createImageBitmap(t.g.canvas):(void 0===t.i&&(t.i=document.createElement("canvas")),new Promise(function(e){t.i.height=t.g.canvas.height,t.i.width=t.g.canvas.width,t.i.getContext("2d",{}).drawImage(t.g.canvas,0,0,t.g.canvas.width,t.g.canvas.height),e(t.i)}))}function eA(t){this.g=t}var eE=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,9,1,7,0,65,0,253,15,26,11]);function ex(t,e){return e+t}function e_(t){if(this.g=t,this.listeners={},this.j={},this.H={},this.o={},this.u={},this.I=this.s=this.Z=!0,this.D=Promise.resolve(),this.Y="",this.C={},this.locateFile=t&&t.locateFile||ex,"object"==typeof window)var e=window.location.pathname.toString().substring(0,window.location.pathname.toString().lastIndexOf("/"))+"/";else if("undefined"!=typeof location)e=location.pathname.toString().substring(0,location.pathname.toString().lastIndexOf("/"))+"/";else throw Error("solutions can only be loaded on a web page or in a web worker");if(this.$=e,t.options){e=u(Object.keys(t.options));for(var n=e.next();!n.done;n=e.next()){n=n.value;var r=t.options[n].default;void 0!==r&&(this.j[n]="function"==typeof r?r():r)}}}function ej(t,e){var n;return x(function(r){return e in t.H?r.return(t.H[e]):(n=fetch(t.locateFile(e,"")).then(function(t){return t.arrayBuffer()}),t.H[e]=n,r.return(n))})}function eO(t){var e=this;t=t||{};var n={url:"face_detection_short.binarypb"},r={type:1,graphOptionXref:{calculatorType:"TensorsToDetectionsCalculator",calculatorName:"facedetectionshortrangegpu__facedetectionshortrangecommon__TensorsToDetectionsCalculator",fieldName:"min_score_thresh"}};this.g=new e_({locateFile:t.locateFile,files:[{data:!0,url:"face_detection_short.binarypb"},{data:!0,url:"face_detection_short_range.tflite"},{simd:!0,url:"face_detection_solution_simd_wasm_bin.js"},{simd:!1,url:"face_detection_solution_wasm_bin.js"}],graph:n,listeners:[{wants:["detections","image_transformed"],outs:{image:"image_transformed",detections:{type:"detection_list",stream:"detections"}}}],inputs:{image:{type:"video",stream:"input_frames_gpu"}},options:{useCpuInference:{type:0,graphOptionXref:{calculatorType:"InferenceCalculator",fieldName:"use_cpu_inference"},default:"object"==typeof window&&void 0!==window.navigator&&("iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod".split(";").includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document)},selfieMode:{type:0,graphOptionXref:{calculatorType:"GlScalerCalculator",calculatorIndex:1,fieldName:"flip_horizontal"}},model:{type:0,onChange:function(t){var i,o,a,s,c;return x(function(f){switch(f.g){case 1:o=(i=u("short"===t?["face_detection_short_range.tflite"]:["face_detection_full_range_sparse.tflite"])).next();case 2:if(o.done){f.g=4;break}return s="third_party/mediapipe/modules/face_detection/"+(a=o.value),b(f,ej(e.g,a),5);case 5:c=f.h,e.g.overrideFile(s,c),o=i.next(),f.g=2;break;case 4:return n.url="short"===t?"face_detection_short.binarypb":"face_detection_full.binarypb",r.graphOptionXref.calculatorName="short"===t?"facedetectionshortrangegpu__facedetectionshortrangecommon__TensorsToDetectionsCalculator":"facedetectionfullrangegpu__facedetectionfullrangecommon__TensorsToDetectionsCalculator",f.return(!0)}})}},minDetectionConfidence:r}})}(c=e_.prototype).close=function(){return this.i&&this.i.delete(),Promise.resolve()},c.reset=function(){var t=this;return x(function(e){t.i&&(t.i.reset(),t.o={},t.u={}),e.g=0})},c.setOptions=function(t,e){var n=this;if(e=e||this.g.options){for(var r=[],i=[],o={},a=u(Object.keys(t)),s=a.next();!s.done;o={R:o.R,S:o.S},s=a.next()){var c=s.value;c in this.j&&this.j[c]===t[c]||(this.j[c]=t[c],void 0!==(s=e[c])&&(s.onChange&&(o.R=s.onChange,o.S=t[c],r.push(function(t){return function(){return x(function(e){if(1==e.g)return b(e,t.R(t.S),2);!0===e.h&&(n.s=!0),e.g=0})}}(o))),s.graphOptionXref&&(c={valueNumber:1===s.type?t[c]:0,valueBoolean:0===s.type&&t[c],valueString:2===s.type?t[c]:""},s=Object.assign(Object.assign(Object.assign({},{calculatorName:"",calculatorIndex:0}),s.graphOptionXref),c),i.push(s))))}(0!==r.length||0!==i.length)&&(this.s=!0,this.B=(void 0===this.B?[]:this.B).concat(i),this.A=(void 0===this.A?[]:this.A).concat(r))}},c.initialize=function(){var t=this;return x(function(e){var n,r,i,o,a,c,f,l,h,g,d,p,v,y,m,w,A,E,_,j,O,T,S,F;return 1==e.g?b(e,x(function(e){switch(e.g){case 1:if(!t.Z)return e.return();return n=void 0===t.g.files?[]:"function"==typeof t.g.files?t.g.files(t.j):t.g.files,b(e,x(function(t){switch(t.g){case 1:return t.o=2,b(t,WebAssembly.instantiate(eE),4);case 4:t.g=3,t.o=0;break;case 2:return t.o=0,t.j=null,t.return(!1);case 3:return t.return(!0)}}),2);case 2:if(r=e.h,"object"==typeof window){var u,p;return u={locateFile:t.locateFile},window.createMediapipeSolutionsWasm=u,p={locateFile:t.locateFile},window.createMediapipeSolutionsPackedAssets=p,c=n.filter(function(t){return void 0!==t.data}),f=n.filter(function(t){return void 0===t.data}),l=Promise.all(c.map(function(e){var n=ej(t,e.url);if(void 0!==e.path){var r=e.path;n=n.then(function(e){return t.overrideFile(r,e),Promise.resolve(e)})}return n})),h=Promise.all(f.map(function(e){var n,i;return void 0===e.simd||e.simd&&r||!e.simd&&!r?(n=t.locateFile(e.url,t.$),(i=document.createElement("script")).setAttribute("src",n),i.setAttribute("crossorigin","anonymous"),new Promise(function(t){i.addEventListener("load",function(){t()},!1),i.addEventListener("error",function(){t()},!1),document.body.appendChild(i)})):Promise.resolve()})).then(function(){var e,n,r;return x(function(i){if(1==i.g)return e=window.createMediapipeSolutionsWasm,n=window.createMediapipeSolutionsPackedAssets,r=t,b(i,e(n),2);r.h=i.h,i.g=0})}),g=x(function(e){return t.g.graph&&t.g.graph.url?e=b(e,ej(t,t.g.graph.url),0):(e.g=0,e=void 0),e}),b(e,Promise.all([h,l,g]),7)}if("function"!=typeof importScripts)throw Error("solutions can only be loaded on a web page or in a web worker");return i=n.filter(function(t){return void 0===t.simd||t.simd&&r||!t.simd&&!r}).map(function(e){return t.locateFile(e.url,t.$)}),importScripts.apply(null,s(i)),o=t,b(e,createMediapipeSolutionsWasm(Module),6);case 6:o.h=e.h,t.l=new OffscreenCanvas(1,1),t.h.canvas=t.l,a=t.h.GL.createContext(t.l,{antialias:!1,alpha:!1,ja:"undefined"!=typeof WebGL2RenderingContext?2:1}),t.h.GL.makeContextCurrent(a),e.g=4;break;case 7:if(t.l=document.createElement("canvas"),!(d=t.l.getContext("webgl2",{}))&&!(d=t.l.getContext("webgl",{})))return alert("Failed to create WebGL canvas context when passing video frame."),e.return();t.G=d,t.h.canvas=t.l,t.h.createContext(t.l,!0,!0,{});case 4:t.i=new t.h.SolutionWasm,t.Z=!1,e.g=0}}),2):3!=e.g?b(e,x(function(e){if(1==e.g){if(t.g.graph&&t.g.graph.url&&t.Y===t.g.graph.url)return e.return();if(t.s=!0,!t.g.graph||!t.g.graph.url){e.g=2;return}return t.Y=t.g.graph.url,b(e,ej(t,t.g.graph.url),3)}for(2!=e.g&&(p=e.h,t.i.loadGraph(p)),y=(v=u(Object.keys(t.C))).next();!y.done;y=v.next())m=y.value,t.i.overrideFile(m,t.C[m]);if(t.C={},t.g.listeners)for(A=(w=u(t.g.listeners)).next();!A.done;A=w.next())!function(t,e){for(var n=e.name||"$",r=[].concat(s(e.wants)),i=new t.h.StringList,o=u(e.wants),a=o.next();!a.done;a=o.next())i.push_back(a.value);o=t.h.PacketListener.implement({onResults:function(i){for(var o,a,s,c,f,l,h,g,d,p,v,y,m,w,A={},E=0;E<e.wants.length;++E)A[r[E]]=i.get(E);var _=t.listeners[n];_&&(t.D=(o=e.outs,x(function(e){switch(e.g){case 1:if(!o)return e.return(A);for(a={},s=0,f=(c=u(Object.keys(o))).next();!f.done;f=c.next())"string"!=typeof(l=o[f.value])&&"texture"===l.type&&void 0!==A[l.stream]&&++s;1<s&&(t.I=!1),f=(h=u(Object.keys(o))).next();case 2:if(f.done){e.g=4;break}if("string"==typeof(d=o[g=f.value]))return m=a,w=g,b(e,function(t,e,n){var r;return x(function(i){return"number"==typeof n||n instanceof Uint8Array||n instanceof t.h.Uint8BlobList?i.return(n):n instanceof t.h.Texture2dDataOut?((r=t.u[e])||(r=new em(t.h,t.G),t.u[e]=r),i.return(ew(r,n,t.I))):i.return(void 0)})}(t,g,A[d]),14);if(p=A[d.stream],"detection_list"===d.type){if(p){for(var n=p.getRectList(),r=p.getLandmarksList(),i=p.getClassificationsList(),E=[],_=0;_<n.size();++_){var j=tZ(n.get(_),es,ec);j={boundingBox:{xCenter:tm(j,1),yCenter:tm(j,2),height:tm(j,3),width:tm(j,4),rotation:tm(j,5,0),rectId:tE(j,6)},landmarks:tw(tZ(r.get(_),eo,ea),er,1).map(eb),V:tw(tZ(i.get(_),et,ee),t8,1).map(function(t){return{index:tE(t,1),ga:tm(t,2),label:null!=tv(t,3)?tx(t,3):void 0,displayName:null!=tv(t,4)?tx(t,4):void 0}})},E.push(j)}n=E}else n=[];a[g]=n,e.g=7;break}if("proto_list"===d.type){if(p){for(r=0,n=Array(p.size());r<p.size();r++)n[r]=p.get(r);p.delete()}else n=[];a[g]=n,e.g=7;break}if(void 0===p){e.g=3;break}if("float_list"===d.type||"proto"===d.type){a[g]=p,e.g=7;break}if("texture"!==d.type)throw Error("Unknown output config type: '"+d.type+"'");return(v=t.u[g])||(v=new em(t.h,t.G),t.u[g]=v),b(e,ew(v,p,t.I),13);case 13:y=e.h,a[g]=y;case 7:d.transform&&a[g]&&(a[g]=d.transform(a[g])),e.g=3;break;case 14:m[w]=e.h;case 3:f=h.next(),e.g=2;break;case 4:return e.return(a)}})).then(function(n){n=_(n);for(var i=0;i<e.wants.length;++i){var o=A[r[i]];"object"==typeof o&&o.hasOwnProperty&&o.hasOwnProperty("delete")&&o.delete()}n&&(t.D=n)}))}}),t.i.attachMultiListener(i,o),i.delete()}(t,A.value);E=t.j,t.j={},t.setOptions(E),e.g=0}),3):b(e,x(function(e){switch(e.g){case 1:if(!t.s)return e.return();if(!t.A){e.g=2;break}j=(_=u(t.A)).next();case 3:if(j.done){e.g=5;break}return b(e,(0,j.value)(),4);case 4:j=_.next(),e.g=3;break;case 5:t.A=void 0;case 2:if(t.B){for(O=new t.h.GraphOptionChangeRequestList,S=(T=u(t.B)).next();!S.done;S=T.next())F=S.value,O.push_back(F);t.i.changeOptions(O),O.delete(),t.B=void 0}t.s=!1,e.g=0}}),0)})},c.overrideFile=function(t,e){this.i?this.i.overrideFile(t,e):this.C[t]=e},c.clearOverriddenFiles=function(){this.C={},this.i&&this.i.clearOverriddenFiles()},c.send=function(t,e){var n,r,i,o,a,s,c,f,l,h=this;return x(function(g){switch(g.g){case 1:if(!h.g.inputs)return g.return();return n=1e3*(null==e?performance.now():e),b(g,h.D,2);case 2:return b(g,h.initialize(),3);case 3:for(r=new h.h.PacketDataList,o=(i=u(Object.keys(t))).next();!o.done;o=i.next())if(a=o.value,s=h.g.inputs[a]){t:{var d=t[a];switch(s.type){case"video":var p=h.o[s.stream];if(p||(p=new em(h.h,h.G),h.o[s.stream]=p),0===p.l&&(p.l=p.h.createTexture()),"undefined"!=typeof HTMLVideoElement&&d instanceof HTMLVideoElement)var v=d.videoWidth,y=d.videoHeight;else"undefined"!=typeof HTMLImageElement&&d instanceof HTMLImageElement?(v=d.naturalWidth,y=d.naturalHeight):(v=d.width,y=d.height);y={glName:p.l,width:v,height:y},(v=p.g).canvas.width=y.width,v.canvas.height=y.height,v.activeTexture(v.TEXTURE0),p.h.bindTexture2d(p.l),v.texImage2D(v.TEXTURE_2D,0,v.RGBA,v.RGBA,v.UNSIGNED_BYTE,d),p.h.bindTexture2d(0),p=y;break t;case"detections":for((p=h.o[s.stream])||(p=new eA(h.h),h.o[s.stream]=p),p.data||(p.data=new p.g.DetectionListData),p.data.reset(d.length),y=0;y<d.length;++y){v=d[y];var m=p.data,w=m.setBoundingBox,A=y,E=v.boundingBox,x=new es;if(ty(x,1,E.xCenter),ty(x,2,E.yCenter),ty(x,3,E.height),ty(x,4,E.width),ty(x,5,E.rotation),ty(x,6,E.rectId),E=tQ(x,ec),w.call(m,A,E),v.landmarks)for(m=0;m<v.landmarks.length;++m){var _=!!(x=v.landmarks[m]).visibility;A=(w=p.data).addNormalizedLandmark,E=y,x=Object.assign(Object.assign({},x),{visibility:_?x.visibility:0}),ty(_=new er,1,x.x),ty(_,2,x.y),ty(_,3,x.z),x.visibility&&ty(_,4,x.visibility),x=tQ(_,ei),A.call(w,E,x)}if(v.V)for(m=0;m<v.V.length;++m)A=(w=p.data).addClassification,E=y,x=v.V[m],ty(_=new t8,2,x.ga),x.index&&ty(_,1,x.index),x.label&&ty(_,3,x.label),x.displayName&&ty(_,4,x.displayName),x=tQ(_,t9),A.call(w,E,x)}p=p.data;break t;default:p={}}}switch(c=p,f=s.stream,s.type){case"video":r.pushTexture2d(Object.assign(Object.assign({},c),{stream:f,timestamp:n}));break;case"detections":(l=c).stream=f,l.timestamp=n,r.pushDetectionList(l);break;default:throw Error("Unknown input config type: '"+s.type+"'")}}return h.i.send(r),b(g,h.D,4);case 4:r.delete(),g.g=0}})},c.onResults=function(t,e){this.listeners[e||"$"]=t},O("Solution",e_),O("OptionType",{BOOL:0,NUMBER:1,ia:2,0:"BOOL",1:"NUMBER",2:"STRING"}),(c=eO.prototype).close=function(){return this.g.close(),Promise.resolve()},c.onResults=function(t){this.g.onResults(t)},c.initialize=function(){var t=this;return x(function(e){return b(e,t.g.initialize(),0)})},c.reset=function(){this.g.reset()},c.send=function(t){var e=this;return x(function(n){return b(n,e.g.send(t),0)})},c.setOptions=function(t){this.g.setOptions(t)},O("FaceDetection",eO),O("FACEDETECTION_LIPS",ef),O("FACEDETECTION_LEFT_EYE",el),O("FACEDETECTION_LEFT_EYEBROW",eh),O("FACEDETECTION_RIGHT_EYE",eg),O("FACEDETECTION_RIGHT_EYEBROW",ed),O("FACEDETECTION_FACE_OVAL",ep),O("FACEDETECTION_CONTOURS",ev),O("FACEDETECTION_TESSELATION",[[127,34],[34,139],[139,127],[11,0],[0,37],[37,11],[232,231],[231,120],[120,232],[72,37],[37,39],[39,72],[128,121],[121,47],[47,128],[232,121],[121,128],[128,232],[104,69],[69,67],[67,104],[175,171],[171,148],[148,175],[118,50],[50,101],[101,118],[73,39],[39,40],[40,73],[9,151],[151,108],[108,9],[48,115],[115,131],[131,48],[194,204],[204,211],[211,194],[74,40],[40,185],[185,74],[80,42],[42,183],[183,80],[40,92],[92,186],[186,40],[230,229],[229,118],[118,230],[202,212],[212,214],[214,202],[83,18],[18,17],[17,83],[76,61],[61,146],[146,76],[160,29],[29,30],[30,160],[56,157],[157,173],[173,56],[106,204],[204,194],[194,106],[135,214],[214,192],[192,135],[203,165],[165,98],[98,203],[21,71],[71,68],[68,21],[51,45],[45,4],[4,51],[144,24],[24,23],[23,144],[77,146],[146,91],[91,77],[205,50],[50,187],[187,205],[201,200],[200,18],[18,201],[91,106],[106,182],[182,91],[90,91],[91,181],[181,90],[85,84],[84,17],[17,85],[206,203],[203,36],[36,206],[148,171],[171,140],[140,148],[92,40],[40,39],[39,92],[193,189],[189,244],[244,193],[159,158],[158,28],[28,159],[247,246],[246,161],[161,247],[236,3],[3,196],[196,236],[54,68],[68,104],[104,54],[193,168],[168,8],[8,193],[117,228],[228,31],[31,117],[189,193],[193,55],[55,189],[98,97],[97,99],[99,98],[126,47],[47,100],[100,126],[166,79],[79,218],[218,166],[155,154],[154,26],[26,155],[209,49],[49,131],[131,209],[135,136],[136,150],[150,135],[47,126],[126,217],[217,47],[223,52],[52,53],[53,223],[45,51],[51,134],[134,45],[211,170],[170,140],[140,211],[67,69],[69,108],[108,67],[43,106],[106,91],[91,43],[230,119],[119,120],[120,230],[226,130],[130,247],[247,226],[63,53],[53,52],[52,63],[238,20],[20,242],[242,238],[46,70],[70,156],[156,46],[78,62],[62,96],[96,78],[46,53],[53,63],[63,46],[143,34],[34,227],[227,143],[123,117],[117,111],[111,123],[44,125],[125,19],[19,44],[236,134],[134,51],[51,236],[216,206],[206,205],[205,216],[154,153],[153,22],[22,154],[39,37],[37,167],[167,39],[200,201],[201,208],[208,200],[36,142],[142,100],[100,36],[57,212],[212,202],[202,57],[20,60],[60,99],[99,20],[28,158],[158,157],[157,28],[35,226],[226,113],[113,35],[160,159],[159,27],[27,160],[204,202],[202,210],[210,204],[113,225],[225,46],[46,113],[43,202],[202,204],[204,43],[62,76],[76,77],[77,62],[137,123],[123,116],[116,137],[41,38],[38,72],[72,41],[203,129],[129,142],[142,203],[64,98],[98,240],[240,64],[49,102],[102,64],[64,49],[41,73],[73,74],[74,41],[212,216],[216,207],[207,212],[42,74],[74,184],[184,42],[169,170],[170,211],[211,169],[170,149],[149,176],[176,170],[105,66],[66,69],[69,105],[122,6],[6,168],[168,122],[123,147],[147,187],[187,123],[96,77],[77,90],[90,96],[65,55],[55,107],[107,65],[89,90],[90,180],[180,89],[101,100],[100,120],[120,101],[63,105],[105,104],[104,63],[93,137],[137,227],[227,93],[15,86],[86,85],[85,15],[129,102],[102,49],[49,129],[14,87],[87,86],[86,14],[55,8],[8,9],[9,55],[100,47],[47,121],[121,100],[145,23],[23,22],[22,145],[88,89],[89,179],[179,88],[6,122],[122,196],[196,6],[88,95],[95,96],[96,88],[138,172],[172,136],[136,138],[215,58],[58,172],[172,215],[115,48],[48,219],[219,115],[42,80],[80,81],[81,42],[195,3],[3,51],[51,195],[43,146],[146,61],[61,43],[171,175],[175,199],[199,171],[81,82],[82,38],[38,81],[53,46],[46,225],[225,53],[144,163],[163,110],[110,144],[52,65],[65,66],[66,52],[229,228],[228,117],[117,229],[34,127],[127,234],[234,34],[107,108],[108,69],[69,107],[109,108],[108,151],[151,109],[48,64],[64,235],[235,48],[62,78],[78,191],[191,62],[129,209],[209,126],[126,129],[111,35],[35,143],[143,111],[117,123],[123,50],[50,117],[222,65],[65,52],[52,222],[19,125],[125,141],[141,19],[221,55],[55,65],[65,221],[3,195],[195,197],[197,3],[25,7],[7,33],[33,25],[220,237],[237,44],[44,220],[70,71],[71,139],[139,70],[122,193],[193,245],[245,122],[247,130],[130,33],[33,247],[71,21],[21,162],[162,71],[170,169],[169,150],[150,170],[188,174],[174,196],[196,188],[216,186],[186,92],[92,216],[2,97],[97,167],[167,2],[141,125],[125,241],[241,141],[164,167],[167,37],[37,164],[72,38],[38,12],[12,72],[38,82],[82,13],[13,38],[63,68],[68,71],[71,63],[226,35],[35,111],[111,226],[101,50],[50,205],[205,101],[206,92],[92,165],[165,206],[209,198],[198,217],[217,209],[165,167],[167,97],[97,165],[220,115],[115,218],[218,220],[133,112],[112,243],[243,133],[239,238],[238,241],[241,239],[214,135],[135,169],[169,214],[190,173],[173,133],[133,190],[171,208],[208,32],[32,171],[125,44],[44,237],[237,125],[86,87],[87,178],[178,86],[85,86],[86,179],[179,85],[84,85],[85,180],[180,84],[83,84],[84,181],[181,83],[201,83],[83,182],[182,201],[137,93],[93,132],[132,137],[76,62],[62,183],[183,76],[61,76],[76,184],[184,61],[57,61],[61,185],[185,57],[212,57],[57,186],[186,212],[214,207],[207,187],[187,214],[34,143],[143,156],[156,34],[79,239],[239,237],[237,79],[123,137],[137,177],[177,123],[44,1],[1,4],[4,44],[201,194],[194,32],[32,201],[64,102],[102,129],[129,64],[213,215],[215,138],[138,213],[59,166],[166,219],[219,59],[242,99],[99,97],[97,242],[2,94],[94,141],[141,2],[75,59],[59,235],[235,75],[24,110],[110,228],[228,24],[25,130],[130,226],[226,25],[23,24],[24,229],[229,23],[22,23],[23,230],[230,22],[26,22],[22,231],[231,26],[112,26],[26,232],[232,112],[189,190],[190,243],[243,189],[221,56],[56,190],[190,221],[28,56],[56,221],[221,28],[27,28],[28,222],[222,27],[29,27],[27,223],[223,29],[30,29],[29,224],[224,30],[247,30],[30,225],[225,247],[238,79],[79,20],[20,238],[166,59],[59,75],[75,166],[60,75],[75,240],[240,60],[147,177],[177,215],[215,147],[20,79],[79,166],[166,20],[187,147],[147,213],[213,187],[112,233],[233,244],[244,112],[233,128],[128,245],[245,233],[128,114],[114,188],[188,128],[114,217],[217,174],[174,114],[131,115],[115,220],[220,131],[217,198],[198,236],[236,217],[198,131],[131,134],[134,198],[177,132],[132,58],[58,177],[143,35],[35,124],[124,143],[110,163],[163,7],[7,110],[228,110],[110,25],[25,228],[356,389],[389,368],[368,356],[11,302],[302,267],[267,11],[452,350],[350,349],[349,452],[302,303],[303,269],[269,302],[357,343],[343,277],[277,357],[452,453],[453,357],[357,452],[333,332],[332,297],[297,333],[175,152],[152,377],[377,175],[347,348],[348,330],[330,347],[303,304],[304,270],[270,303],[9,336],[336,337],[337,9],[278,279],[279,360],[360,278],[418,262],[262,431],[431,418],[304,408],[408,409],[409,304],[310,415],[415,407],[407,310],[270,409],[409,410],[410,270],[450,348],[348,347],[347,450],[422,430],[430,434],[434,422],[313,314],[314,17],[17,313],[306,307],[307,375],[375,306],[387,388],[388,260],[260,387],[286,414],[414,398],[398,286],[335,406],[406,418],[418,335],[364,367],[367,416],[416,364],[423,358],[358,327],[327,423],[251,284],[284,298],[298,251],[281,5],[5,4],[4,281],[373,374],[374,253],[253,373],[307,320],[320,321],[321,307],[425,427],[427,411],[411,425],[421,313],[313,18],[18,421],[321,405],[405,406],[406,321],[320,404],[404,405],[405,320],[315,16],[16,17],[17,315],[426,425],[425,266],[266,426],[377,400],[400,369],[369,377],[322,391],[391,269],[269,322],[417,465],[465,464],[464,417],[386,257],[257,258],[258,386],[466,260],[260,388],[388,466],[456,399],[399,419],[419,456],[284,332],[332,333],[333,284],[417,285],[285,8],[8,417],[346,340],[340,261],[261,346],[413,441],[441,285],[285,413],[327,460],[460,328],[328,327],[355,371],[371,329],[329,355],[392,439],[439,438],[438,392],[382,341],[341,256],[256,382],[429,420],[420,360],[360,429],[364,394],[394,379],[379,364],[277,343],[343,437],[437,277],[443,444],[444,283],[283,443],[275,440],[440,363],[363,275],[431,262],[262,369],[369,431],[297,338],[338,337],[337,297],[273,375],[375,321],[321,273],[450,451],[451,349],[349,450],[446,342],[342,467],[467,446],[293,334],[334,282],[282,293],[458,461],[461,462],[462,458],[276,353],[353,383],[383,276],[308,324],[324,325],[325,308],[276,300],[300,293],[293,276],[372,345],[345,447],[447,372],[352,345],[345,340],[340,352],[274,1],[1,19],[19,274],[456,248],[248,281],[281,456],[436,427],[427,425],[425,436],[381,256],[256,252],[252,381],[269,391],[391,393],[393,269],[200,199],[199,428],[428,200],[266,330],[330,329],[329,266],[287,273],[273,422],[422,287],[250,462],[462,328],[328,250],[258,286],[286,384],[384,258],[265,353],[353,342],[342,265],[387,259],[259,257],[257,387],[424,431],[431,430],[430,424],[342,353],[353,276],[276,342],[273,335],[335,424],[424,273],[292,325],[325,307],[307,292],[366,447],[447,345],[345,366],[271,303],[303,302],[302,271],[423,266],[266,371],[371,423],[294,455],[455,460],[460,294],[279,278],[278,294],[294,279],[271,272],[272,304],[304,271],[432,434],[434,427],[427,432],[272,407],[407,408],[408,272],[394,430],[430,431],[431,394],[395,369],[369,400],[400,395],[334,333],[333,299],[299,334],[351,417],[417,168],[168,351],[352,280],[280,411],[411,352],[325,319],[319,320],[320,325],[295,296],[296,336],[336,295],[319,403],[403,404],[404,319],[330,348],[348,349],[349,330],[293,298],[298,333],[333,293],[323,454],[454,447],[447,323],[15,16],[16,315],[315,15],[358,429],[429,279],[279,358],[14,15],[15,316],[316,14],[285,336],[336,9],[9,285],[329,349],[349,350],[350,329],[374,380],[380,252],[252,374],[318,402],[402,403],[403,318],[6,197],[197,419],[419,6],[318,319],[319,325],[325,318],[367,364],[364,365],[365,367],[435,367],[367,397],[397,435],[344,438],[438,439],[439,344],[272,271],[271,311],[311,272],[195,5],[5,281],[281,195],[273,287],[287,291],[291,273],[396,428],[428,199],[199,396],[311,271],[271,268],[268,311],[283,444],[444,445],[445,283],[373,254],[254,339],[339,373],[282,334],[334,296],[296,282],[449,347],[347,346],[346,449],[264,447],[447,454],[454,264],[336,296],[296,299],[299,336],[338,10],[10,151],[151,338],[278,439],[439,455],[455,278],[292,407],[407,415],[415,292],[358,371],[371,355],[355,358],[340,345],[345,372],[372,340],[346,347],[347,280],[280,346],[442,443],[443,282],[282,442],[19,94],[94,370],[370,19],[441,442],[442,295],[295,441],[248,419],[419,197],[197,248],[263,255],[255,359],[359,263],[440,275],[275,274],[274,440],[300,383],[383,368],[368,300],[351,412],[412,465],[465,351],[263,467],[467,466],[466,263],[301,368],[368,389],[389,301],[395,378],[378,379],[379,395],[412,351],[351,419],[419,412],[436,426],[426,322],[322,436],[2,164],[164,393],[393,2],[370,462],[462,461],[461,370],[164,0],[0,267],[267,164],[302,11],[11,12],[12,302],[268,12],[12,13],[13,268],[293,300],[300,301],[301,293],[446,261],[261,340],[340,446],[330,266],[266,425],[425,330],[426,423],[423,391],[391,426],[429,355],[355,437],[437,429],[391,327],[327,326],[326,391],[440,457],[457,438],[438,440],[341,382],[382,362],[362,341],[459,457],[457,461],[461,459],[434,430],[430,394],[394,434],[414,463],[463,362],[362,414],[396,369],[369,262],[262,396],[354,461],[461,457],[457,354],[316,403],[403,402],[402,316],[315,404],[404,403],[403,315],[314,405],[405,404],[404,314],[313,406],[406,405],[405,313],[421,418],[418,406],[406,421],[366,401],[401,361],[361,366],[306,408],[408,407],[407,306],[291,409],[409,408],[408,291],[287,410],[410,409],[409,287],[432,436],[436,410],[410,432],[434,416],[416,411],[411,434],[264,368],[368,383],[383,264],[309,438],[438,457],[457,309],[352,376],[376,401],[401,352],[274,275],[275,4],[4,274],[421,428],[428,262],[262,421],[294,327],[327,358],[358,294],[433,416],[416,367],[367,433],[289,455],[455,439],[439,289],[462,370],[370,326],[326,462],[2,326],[326,370],[370,2],[305,460],[460,455],[455,305],[254,449],[449,448],[448,254],[255,261],[261,446],[446,255],[253,450],[450,449],[449,253],[252,451],[451,450],[450,252],[256,452],[452,451],[451,256],[341,453],[453,452],[452,341],[413,464],[464,463],[463,413],[441,413],[413,414],[414,441],[258,442],[442,441],[441,258],[257,443],[443,442],[442,257],[259,444],[444,443],[443,259],[260,445],[445,444],[444,260],[467,342],[342,445],[445,467],[459,458],[458,250],[250,459],[289,392],[392,290],[290,289],[290,328],[328,460],[460,290],[376,433],[433,435],[435,376],[250,290],[290,392],[392,250],[411,416],[416,433],[433,411],[341,463],[463,464],[464,341],[453,464],[464,465],[465,453],[357,465],[465,412],[412,357],[343,412],[412,399],[399,343],[360,363],[363,440],[440,360],[437,399],[399,456],[456,437],[420,456],[456,363],[363,420],[401,435],[435,288],[288,401],[372,383],[383,353],[353,372],[339,255],[255,249],[249,339],[448,261],[261,255],[255,448],[133,243],[243,190],[190,133],[133,155],[155,112],[112,133],[33,246],[246,247],[247,33],[33,130],[130,25],[25,33],[398,384],[384,286],[286,398],[362,398],[398,414],[414,362],[362,463],[463,341],[341,362],[263,359],[359,467],[467,263],[263,249],[249,255],[255,263],[466,467],[467,260],[260,466],[75,60],[60,166],[166,75],[238,239],[239,79],[79,238],[162,127],[127,139],[139,162],[72,11],[11,37],[37,72],[121,232],[232,120],[120,121],[73,72],[72,39],[39,73],[114,128],[128,47],[47,114],[233,232],[232,128],[128,233],[103,104],[104,67],[67,103],[152,175],[175,148],[148,152],[119,118],[118,101],[101,119],[74,73],[73,40],[40,74],[107,9],[9,108],[108,107],[49,48],[48,131],[131,49],[32,194],[194,211],[211,32],[184,74],[74,185],[185,184],[191,80],[80,183],[183,191],[185,40],[40,186],[186,185],[119,230],[230,118],[118,119],[210,202],[202,214],[214,210],[84,83],[83,17],[17,84],[77,76],[76,146],[146,77],[161,160],[160,30],[30,161],[190,56],[56,173],[173,190],[182,106],[106,194],[194,182],[138,135],[135,192],[192,138],[129,203],[203,98],[98,129],[54,21],[21,68],[68,54],[5,51],[51,4],[4,5],[145,144],[144,23],[23,145],[90,77],[77,91],[91,90],[207,205],[205,187],[187,207],[83,201],[201,18],[18,83],[181,91],[91,182],[182,181],[180,90],[90,181],[181,180],[16,85],[85,17],[17,16],[205,206],[206,36],[36,205],[176,148],[148,140],[140,176],[165,92],[92,39],[39,165],[245,193],[193,244],[244,245],[27,159],[159,28],[28,27],[30,247],[247,161],[161,30],[174,236],[236,196],[196,174],[103,54],[54,104],[104,103],[55,193],[193,8],[8,55],[111,117],[117,31],[31,111],[221,189],[189,55],[55,221],[240,98],[98,99],[99,240],[142,126],[126,100],[100,142],[219,166],[166,218],[218,219],[112,155],[155,26],[26,112],[198,209],[209,131],[131,198],[169,135],[135,150],[150,169],[114,47],[47,217],[217,114],[224,223],[223,53],[53,224],[220,45],[45,134],[134,220],[32,211],[211,140],[140,32],[109,67],[67,108],[108,109],[146,43],[43,91],[91,146],[231,230],[230,120],[120,231],[113,226],[226,247],[247,113],[105,63],[63,52],[52,105],[241,238],[238,242],[242,241],[124,46],[46,156],[156,124],[95,78],[78,96],[96,95],[70,46],[46,63],[63,70],[116,143],[143,227],[227,116],[116,123],[123,111],[111,116],[1,44],[44,19],[19,1],[3,236],[236,51],[51,3],[207,216],[216,205],[205,207],[26,154],[154,22],[22,26],[165,39],[39,167],[167,165],[199,200],[200,208],[208,199],[101,36],[36,100],[100,101],[43,57],[57,202],[202,43],[242,20],[20,99],[99,242],[56,28],[28,157],[157,56],[124,35],[35,113],[113,124],[29,160],[160,27],[27,29],[211,204],[204,210],[210,211],[124,113],[113,46],[46,124],[106,43],[43,204],[204,106],[96,62],[62,77],[77,96],[227,137],[137,116],[116,227],[73,41],[41,72],[72,73],[36,203],[203,142],[142,36],[235,64],[64,240],[240,235],[48,49],[49,64],[64,48],[42,41],[41,74],[74,42],[214,212],[212,207],[207,214],[183,42],[42,184],[184,183],[210,169],[169,211],[211,210],[140,170],[170,176],[176,140],[104,105],[105,69],[69,104],[193,122],[122,168],[168,193],[50,123],[123,187],[187,50],[89,96],[96,90],[90,89],[66,65],[65,107],[107,66],[179,89],[89,180],[180,179],[119,101],[101,120],[120,119],[68,63],[63,104],[104,68],[234,93],[93,227],[227,234],[16,15],[15,85],[85,16],[209,129],[129,49],[49,209],[15,14],[14,86],[86,15],[107,55],[55,9],[9,107],[120,100],[100,121],[121,120],[153,145],[145,22],[22,153],[178,88],[88,179],[179,178],[197,6],[6,196],[196,197],[89,88],[88,96],[96,89],[135,138],[138,136],[136,135],[138,215],[215,172],[172,138],[218,115],[115,219],[219,218],[41,42],[42,81],[81,41],[5,195],[195,51],[51,5],[57,43],[43,61],[61,57],[208,171],[171,199],[199,208],[41,81],[81,38],[38,41],[224,53],[53,225],[225,224],[24,144],[144,110],[110,24],[105,52],[52,66],[66,105],[118,229],[229,117],[117,118],[227,34],[34,234],[234,227],[66,107],[107,69],[69,66],[10,109],[109,151],[151,10],[219,48],[48,235],[235,219],[183,62],[62,191],[191,183],[142,129],[129,126],[126,142],[116,111],[111,143],[143,116],[118,117],[117,50],[50,118],[223,222],[222,52],[52,223],[94,19],[19,141],[141,94],[222,221],[221,65],[65,222],[196,3],[3,197],[197,196],[45,220],[220,44],[44,45],[156,70],[70,139],[139,156],[188,122],[122,245],[245,188],[139,71],[71,162],[162,139],[149,170],[170,150],[150,149],[122,188],[188,196],[196,122],[206,216],[216,92],[92,206],[164,2],[2,167],[167,164],[242,141],[141,241],[241,242],[0,164],[164,37],[37,0],[11,72],[72,12],[12,11],[12,38],[38,13],[13,12],[70,63],[63,71],[71,70],[31,226],[226,111],[111,31],[36,101],[101,205],[205,36],[203,206],[206,165],[165,203],[126,209],[209,217],[217,126],[98,165],[165,97],[97,98],[237,220],[220,218],[218,237],[237,239],[239,241],[241,237],[210,214],[214,169],[169,210],[140,171],[171,32],[32,140],[241,125],[125,237],[237,241],[179,86],[86,178],[178,179],[180,85],[85,179],[179,180],[181,84],[84,180],[180,181],[182,83],[83,181],[181,182],[194,201],[201,182],[182,194],[177,137],[137,132],[132,177],[184,76],[76,183],[183,184],[185,61],[61,184],[184,185],[186,57],[57,185],[185,186],[216,212],[212,186],[186,216],[192,214],[214,187],[187,192],[139,34],[34,156],[156,139],[218,79],[79,237],[237,218],[147,123],[123,177],[177,147],[45,44],[44,4],[4,45],[208,201],[201,32],[32,208],[98,64],[64,129],[129,98],[192,213],[213,138],[138,192],[235,59],[59,219],[219,235],[141,242],[242,97],[97,141],[97,2],[2,141],[141,97],[240,75],[75,235],[235,240],[229,24],[24,228],[228,229],[31,25],[25,226],[226,31],[230,23],[23,229],[229,230],[231,22],[22,230],[230,231],[232,26],[26,231],[231,232],[233,112],[112,232],[232,233],[244,189],[189,243],[243,244],[189,221],[221,190],[190,189],[222,28],[28,221],[221,222],[223,27],[27,222],[222,223],[224,29],[29,223],[223,224],[225,30],[30,224],[224,225],[113,247],[247,225],[225,113],[99,60],[60,240],[240,99],[213,147],[147,215],[215,213],[60,20],[20,166],[166,60],[192,187],[187,213],[213,192],[243,112],[112,244],[244,243],[244,233],[233,245],[245,244],[245,128],[128,188],[188,245],[188,114],[114,174],[174,188],[134,131],[131,220],[220,134],[174,217],[217,236],[236,174],[236,198],[198,134],[134,236],[215,177],[177,58],[58,215],[156,143],[143,124],[124,156],[25,110],[110,7],[7,25],[31,228],[228,25],[25,31],[264,356],[356,368],[368,264],[0,11],[11,267],[267,0],[451,452],[452,349],[349,451],[267,302],[302,269],[269,267],[350,357],[357,277],[277,350],[350,452],[452,357],[357,350],[299,333],[333,297],[297,299],[396,175],[175,377],[377,396],[280,347],[347,330],[330,280],[269,303],[303,270],[270,269],[151,9],[9,337],[337,151],[344,278],[278,360],[360,344],[424,418],[418,431],[431,424],[270,304],[304,409],[409,270],[272,310],[310,407],[407,272],[322,270],[270,410],[410,322],[449,450],[450,347],[347,449],[432,422],[422,434],[434,432],[18,313],[313,17],[17,18],[291,306],[306,375],[375,291],[259,387],[387,260],[260,259],[424,335],[335,418],[418,424],[434,364],[364,416],[416,434],[391,423],[423,327],[327,391],[301,251],[251,298],[298,301],[275,281],[281,4],[4,275],[254,373],[373,253],[253,254],[375,307],[307,321],[321,375],[280,425],[425,411],[411,280],[200,421],[421,18],[18,200],[335,321],[321,406],[406,335],[321,320],[320,405],[405,321],[314,315],[315,17],[17,314],[423,426],[426,266],[266,423],[396,377],[377,369],[369,396],[270,322],[322,269],[269,270],[413,417],[417,464],[464,413],[385,386],[386,258],[258,385],[248,456],[456,419],[419,248],[298,284],[284,333],[333,298],[168,417],[417,8],[8,168],[448,346],[346,261],[261,448],[417,413],[413,285],[285,417],[326,327],[327,328],[328,326],[277,355],[355,329],[329,277],[309,392],[392,438],[438,309],[381,382],[382,256],[256,381],[279,429],[429,360],[360,279],[365,364],[364,379],[379,365],[355,277],[277,437],[437,355],[282,443],[443,283],[283,282],[281,275],[275,363],[363,281],[395,431],[431,369],[369,395],[299,297],[297,337],[337,299],[335,273],[273,321],[321,335],[348,450],[450,349],[349,348],[359,446],[446,467],[467,359],[283,293],[293,282],[282,283],[250,458],[458,462],[462,250],[300,276],[276,383],[383,300],[292,308],[308,325],[325,292],[283,276],[276,293],[293,283],[264,372],[372,447],[447,264],[346,352],[352,340],[340,346],[354,274],[274,19],[19,354],[363,456],[456,281],[281,363],[426,436],[436,425],[425,426],[380,381],[381,252],[252,380],[267,269],[269,393],[393,267],[421,200],[200,428],[428,421],[371,266],[266,329],[329,371],[432,287],[287,422],[422,432],[290,250],[250,328],[328,290],[385,258],[258,384],[384,385],[446,265],[265,342],[342,446],[386,387],[387,257],[257,386],[422,424],[424,430],[430,422],[445,342],[342,276],[276,445],[422,273],[273,424],[424,422],[306,292],[292,307],[307,306],[352,366],[366,345],[345,352],[268,271],[271,302],[302,268],[358,423],[423,371],[371,358],[327,294],[294,460],[460,327],[331,279],[279,294],[294,331],[303,271],[271,304],[304,303],[436,432],[432,427],[427,436],[304,272],[272,408],[408,304],[395,394],[394,431],[431,395],[378,395],[395,400],[400,378],[296,334],[334,299],[299,296],[6,351],[351,168],[168,6],[376,352],[352,411],[411,376],[307,325],[325,320],[320,307],[285,295],[295,336],[336,285],[320,319],[319,404],[404,320],[329,330],[330,349],[349,329],[334,293],[293,333],[333,334],[366,323],[323,447],[447,366],[316,15],[15,315],[315,316],[331,358],[358,279],[279,331],[317,14],[14,316],[316,317],[8,285],[285,9],[9,8],[277,329],[329,350],[350,277],[253,374],[374,252],[252,253],[319,318],[318,403],[403,319],[351,6],[6,419],[419,351],[324,318],[318,325],[325,324],[397,367],[367,365],[365,397],[288,435],[435,397],[397,288],[278,344],[344,439],[439,278],[310,272],[272,311],[311,310],[248,195],[195,281],[281,248],[375,273],[273,291],[291,375],[175,396],[396,199],[199,175],[312,311],[311,268],[268,312],[276,283],[283,445],[445,276],[390,373],[373,339],[339,390],[295,282],[282,296],[296,295],[448,449],[449,346],[346,448],[356,264],[264,454],[454,356],[337,336],[336,299],[299,337],[337,338],[338,151],[151,337],[294,278],[278,455],[455,294],[308,292],[292,415],[415,308],[429,358],[358,355],[355,429],[265,340],[340,372],[372,265],[352,346],[346,280],[280,352],[295,442],[442,282],[282,295],[354,19],[19,370],[370,354],[285,441],[441,295],[295,285],[195,248],[248,197],[197,195],[457,440],[440,274],[274,457],[301,300],[300,368],[368,301],[417,351],[351,465],[465,417],[251,301],[301,389],[389,251],[394,395],[395,379],[379,394],[399,412],[412,419],[419,399],[410,436],[436,322],[322,410],[326,2],[2,393],[393,326],[354,370],[370,461],[461,354],[393,164],[164,267],[267,393],[268,302],[302,12],[12,268],[312,268],[268,13],[13,312],[298,293],[293,301],[301,298],[265,446],[446,340],[340,265],[280,330],[330,425],[425,280],[322,426],[426,391],[391,322],[420,429],[429,437],[437,420],[393,391],[391,326],[326,393],[344,440],[440,438],[438,344],[458,459],[459,461],[461,458],[364,434],[434,394],[394,364],[428,396],[396,262],[262,428],[274,354],[354,457],[457,274],[317,316],[316,402],[402,317],[316,315],[315,403],[403,316],[315,314],[314,404],[404,315],[314,313],[313,405],[405,314],[313,421],[421,406],[406,313],[323,366],[366,361],[361,323],[292,306],[306,407],[407,292],[306,291],[291,408],[408,306],[291,287],[287,409],[409,291],[287,432],[432,410],[410,287],[427,434],[434,411],[411,427],[372,264],[264,383],[383,372],[459,309],[309,457],[457,459],[366,352],[352,401],[401,366],[1,274],[274,4],[4,1],[418,421],[421,262],[262,418],[331,294],[294,358],[358,331],[435,433],[433,367],[367,435],[392,289],[289,439],[439,392],[328,462],[462,326],[326,328],[94,2],[2,370],[370,94],[289,305],[305,455],[455,289],[339,254],[254,448],[448,339],[359,255],[255,446],[446,359],[254,253],[253,449],[449,254],[253,252],[252,450],[450,253],[252,256],[256,451],[451,252],[256,341],[341,452],[452,256],[414,413],[413,463],[463,414],[286,441],[441,414],[414,286],[286,258],[258,441],[441,286],[258,257],[257,442],[442,258],[257,259],[259,443],[443,257],[259,260],[260,444],[444,259],[260,467],[467,445],[445,260],[309,459],[459,250],[250,309],[305,289],[289,290],[290,305],[305,290],[290,460],[460,305],[401,376],[376,435],[435,401],[309,250],[250,392],[392,309],[376,411],[411,433],[433,376],[453,341],[341,464],[464,453],[357,453],[453,465],[465,357],[343,357],[357,412],[412,343],[437,343],[343,399],[399,437],[344,360],[360,440],[440,344],[420,437],[437,456],[456,420],[360,420],[420,363],[363,360],[361,401],[401,288],[288,361],[265,372],[372,353],[353,265],[390,339],[339,249],[249,390],[339,448],[448,255],[255,339]]),O("VERSION","0.4.1646425229")}).call(this)}}]);