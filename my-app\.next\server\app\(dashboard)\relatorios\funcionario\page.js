(()=>{var a={};a.id=2823,a.ids=[2823],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16893:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,99796)),Promise.resolve().then(c.bind(c,98316))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},28582:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["relatorios",{children:["funcionario",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,97627)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\funcionario\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,57675)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,85389)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\app\\(dashboard)\\relatorios\\funcionario\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/relatorios/funcionario/page",pathname:"/relatorios/funcionario",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/relatorios/funcionario/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:a=>{"use strict";a.exports=require("path")},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41862:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},46749:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,97818)),Promise.resolve().then(c.bind(c,58570))},51465:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88971:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},97627:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m,metadata:()=>l});var d=c(37413),e=c(61120),f=c(97818),g=c(51465),h=c(88971),i=c(75243),j=c(4536),k=c.n(j);let l={title:"Relat\xf3rio Individual - RLPONTO",description:"Gerar relat\xf3rio individual de funcion\xe1rio"};function m(){let a={type:"funcionario",title:"Relat\xf3rio Individual",description:"Gere um relat\xf3rio detalhado de um funcion\xe1rio espec\xedfico",color:"blue",fields:[{name:"funcionario",label:"Funcion\xe1rio",type:"select",required:!0,placeholder:"Selecione um funcion\xe1rio",options:[{value:"EMP001",label:"Jo\xe3o Silva Santos - EMP001"},{value:"EMP002",label:"Maria Oliveira Costa - EMP002"},{value:"EMP003",label:"Carlos Roberto Lima - EMP003"},{value:"EMP004",label:"Ana Paula Silva - EMP004"},{value:"EMP005",label:"Pedro Henrique Souza - EMP005"},{value:"EMP006",label:"Juliana Ferreira Alves - EMP006"},{value:"EMP007",label:"Roberto Carlos Mendes - EMP007"},{value:"EMP008",label:"Fernanda Lima Santos - EMP008"}]},{name:"periodo",label:"Per\xedodo",type:"daterange",required:!0,defaultValue:{start:new Date(new Date().getFullYear(),new Date().getMonth(),1),end:new Date}},{name:"incluirGraficos",label:"Incluir Gr\xe1ficos",type:"checkbox",defaultValue:!0,description:"Adicionar gr\xe1ficos de frequ\xeancia e horas trabalhadas"},{name:"incluirDetalhes",label:"Incluir Detalhes Di\xe1rios",type:"checkbox",defaultValue:!1,description:"Mostrar registros detalhados dia a dia"},{name:"incluirHorasExtras",label:"Incluir Horas Extras",type:"checkbox",defaultValue:!0,description:"Adicionar se\xe7\xe3o de horas extras"},{name:"formato",label:"Formato de Sa\xedda",type:"radio",required:!0,defaultValue:"PDF",options:[{value:"PDF",label:"PDF",description:"Melhor para visualiza\xe7\xe3o e impress\xe3o"},{value:"Excel",label:"Excel",description:"Melhor para an\xe1lise de dados"},{value:"CSV",label:"CSV",description:"Para importa\xe7\xe3o em outros sistemas"}]}],sections:[{title:"Dados B\xe1sicos",description:"Informa\xe7\xf5es pessoais e profissionais do funcion\xe1rio",included:!0},{title:"Resumo do Per\xedodo",description:"Totalizadores de horas, frequ\xeancia e estat\xedsticas",included:!0},{title:"Registros de Ponto",description:"Hist\xf3rico detalhado de entradas e sa\xeddas",included:!0},{title:"An\xe1lise de Frequ\xeancia",description:"Gr\xe1ficos e an\xe1lises de presen\xe7a",included:!0,optional:!0},{title:"Horas Extras",description:"Detalhamento de horas extras trabalhadas",included:!0,optional:!0},{title:"Observa\xe7\xf5es",description:"Registros manuais e justificativas",included:!1,optional:!0}]};return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(k(),{href:"/relatorios",children:(0,d.jsxs)(i.$n,{variant:"outline",size:"sm",children:[(0,d.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Voltar"]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-600 rounded-lg",children:(0,d.jsx)(h.A,{className:"h-8 w-8 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Relat\xf3rio Individual"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Configure e gere relat\xf3rio de funcion\xe1rio espec\xedfico"})]})]})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(n,{}),children:(0,d.jsx)(f.ReportForm,{config:a})})})]})})})}function n(){return(0,d.jsxs)("div",{className:"p-6 space-y-6",children:[(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,d.jsx)("div",{className:"h-10 bg-gray-200 rounded"})]},b))}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,d.jsx)("div",{className:"h-24 bg-gray-200 rounded"})]}),(0,d.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32"})]})}},97818:(a,b,c)=>{"use strict";c.d(b,{ReportForm:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ReportForm() from the server but ReportForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\01projeto\\my-app\\src\\components\\relatorios\\report-form.tsx","ReportForm")},99796:(a,b,c)=>{"use strict";c.d(b,{ReportForm:()=>n});var d=c(60687),e=c(43210),f=c(42613),g=c(84027),h=c(5336),i=c(40228),j=c(13861),k=c(41862),l=c(31158),m=c(10022);function n({config:a}){let[b,c]=(0,e.useState)(()=>{let b={};return a.fields.forEach(a=>{void 0!==a.defaultValue&&(b[a.name]=a.defaultValue)}),b}),[n,o]=(0,e.useState)(!1),[p,q]=(0,e.useState)(!1),[r,s]=(0,e.useState)({}),t=(a,b)=>{c(c=>({...c,[a]:b})),r[a]&&s(b=>({...b,[a]:""}))},u=async c=>{if((()=>{let c={};return a.fields.forEach(a=>{a.required&&!b[a.name]&&(c[a.name]=`${a.label} \xe9 obrigat\xf3rio`)}),s(c),0===Object.keys(c).length})()){o(!0);try{await new Promise(a=>setTimeout(a,3e3)),"preview"===c?q(!0):(console.log("Gerando relat\xf3rio:",{config:a.type,data:b}),alert("Relat\xf3rio gerado com sucesso! O download iniciar\xe1 em breve."))}catch(a){console.error("Erro ao gerar relat\xf3rio:",a)}finally{o(!1)}}};return(0,d.jsxs)("div",{className:"p-6 space-y-6",children:[(0,d.jsxs)("div",{className:"border-b border-gray-200 pb-4",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600",children:a.description})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,d.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:a.fields.map(a=>(0,d.jsxs)("div",{className:"radio"===a.type?"md:col-span-2":"",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[a.label,a.required&&(0,d.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(a=>{switch(a.type){case"select":return(0,d.jsxs)("select",{value:b[a.name]||"",onChange:b=>t(a.name,b.target.value),className:`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${r[a.name]?"border-red-500":"border-gray-300"}`,children:[(0,d.jsx)("option",{value:"",children:a.placeholder}),a.options?.map(a=>(0,d.jsx)("option",{value:a.value,children:a.label},a.value))]});case"checkbox":return(0,d.jsxs)("label",{className:"flex items-start space-x-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:b[a.name]||!1,onChange:b=>t(a.name,b.target.checked),className:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-900",children:a.label}),a.description&&(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.description})]})]});case"radio":return(0,d.jsx)("div",{className:"space-y-3",children:a.options?.map(c=>(0,d.jsxs)("label",{className:"flex items-start space-x-3",children:[(0,d.jsx)("input",{type:"radio",name:a.name,value:c.value,checked:b[a.name]===c.value,onChange:b=>t(a.name,b.target.value),className:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-900",children:c.label}),c.description&&(0,d.jsx)("p",{className:"text-sm text-gray-600",children:c.description})]})]},c.value))});case"daterange":return(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm text-gray-600 mb-1",children:"Data Inicial"}),(0,d.jsx)("input",{type:"date",value:b[a.name]?.start?.toISOString().split("T")[0]||"",onChange:c=>t(a.name,{...b[a.name],start:new Date(c.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm text-gray-600 mb-1",children:"Data Final"}),(0,d.jsx)("input",{type:"date",value:b[a.name]?.end?.toISOString().split("T")[0]||"",onChange:c=>t(a.name,{...b[a.name],end:new Date(c.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]});default:return(0,d.jsx)(f.pd,{type:a.type,placeholder:a.placeholder,value:b[a.name]||"",onChange:b=>t(a.name,b.target.value),error:r[a.name]})}})(a),r[a.name]&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r[a.name]})]},a.name))})}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(g.A,{className:"h-5 w-5 text-gray-600"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Se\xe7\xf5es do Relat\xf3rio"})]}),(0,d.jsx)("div",{className:"space-y-3",children:a.sections.map((a,b)=>(0,d.jsx)("div",{className:"p-3 border border-gray-200 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:a.title}),(0,d.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:a.description})]}),(0,d.jsx)("div",{className:"ml-3",children:a.included?(0,d.jsx)(h.A,{className:"h-4 w-4 text-green-600"}):(0,d.jsx)("div",{className:"h-4 w-4 border border-gray-300 rounded"})})]})},b))})]})]}),(0,d.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 inline mr-1"}),"Tempo estimado: 2-3 minutos"]}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsxs)(f.$n,{variant:"outline",onClick:()=>u("preview"),disabled:n,children:[(0,d.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Visualizar"]}),(0,d.jsx)(f.$n,{variant:"primary",onClick:()=>u("generate"),disabled:n,children:n?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Gerando..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Gerar Relat\xf3rio"]})})]})]})}),p&&(0,d.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,d.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,d.jsx)(m.A,{className:"h-5 w-5 text-blue-600"}),(0,d.jsx)("h4",{className:"font-medium text-blue-900",children:"Preview do Relat\xf3rio"})]}),(0,d.jsx)("p",{className:"text-sm text-blue-700",children:'O relat\xf3rio ser\xe1 gerado com os par\xe2metros selecionados. Clique em "Gerar Relat\xf3rio" para fazer o download.'})]})})]})}}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,3327,4556,1684,2121],()=>b(b.s=28582));module.exports=c})();